
### 交换长期token



### 
GET https://graph.facebook.com/v22.0/me/accounts?fields=id,name,access_token,category,tasks,picture&access_token=EAATncSlEAFcBOyZCmwCm90lS9KZCA5PqpjDRw4lMynte0N16Ptch7XTay5kvO2ZAYm0jczCjQ0YgVUiI3eXZBM22Ao4K0khm44riPJxNIDgQ5ZBSlX037EYDcY1ZB6keFpRV3IZAxykoRkXfaZC3ZABgZCHMVvIBMBT0hSCLj61SFZBJpWe8hdwdgFDnjluHnWVqguv

###
GET https://graph.facebook.com/v22.0/***************/feed?fields=id,message,created_time,from,full_picture,comments{id,message,from,created_time}&access_token=EAATncSlEAFcBO5LgNy4kGfqvbNr4A5mZAkQrREzkhjEmqjMQZCBXti4G0RV3DNaeNCseIl2P6ZAGAlRKOOxjRY25SQ3k1YohDFd1SVsAlZARnja6YAZBhX9ImBBXjJVsSCQCr1dc0xjCa1ZAWZCQRZAf4J6ZAoiYlMGXXt5ELFHOuQU7bh07nD0WHdRgW52xDFnvXaLjxo96F

###
GET https://www-test.dolphinradar.com/api/tdk?pageRoute=/ HTTP/1.1
content-type: application/json
tenantId:20
Current-Language:en

###

GET https://www-test.dolphinradar.com/api/landing/get?pathVal=/&preview=&lang=en&preview_id= HTTP/1.1
content-type: application/json
tenantId:20
Current-Language:en
