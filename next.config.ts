import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  output: 'standalone',
  /* config options here */
  images: {
    remotePatterns: [
      {
        hostname: 'lh3.googleusercontent.com',
      },
      {
        hostname: 'otadzyqgxmtkuphmzzar.supabase.co',
      },
      {
        hostname: 'opqwmzvikvwqadgxugqj.supabase.co',
      },
      {
        hostname: 'res-back.pumpsoul.com',
      },
      {
        hostname: 'lh3.googleusercontent.com',
      },
      {
        hostname: 'otadzyqgxmtkuphmzzar.supabase.co',
      },
      {
        hostname: 'opqwmzvikvwqadgxugqj.supabase.co',
      },
      {
        hostname: 'res-back.pumpsoul.com',
      },
      { hostname: 'mdtaxzhfiygaefngasbx.supabase.co' },
      { hostname: 'platform-lookaside.fbsbx.com' },
      { hostname: 'otadzyqgxmtkuphmzzar.supabase.co' },
      { hostname: 'opqwmzvikvwqadgxugqj.supabase.co' },
    ],
  },
};

export default withNextIntl(nextConfig);
