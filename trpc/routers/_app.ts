import { commentsRouter } from '@/modules/core/comments/server/procedures';
import { socialPageRouter } from '@/modules/core/social-page/server/procedures';
import { stripeRouter } from '@/modules/stripe/server/procedures';
import { createTRPCRouter } from '@/trpc/init';
import { userRouter } from '@/modules/user/server/procedures';
import { chatRouter } from '@/modules/chat/server/procedures';
import { socialPostRouter } from '@/modules/core/posts/server/procedures';
import { ruleRouter } from '@/modules/core/comment-rule/server/procedures';
import { dataRouter } from '@/modules/core/data/server/procedures';
export const appRouter = createTRPCRouter({
  stripe: stripeRouter,
  socialPage: socialPageRouter,
  posts: socialPostRouter,
  comments: commentsRouter,
  user: userRouter,
  chat: chatRouter,
  rule: ruleRouter,
  data: dataRouter,
});
// export type definition of API
export type AppRouter = typeof appRouter;
