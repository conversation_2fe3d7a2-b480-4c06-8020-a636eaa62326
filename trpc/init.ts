import { initTR<PERSON>, TRPCError } from '@trpc/server';
import { cache } from 'react';
import superjson from 'superjson';
import { createClient } from '@/utils/supabase/server';
import { eq } from 'drizzle-orm';
import { db } from '@/db';
import { user } from '@/db/schema';
import { logger } from '@/utils/logger';

export const createTRPCContext = cache(async () => {
  const supabase = await createClient();
  const {
    data: { user: supabaseUser },
  } = await supabase.auth.getUser();
  /**
   * @see: https://trpc.io/docs/server/context
   */
  return { uid: supabaseUser?.id };
});

export type Context = Awaited<ReturnType<typeof createTRPCContext>>;
// Avoid exporting the entire t-object
// since it's not very descriptive.
// For instance, the use of a t variable
// is common in i18n libraries.
const t = initTRPC.context<Context>().create({
  /**
   * @see https://trpc.io/docs/server/data-transformers
   */
  transformer: superjson,
});

// Logging middleware
const loggingMiddleware = t.middleware(async (opts) => {
  const input = await opts.getRawInput();
  logger.info(
    `[TRPC] [PATH: ${opts.path}] [TYPE: ${opts.type}] [UID: ${opts.ctx.uid}] [INPUT: ${JSON.stringify(
      input,
    )}]`,
  );
  return opts.next();
});

// Base router and procedure helpers
export const createTRPCRouter = t.router;
export const createCallerFactory = t.createCallerFactory;
export const baseProcedure = t.procedure.use(loggingMiddleware);

export const protectedProcedure = t.procedure
  .use(async (opts) => {
    if (!opts.ctx.uid) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.uid, opts.ctx.uid));
    if (!currentUser) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    return opts.next({ ctx: { ...opts.ctx, user: currentUser } });
  })
  .use(loggingMiddleware);
