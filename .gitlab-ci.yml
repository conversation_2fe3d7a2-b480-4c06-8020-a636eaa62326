stages:
  - build

build:
  stage: build
  image: registry-docker.rightknights.com/open/node:18.18.2-buster
  only:
    - merge_requests
  tags:
    - com
  before_script:
    - npm install --global corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - pnpm install # install dependencies
    - pnpm build
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
