NEXT_PUBLIC_APP_ENV=local
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=Commentify-Test


NEXT_PUBLIC_CLARIFY_ID=qxqlzw6xaj

NEXT_PUBLIC_SUPABASE_URL=https://mdtaxzhfiygaefngasbx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1kdGF4emhmaXlnYWVmbmdhc2J4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MTYxMDMsImV4cCI6MjA2MDI5MjEwM30.g0vtnDCKXdiB4ZPsBWX2Msxy3Y2linC38DPHmZ8SM3Q
DATABASE_URL=postgresql://postgres.mdtaxzhfiygaefngasbx:<EMAIL>:6543/postgres?pgbouncer=true
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-ZZXR94KD10

METADATA_TITLE=commentify - Smart Comment Management for Facebook & Instagram
METADATA_DESCRIPTION=Hide, snooze, and manage comments on Facebook and Instagram with AI-powered tools. Save time and improve your social media presence.

# AWS S3
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=b42936b5d78d58a0cc3e3e3a2856f6ad
AWS_SECRET_ACCESS_KEY=43c372f47e1b6c6cbae8b8cbf4bb3d685c485fb6f9005a37daa23b1caa411052
AWS_S3_BUCKET_NAME=commentify-test
AWS_ENDPOINT_URL=https://mdtaxzhfiygaefngasbx.supabase.co/storage/v1/s3
AWS_S3_PUBLIC_URL=https://mdtaxzhfiygaefngasbx.supabase.co/storage/v1/object/public/commentify-test/


# Stripe
STRIPE_SECRET_KEY=sk_test_51RWr58PwdIBE7zGPyJw0WrjJml8Ix8fsKgxXoVI537fjEX9bSHvmuK2hChCsDH5fP5wQpP2okgYVFXdErJuAwlmv00NinxIsSs
STRIPE_WEBHOOK_SECRET=whsec_52eac418e18caa7d2a2b5cc320ffe64e42ae2f2a31c24a9eb091d5a305d14ce9

# AWS Bedrock
# AWS Bedrock
AWS_BEDROCK_REGION=us-west-2
AWS_BEDROCK_ACCESS_KEY_ID=********************
AWS_BEDROCK_SECRET_KEY=eDj7H6vliP0TJrln6cUJDCgZH1bXh6ezyFN+eNGl



# Facebook Business App
FACEBOOK_APP_ID=1113592333953063
FACEBOOK_APP_SECRET=********************************
FACEBOOK_VERIFY_TOKEN=********************************

# blog
NEXT_APP_BLOG_API_HOST=https://www-test.dolphinradar.com/api
NEXT_APP_TENANTID=20



# OPENAI API KEY
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Comment MCP Server
COMMENT_MCP_SERVER_SSE=https://mcp-test.drfans.com/sse


# JOB TOKEN
JOB_TOKEN=tBuNK48qsUjLwq