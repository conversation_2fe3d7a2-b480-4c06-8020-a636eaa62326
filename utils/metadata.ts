import { Metadata } from 'next';
import { I18nProps } from '@/i18n/locale';

export function createCanonical(pathname: string) {
  return async function generateMetadata({
    params,
  }: {
    params: Promise<I18nProps>;
  }): Promise<Metadata> {
    const { locale } = await params;
    return {
      alternates: {
        canonical: locale === 'en' ? `${pathname}` : `/${locale}${pathname}`,
      },
    };
  };
}
