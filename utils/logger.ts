import pino from 'pino';
import pretty from 'pino-pretty';

const stream = pretty({
  colorize: true,
  messageFormat: '{msg}',
  customPrettifiers: {
    time: () => {
      const date = new Date();
      return date
        .toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        })
        .replace(
          /(\d+)\/(\d+)\/(\d+),\s(\d+):(\d+):(\d+)/,
          '$3-$1-$2 $4:$5:$6',
        );
    },
  },
});

export const logger = pino(
  {
    base: undefined,
  },
  stream,
);
