'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { createClient } from '@/utils/supabase/server';
import { env } from '@/env';
import { createUser, getUserByUid } from '@/modules/user/service';
import { platformConnect, PlatformConnect } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { db } from '@/db';
import { logger } from '@/utils/logger';
export async function login(formData: FormData) {
  logger.info('start login');
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  };

  logger.info(data, 'start login');

  const { error, data: user } = await supabase.auth.signInWithPassword(data);
  logger.info(data, 'Login success');
  if (error) {
    logger.error(error, 'Login error');
    return {
      status: 'error',
      message: 'login failed',
    };
  }
  logger.info(user.user, 'Login success');
  const uid = user.user.id;
  const bizUser = await getUserByUid(uid);
  const platforms = await db
    .select()
    .from(platformConnect)
    .where(eq(platformConnect.uid, uid));
  logger.info(platforms, 'platforms');
  return {
    user: user.user,
    platforms,
    bizUser,
    status: 'ok',
  };
}

export async function signup(formData: FormData) {
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const { email, password } = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  };

  const { data, error } = await supabase.auth.signUp({ email, password });

  if (error) {
    logger.error(error, 'Signup error');
    return {
      status: 'error',
      message: 'signup failed',
    };
  } else if (data.user) {
    logger.info(data.user, 'Signup success');
    const bizUser = await createUser(data.user, null);
    return {
      status: 'ok',
      user: data.user,
      message: 'signup success',
      bizUser,
    };
  }
  return {
    status: 'error',
    message: 'signup failed',
  };
}

export async function resetPassword(formData: FormData) {
  const supabase = await createClient();
  const email = formData.get('email') as string;

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${env.NEXT_PUBLIC_SITE_URL}/reset-password`,
  });

  if (error) {
    logger.error(error, 'Reset password error');
    redirect('/error');
  }

  redirect('/reset-password-sent');
}

export async function updatePassword(formData: FormData) {
  const supabase = await createClient();
  const password = formData.get('password') as string;

  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    logger.error(error, 'Update password error');
    redirect('/error');
  }

  revalidatePath('/', 'layout');
}

export async function linkWithFacebook() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.linkIdentity({
    provider: 'facebook',
    options: {
      redirectTo: `${env.NEXT_PUBLIC_SITE_URL}/api/auth/link-callback`,
      scopes:
        'email public_profile pages_show_list pages_read_engagement pages_manage_metadata pages_read_user_content pages_manage_engagement instagram_basic instagram_manage_comments business_management',
    },
  });
  if (data.url) {
    redirect(data.url);
  } else if (error) {
    logger.error(error, 'Login with Facebook error');
    redirect('/error');
  }
}

export async function unlinkPlatform(platform: PlatformConnect) {
  const supabase = await createClient();
  const { data: identities } = await supabase.auth.getUserIdentities();

  const identity = identities?.identities.find(
    (identity) =>
      identity.provider === platform.platform &&
      identity.id == platform.identityId,
  );

  if (identity) {
    // auth unlink
    await supabase.auth.unlinkIdentity(identity);
    // 删除 platformConnect
    return await db
      .delete(platformConnect)
      .where(eq(platformConnect.id, platform.id))
      .returning();
  }
  return null;
}

export async function loginWithGoogle() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${env.NEXT_PUBLIC_SITE_URL}/api/auth/callback`,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    },
  });
  if (data.url) {
    redirect(data.url);
  } else if (error) {
    logger.error(error, 'Login with Google error');
    redirect('/error');
  }
}
