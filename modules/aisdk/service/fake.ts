import { models } from '@/modules/aisdk/model-providers';
import { z } from 'zod';
import { generateObject } from 'ai';

export const fakeAccount = async () => {
  const { object } = await generateObject({
    model: models.translateModel,
    prompt: `帮助我模拟一个facebook 的账号，账号名称为经典小说方向的帐号，帐号可以带有某个经典小说  的名字相关`,
    schema: z.object({
      account: z.object({
        name: z.string().describe('the name of the account'),
      }),
    }),
  });
  return object.account;
};

export const fakePosts = async () => {
  const { object } = await generateObject({
    model: models.translateModel,
    prompt: `帮助我模拟20条 facebook 的发帖，发帖内容是五个不同的经典小说的评价`,
    schema: z.object({
      posts: z.array(
        z.object({
          content: z.string().describe('the content of the post'),
        }),
      ),
    }),
  });
  return object.posts;
};

export const fakeComments = async (postContent: string) => {
  const { object } = await generateObject({
    model: models.translateModel,
    prompt: `帮助我模拟20条 facebook 的评论，评论内容是这个帖子的内容，要求评论有正面，有负面，有中立的，帖子内容如下：${postContent}`,
    schema: z.object({
      comments: z.array(
        z.object({
          content: z.string().describe('the content of the comment'),
          sentiment: z
            .enum(['positive', 'negative', 'neutral'])
            .describe('the sentiment of the comment'),
          authorName: z.string().describe('the name of the author'),
          authorId: z
            .string()
            .describe('the id of the author, random 10 characters'),
        }),
      ),
    }),
  });
  return object.comments;
};
