import { models } from '@/modules/aisdk/model-providers';
import { z } from 'zod';
import { generateObject } from 'ai';
export const translateText = async (text: string, targetLanguage: string) => {
  const { object } = await generateObject({
    model: models.translateModel,
    prompt: `Translate the following text to ${targetLanguage}, return tranlated text only: ${text}`,
    schema: z.object({
      translatedText: z.string().describe('the translated text'),
    }),
  });
  return object.translatedText;
};
