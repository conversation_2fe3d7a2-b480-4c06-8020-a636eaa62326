import { models } from '@/modules/aisdk/model-providers';
import { logger } from '@/utils/logger';
import { generateObject } from 'ai';
import { z } from 'zod';

export const getCommentEmotion = async (commentText: string) => {
  logger.info(`comment text: ${commentText}`, 'getCommentEmotion');
  const { object } = await generateObject({
    model: models.commentModel,
    schema: z.object({
      emotion: z.enum(['positive', 'negative', 'neutral']),
      reason: z.string(),
    }),
    prompt: `
    Analyze the following comment and determine its emotional tone:
    ${commentText}
    `,
  });
  console.log(`object: ${JSON.stringify(object)}`);
  return object;
};

export const analyzeCommentIsProfanity = async (commentText: string) => {
  logger.info(`comment text: ${commentText}`, 'analyzeCommentRule');
  const { object } = await generateObject({
    model: models.commentModel,
    schema: z.object({
      isProfanity: z.boolean(),
      reason: z.string(),
    }),
    prompt: `
    Analyze the following comment and determine if it is a profanity:
    ${commentText}
    `,
  });
  return object;
};

export const analyzeCommentIsNegativity = async (commentText: string) => {
  logger.info(`comment text: ${commentText}`, 'analyzeCommentIsNegativity');
  const { object } = await generateObject({
    model: models.commentModel,
    schema: z.object({
      isNegativity: z.boolean(),
      reason: z.string(),
    }),
    prompt: `
    Analyze the following comment and determine if it is a negative comment:
    ${commentText}
    `,
  });
  return object;
};
