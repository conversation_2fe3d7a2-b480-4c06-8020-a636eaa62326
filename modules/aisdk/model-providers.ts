import { createAmazonBedrock } from '@ai-sdk/amazon-bedrock';
import { openai } from '@ai-sdk/openai';
import { env } from '@/env';
import { extractReasoningMiddleware, wrapLanguageModel } from 'ai';

export const amazonBedrock = createAmazonBedrock({
  region: env.AWS_BEDROCK_REGION,
  accessKeyId: env.AWS_BEDROCK_ACCESS_KEY_ID,
  secretAccessKey: env.AWS_BEDROCK_SECRET_KEY,
});

export const claudeHaiku = amazonBedrock(
  'us.anthropic.claude-3-5-haiku-20241022-v1:0',
);

export const claudeSonnet = amazonBedrock(
  'us.anthropic.claude-3-5-sonnet-20241022-v2:0',
);

export const claudeDeepseekR1 = wrapLanguageModel({
  model: amazonBedrock('us.deepseek.r1-v1:0'),
  middleware: extractReasoningMiddleware({ tagName: 'think' }),
});

export const models = {
  chatModel: claudeSonnet,
  advancedModel: claudeSonnet,
  titleModel: openai('gpt-4o-mini'),
  commentModel: claudeHaiku,
  reasoningModel: claudeDeepseekR1,
  translateModel: claudeHaiku,
};
