import { tool } from 'ai';
import { z } from 'zod';

export const getCommentChart = tool({
  description: '分析用户评论的价值和影响',
  parameters: z.object({
    comment: z.object({
      content: z.string().describe('评论内容'),
      userId: z.string().optional().describe('用户ID'),
      timestamp: z.string().describe('评论时间'),
    }),
  }),
  execute: async ({ comment }) => {
    // 分析评论价值
    const analysis = analyzeComment(comment.content);

    return {
      value: analysis.value,
      type: analysis.type,
      sentiment: analysis.sentiment,
      labels: ['正面', '负面', '中立'],
      values: [1, 2, 3],
      metadata: {
        analyzedAt: new Date().toISOString(),
        timestamp: new Date().toISOString(),
      },
    };
  },
});

function analyzeComment(content: string) {
  // 评论类型判断
  let type = 'normal';
  if (content.includes('广告') || content.includes('推广')) type = 'spam';
  if (content.includes('举报') || content.includes('违规')) type = 'report';
  if (content.length < 5) type = 'invalid';

  // 情感分析
  const positiveWords = ['好', '赞', '喜欢', '支持'];
  const negativeWords = ['差', '烂', '退款', '失望'];
  let sentiment = 'neutral';
  if (positiveWords.some((word) => content.includes(word)))
    sentiment = 'positive';
  if (negativeWords.some((word) => content.includes(word)))
    sentiment = 'negative';

  // 评论价值评估
  let value = 'medium';
  if (content.length > 50 && type === 'normal') value = 'high';
  if (type === 'spam' || type === 'invalid') value = 'low';

  return { type, sentiment, value };
}
