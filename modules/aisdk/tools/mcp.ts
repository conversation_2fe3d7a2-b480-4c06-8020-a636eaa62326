import { experimental_createMCPClient as createMCPClient } from 'ai';
import { env } from '@/env';
// 这里使用懒加载的方式，Docker 构建的时候才不会出问题
// 因此每次创建 mcpclient  的时候都需要使用这种方式来调用
// Create a function to initialize the MCP client lazily
export const createCommentClient = async () => {
  return createMCPClient({
    transport: {
      type: 'sse',
      url: env.COMMENT_MCP_SERVER_SSE,
    },
  });
};

// Create a lazy-loaded client
export const commentSSEClient = {
  tools: async () => {
    const client = await createCommentClient();
    return client.tools();
  },
};

export const mcpTools = {
  ...(await commentSSEClient.tools()),
};
