import { env } from '@/env';
export type Plan = {
  id: string;
  priceId: string;
  uniqueName: string;
  name: string;
  unitAmount: number;
  currency: string;
  credits: number;
  f_name: string;
  f_time: string;
  f_value: string;
};

const testPlans: Plan[] = [
  {
    id: 'free',
    priceId: 'freePriceId',
    uniqueName: 'Free',
    name: 'Free',
    unitAmount: 0,
    currency: 'usd',
    credits: 100,

    f_name: 'Free',
    f_time: '7 days',
    f_value: '0',
  },
  {
    id: 'prod_SRm2lr3dHoYJLa',
    priceId: 'price_1RWsUNPwdIBE7zGPufMKBgcq',
    uniqueName: 'Starter-Monthly',
    name: 'Starter-Monthly',
    unitAmount: 2500,
    currency: 'usd',
    credits: 10000,

    f_name: 'Starter',
    f_time: 'month',
    f_value: '25',
  },
  {
    id: 'prod_SRm3kxWzLmD1Bu',
    priceId: 'price_1RWsV0PwdIBE7zGP1TmAUbnb',
    uniqueName: 'Growth-Monthly',
    name: 'Growth-Monthly',
    unitAmount: 4400,
    currency: 'usd',
    credits: 27000,

    f_name: 'Growth',
    f_time: 'month',
    f_value: '44',
  },
  {
    id: 'prod_SRm4fUsX2YfpTr',
    priceId: 'price_1RWsVvPwdIBE7zGPr2aGNHni',
    uniqueName: 'Pro-Monthly',
    name: 'Pro-Monthly',
    unitAmount: 9500,
    currency: 'usd',
    credits: 60000,

    f_name: 'Pro',
    f_time: 'month',
    f_value: '95',
  },
];

const productionPlans: Plan[] = [
  {
    id: 'free',
    priceId: 'freePriceId',
    uniqueName: 'Free',
    name: 'Free',
    unitAmount: 0,
    currency: 'usd',
    credits: 100,

    f_name: 'Free',
    f_time: '7 days',
    f_value: '0',
  },
  {
    id: 'prod_SRmBDwBfh08VNX',
    priceId: 'price_1RWsciPp2kXXWKd5B67bUfVe',
    uniqueName: 'Starter-Monthly',
    name: 'Starter-Monthly',
    unitAmount: 2500,
    currency: 'usd',
    credits: 10000,

    f_name: 'Starter',
    f_time: 'month',
    f_value: '25',
  },
  {
    id: 'prod_SRmB6Q28aA5uvg',
    priceId: 'price_1RWscrPp2kXXWKd5gdIAPTCb',
    uniqueName: 'Growth-Monthly',
    name: 'Growth-Monthly',
    unitAmount: 4400,
    currency: 'usd',
    credits: 27000,

    f_name: 'Growth',
    f_time: 'month',
    f_value: '44',
  },
  {
    id: 'prod_SRmBjrbCMUNEDn',
    priceId: 'price_1RWscwPp2kXXWKd5RR0zv610',
    uniqueName: 'Pro-Monthly',
    name: 'Pro-Monthly',
    unitAmount: 9500,
    currency: 'usd',
    credits: 60000,

    f_name: 'Pro',
    f_time: 'month',
    f_value: '95',
  },
];

export const plans =
  env.NEXT_PUBLIC_APP_ENV === 'production' ? productionPlans : testPlans;

export const freePlan = plans[0];
