import Stripe from 'stripe';
import { env } from '@/env';
import { db } from '@/db';
import { user, subscriptionHistory } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { plans } from './plan';
import { logger } from '@/utils/logger';

// Initialize Stripe
const stripe = new Stripe(env.STRIPE_SECRET_KEY);

export class StripeService {
  // 创建或获取 Stripe 客户
  static async createOrGetCustomer(email: string) {
    // 查找现有用户
    const [existingUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email));

    if (existingUser?.stripeCustomerId) {
      return existingUser.stripeCustomerId;
    }

    // 创建新的 Stripe 客户
    const customer = await stripe.customers.create({
      email,
    });

    // 如果用户存在但没有 stripeCustomerId，更新用户
    if (existingUser) {
      await db
        .update(user)
        .set({
          stripeCustomerId: customer.id,
          updatedAt: new Date(),
        })
        .where(eq(user.id, existingUser.id));
    } else {
    }
    return customer.id;
  }

  // 获取用户的 Stripe Customer 对象
  static async getCustomer(uid: string) {
    const [currentUser] = await db.select().from(user).where(eq(user.uid, uid));

    if (!currentUser || !currentUser.stripeCustomerId) {
      return null;
    }

    try {
      const customer = (await stripe.customers.retrieve(
        currentUser.stripeCustomerId,
      )) as Stripe.Customer;
      return customer;
    } catch (error) {
      logger.error({ error, uid }, 'Failed to retrieve Stripe customer');
      return null;
    }
  }

  static async createCustomerPortalSession(uid: string) {
    const [currentUsr] = await db.select().from(user).where(eq(user.uid, uid));

    if (!currentUsr) {
      throw new Error('User not found');
    }
    if (!currentUsr.stripeCustomerId) {
      throw new Error('User has no stripe customer id');
    }
    const session = await stripe.billingPortal.sessions.create({
      customer: currentUsr.stripeCustomerId,
    });
    return session.url;
  }

  // 创建订阅会话
  static async createSubscriptionSession(
    email: string,
    priceId: string,
    locale: string,
  ) {
    const baseUrl = env.NEXT_PUBLIC_SITE_URL;
    const successUrl = `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/payment/success`;
    const cancelUrl = `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/payment/cancel`;
    const customerId = await this.createOrGetCustomer(email);

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      mode: 'subscription',
      allow_promotion_codes: true,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl,
      cancel_url: cancelUrl,
    });

    return session;
  }

  // 直接处理订阅升级，而不创建结账会话
  static async handleSubscriptionUpgrade(uid: string, newPriceId: string) {
    // 查找客户和活跃订阅
    const customer = await this.getCustomer(uid);
    if (!customer) {
      throw new Error('Customer not found. Please contact support.');
    }

    // 获取用户的活跃订阅
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      status: 'active',
      limit: 1,
    });

    if (subscriptions.data.length === 0) {
      throw new Error('No active subscription found. Please subscribe first.');
    }

    const currentSubscription = subscriptions.data[0];

    // 获取当前订阅项
    const subscriptionItems = currentSubscription.items.data;
    if (subscriptionItems.length === 0) {
      throw new Error('No subscription items found');
    }

    const currentPriceId = subscriptionItems[0].price.id;

    // 不允许通过此方法降级
    const currentPrice = await stripe.prices.retrieve(currentPriceId);
    const newPrice = await stripe.prices.retrieve(newPriceId);

    if (
      currentPrice.unit_amount !== null &&
      newPrice.unit_amount !== null &&
      newPrice.unit_amount < currentPrice.unit_amount
    ) {
      throw new Error(
        'This endpoint is for upgrades only. Please use the customer portal for downgrades.',
      );
    }

    try {
      // 直接更新订阅以使用新价格并进行按比例收费
      const updatedSubscription = await stripe.subscriptions.update(
        currentSubscription.id,
        {
          items: [
            {
              id: subscriptionItems[0].id,
              price: newPriceId,
            },
          ],
          // 立即创建按比例收费的发票并尝试收费
          proration_behavior: 'create_prorations',
          // 保持现有的账单周期
          billing_cycle_anchor: 'now',
          metadata: {
            upgraded_from: currentPriceId,
            upgraded_at: new Date().toISOString(),
          },
        },
      );

      // 找到新价格对应的计划
      const plan = plans.find((p) => p.priceId === newPriceId);
      if (!plan) {
        throw new Error('Plan not found for the new price');
      }

      // 更新用户记录中的计划和点数信息
      await db
        .update(user)
        .set({
          currentPlan: plan.uniqueName,
          amount: plan.credits,
          remainAmount: plan.credits,
          updatedAt: new Date(),
        })
        .where(eq(user.uid, uid));

      return {
        success: true,
        subscription: updatedSubscription,
        plan: plan.uniqueName,
        credits: plan.credits,
        remainAmount: plan.credits,
        message: 'Subscription upgraded successfully',
      };
    } catch (error) {
      logger.error(
        { error, userId: uid, newPriceId },
        'Failed to upgrade subscription',
      );
      throw new Error(
        `Failed to upgrade subscription: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  // 处理 Stripe Webhook
  static async handleWebhook(event: Stripe.Event) {
    switch (event.type) {
      case 'invoice.paid': {
        // 处理订阅升级产生的发票支付成功事件
        const invoice = event.data.object as Stripe.Invoice;

        // 验证这是一个订阅发票
        if ('subscription' in invoice && invoice.subscription) {
          try {
            // 获取订阅信息
            let subscriptionId: string;
            if (typeof invoice.subscription === 'string') {
              subscriptionId = invoice.subscription;
            } else if (
              typeof invoice.subscription === 'object' &&
              invoice.subscription !== null
            ) {
              // 尝试安全地获取ID，避免类型错误
              const subObj = invoice.subscription as Record<string, unknown>;
              subscriptionId = typeof subObj.id === 'string' ? subObj.id : '';
            } else {
              logger.error(
                { invoiceId: invoice.id },
                'Invalid subscription format in invoice',
              );
              break;
            }

            // 确保我们有有效的订阅ID
            if (!subscriptionId) {
              logger.error({ invoiceId: invoice.id }, 'Empty subscription ID');
              break;
            }

            const subscription =
              await stripe.subscriptions.retrieve(subscriptionId);

            // 检查发票的元数据是否包含升级标记
            if (subscription.metadata?.upgraded_from) {
              const customer = subscription.customer as string;
              const priceId = subscription.items.data[0].price.id;

              // 处理成功支付的升级发票
              logger.info(
                {
                  invoiceId: invoice.id,
                  subscriptionId: subscription.id,
                  customer,
                  priceId,
                  upgradedFrom: subscription.metadata.upgraded_from,
                },
                '用户成功支付了订阅升级费用',
              );
            }
          } catch (error) {
            logger.error(
              { error, invoiceId: invoice.id },
              'Failed to process paid upgrade invoice',
            );
          }
        }
        break;
      }
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;

        // Check if this is an upgrade session
        if (
          session.metadata?.isUpgrade === 'true' &&
          session.metadata?.oldSubscriptionId
        ) {
          try {
            // Wait for the new subscription to be active before canceling the old one
            // The subscription creation webhook will handle updating the user's plan
            await stripe.subscriptions.cancel(
              session.metadata.oldSubscriptionId,
              {
                prorate: true,
              },
            );

            logger.info(
              {
                sessionId: session.id,
                oldSubscriptionId: session.metadata.oldSubscriptionId,
                uid: session.metadata.uid,
              },
              'Canceled old subscription after successful upgrade',
            );
          } catch (error) {
            logger.error(
              {
                error,
                sessionId: session.id,
                oldSubscriptionId: session.metadata.oldSubscriptionId,
              },
              'Failed to cancel old subscription after upgrade',
            );
          }
        }
        break;
      }
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        const customer = subscription.customer as string;
        const currentUser = await StripeService.getCurrentUser(customer);
        logger.info(
          { email: currentUser.email, customer: currentUser.stripeCustomerId },
          '用户开始处理订阅信息',
        );
        if (!currentUser) {
          throw new Error('User not found');
        }
        const priceId = subscription.items.data[0].plan.id;
        logger.info({ priceId }, 'priceId');
        const plan = plans.find((p) => p.priceId === priceId);
        logger.info({ plan }, 'plan');
        if (!plan) {
          throw new Error('Plan not found');
        }
        const credits = plan.credits;
        logger.info({ credits }, '充值点数 credits');
        // Get subscription data safely
        const currentPeriodEnd = subscription.items.data[0].current_period_end
          ? new Date(subscription.items.data[0].current_period_end * 1000)
          : undefined;

        const currentPeriodStart = subscription.items.data[0]
          .current_period_start
          ? new Date(subscription.items.data[0].current_period_start * 1000)
          : new Date();
        // 更新用户订阅状态
        logger.info(
          {
            subscriptionStatus: subscription.status,
            periodStart: currentPeriodStart,
            periodEnd: currentPeriodEnd,
            credits,
          },
          'subscriptionStatus',
        );
        await db
          .update(user)
          .set({
            currentPlan: plan.uniqueName,
            subscriptionStatus: subscription.status,
            subscriptionExpiresAt: currentPeriodEnd,
            updatedAt: new Date(),
            remainAmount: credits,
            amount: credits,
          })
          .where(eq(user.id, currentUser.id));
        // Only record history if we have valid dates
        if (currentPeriodStart && currentPeriodEnd) {
          // 记录订阅历史
          await db.insert(subscriptionHistory).values({
            uid: currentUser.uid,
            stripeSubscriptionId: subscription.id,
            status: subscription.status,
            startDate: currentPeriodStart,
            endDate: currentPeriodEnd,
          });
        }

        break;
      }
    }
  }

  static async getCurrentUser(customer: string) {
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.stripeCustomerId, customer));
    if (!currentUser) {
      const currentCustomer = (await stripe.customers.retrieve(
        customer,
      )) as Stripe.Customer;
      const email = currentCustomer.email;
      const [existingUser] = await db
        .select()
        .from(user)
        .where(eq(user.email, email as string));
      if (!existingUser) {
        throw new Error('User not found');
      }
      // 更新用户的 customerId
      await db
        .update(user)
        .set({
          stripeCustomerId: customer,
          updatedAt: new Date(),
        })
        .where(eq(user.id, existingUser.id));
      return existingUser;
    }
    return currentUser;
  }
  // 验证 Webhook 签名
  static constructEvent(payload: string, signature: string) {
    return stripe.webhooks.constructEvent(
      payload,
      signature,
      env.STRIPE_WEBHOOK_SECRET,
    );
  }
}
