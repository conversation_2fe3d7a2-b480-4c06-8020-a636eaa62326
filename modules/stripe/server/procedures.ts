import { z } from 'zod';
import { StripeService } from '@/modules/stripe/service';
import {
  baseProcedure,
  protectedProcedure,
  createTRPCRouter,
} from '@/trpc/init';
import { TRPCError } from '@trpc/server';
import { plans } from '@/modules/stripe/plan';
import { logger } from '@/utils/logger';

export const stripeRouter = createTRPCRouter({
  getProducts: baseProcedure.query(async () => {
    return plans;
  }),
  createCheckoutSession: protectedProcedure
    .input(
      z.object({
        priceId: z.string(),
        locale: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const checkoutSession = await StripeService.createSubscriptionSession(
          ctx.user.email!,
          input.priceId,
          input.locale,
        );
        return checkoutSession.url;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '创建订阅失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create checkout session',
        });
      }
    }),
  createCustomerPortalSession: protectedProcedure.mutation(async ({ ctx }) => {
    if (!ctx.user.uid) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'User not found',
      });
    }
    try {
      const portalURL = await StripeService.createCustomerPortalSession(
        ctx.user.uid,
      );
      return portalURL;
    } catch (error) {
      logger.error({ email: ctx.user.email, error }, '创建Stripe门户会话失败:');
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to create customer portal session',
      });
    }
  }),
  upgradeSubscription: protectedProcedure
    .input(
      z.object({
        priceId: z.string(),
        locale: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user.uid) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not found',
        });
      }

      try {
        // 直接使用handleSubscriptionUpgrade方法立即进行升级并收取差价
        const directUpgradeResult =
          await StripeService.handleSubscriptionUpgrade(
            ctx.user.uid,
            input.priceId,
          );

        return {
          type: 'direct_upgrade',
          ...directUpgradeResult,
        };
      } catch (error) {
        if (error instanceof Error) {
          logger.error({ email: ctx.user.email, error }, '升级订阅失败:');
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error.message,
          });
        }
        logger.error({ email: ctx.user.email, error }, '升级订阅失败:');
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to upgrade subscription',
        });
      }
    }),
});
