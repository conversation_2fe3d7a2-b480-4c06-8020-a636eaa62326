import { db } from '@/db';
import {
  commentRule,
  platformConnect,
  socialComment,
  socialPage,
  socialPost,
  user,
  UserWithPlatform,
} from '@/db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { Session, User } from '@supabase/supabase-js';
import { uploadFromURL } from '@/modules/storage/service';
import { unlinkPlatform } from '../auth/supabase';
import { logger } from '@/utils/logger';
import { freePlan } from '@/modules/stripe/plan';
export const createUser = async (createUser: User, session: Session | null) => {
  const uid = createUser.id;
  const email = createUser.email;
  const avatar = createUser.user_metadata.avatar_url;

  const avatarUrl = avatar ? await uploadFromURL(avatar) : null;
  const [existingUser] = await db.select().from(user).where(eq(user.uid, uid));

  if (!existingUser) {
    // 获取点数
    const credits = freePlan.credits;
    // 默认给七天有效期
    const subscriptionExpiresAt = new Date(
      Date.now() + 7 * 24 * 60 * 60 * 1000,
    );
    const [data] = await db
      .insert(user)
      .values({
        uid: uid,
        email: email as string,
        avatar: avatarUrl,
        amount: credits,
        currentPlan: freePlan.uniqueName,
        subscriptionExpiresAt: subscriptionExpiresAt,
      })
      .returning();
    await updatePlatformConnect(uid, session);
    return data;
  }
  // 更新token
  await updatePlatformConnect(uid, session);
  return existingUser;
};

async function updatePlatformConnect(uid: string, session: Session | null) {
  if (!session) {
    return;
  }
  const {
    provider_token,
    refresh_token,
    expires_at,
    user: { id: session_user_id, email: session_user_email },
  } = session;
  console.log(`session: ${JSON.stringify(session)}`);
  console.log(
    `session_user_id: ${session_user_id} provider_token: ${provider_token} refresh_token: ${refresh_token} expires_at: ${expires_at}`,
  );

  const [currentPlatform] = await db
    .select()
    .from(platformConnect)
    .where(
      and(
        eq(platformConnect.uid, uid),
        eq(platformConnect.platform, 'facebook'),
        eq(platformConnect.externalId, session_user_id),
      ),
    );
  if (!currentPlatform) {
    // 开始判断
    const tokenExpiry = expires_at ? new Date(expires_at * 1000) : null;

    await db
      .insert(platformConnect)
      .values({
        uid: uid,
        platform: 'facebook',
        accountName: session_user_email,
        externalId: session_user_id,
        accessToken: provider_token,
        refreshToken: refresh_token,
        tokenExpiry: tokenExpiry,
      })
      .returning();

    return;
  } else {
    const tokenExpiry = expires_at ? new Date(expires_at * 1000) : null;

    await db
      .update(platformConnect)
      .set({
        tokenExpiry: tokenExpiry,
      })
      .where(eq(platformConnect.id, currentPlatform.id));
  }
}

export async function linkPlatform(uid: string, session: Session | null) {
  if (!session) {
    return;
  }
  const {
    provider_token,
    refresh_token,
    expires_at,
    user: { id: session_user_id, identities },
  } = session;
  console.log(`session: ${JSON.stringify(session)}`);
  console.log(
    `session_user_id: ${session_user_id} provider_token: ${provider_token} refresh_token: ${refresh_token} expires_at: ${expires_at}`,
  );
  const identity = identities?.[0];
  if (!identity) {
    return;
  }
  const soicalIdentities = identities.filter(
    (identitiy) => identitiy.provider !== 'email',
  );
  if (!soicalIdentities) {
    return;
  }

  for (const social of soicalIdentities) {
    const [currentPlatform] = await db
      .select()
      .from(platformConnect)
      .where(
        and(
          eq(platformConnect.identityId, social.id),
          eq(platformConnect.uid, uid),
          eq(platformConnect.platform, social.provider),
          eq(platformConnect.externalId, session_user_id),
        ),
      );
    if (!currentPlatform) {
      // 开始判断
      const tokenExpiry = expires_at ? new Date(expires_at * 1000) : null;

      await db
        .insert(platformConnect)
        .values({
          uid: uid,
          platform: social.provider,
          identityId: social.id,
          email: social.identity_data?.email,
          accountName: social.identity_data?.full_name,
          externalId: session_user_id,
          accessToken: provider_token,
          refreshToken: refresh_token,
          tokenExpiry: tokenExpiry,
        })
        .returning();

      return;
    } else {
      const tokenExpiry = expires_at ? new Date(expires_at * 1000) : null;

      await db
        .update(platformConnect)
        .set({
          accessToken: provider_token,
          refreshToken: refresh_token,
          tokenExpiry: tokenExpiry,
          email: social.identity_data?.email,
        })
        .where(eq(platformConnect.id, currentPlatform.id));
    }
  }
}

export const currentUserInfo = async (
  userId: string,
): Promise<UserWithPlatform | null> => {
  const [currentUser] = await db
    .select()
    .from(user)
    .where(eq(user.id, userId))
    .limit(1);
  if (!currentUser) {
    return null;
  }
  const platforms = await db
    .select()
    .from(platformConnect)
    .where(eq(platformConnect.uid, currentUser.uid));
  return { user: currentUser, platforms };
};

export const getUserByUid = async (uid: string) => {
  const [currentUser] = await db
    .select()
    .from(user)
    .where(eq(user.uid, uid))
    .limit(1);
  return currentUser;
};

export const deleteData = async (uid: string) => {
  logger.info(`deleteData: ${uid}`);
  // 删除用户 不可撤消
  // 开始删除数据
  // comments
  logger.info(`delete socialComment: ${uid}`);
  await db.delete(socialComment).where(eq(socialComment.uid, uid));
  // posts
  logger.info(`delete socialPost: ${uid}`);
  await db.delete(socialPost).where(eq(socialPost.uid, uid));
  logger.info(`delete socialPage: ${uid}`);
  await db.delete(socialPage).where(eq(socialPage.uid, uid));
  // commentRule
  logger.info(`delete commentRule: ${uid}`);
  await db.delete(commentRule).where(eq(commentRule.uid, uid));
  // platforms
  const platforms = await db
    .select()
    .from(platformConnect)
    .where(eq(platformConnect.uid, uid));
  // unlink identity
  for (const platform of platforms) {
    logger.info(`unlink identity: ${platform.platform}`);
    await unlinkPlatform(platform);
  }
  // platformConnect
  logger.info(`delete platformConnect: ${uid}`);
  await db.delete(platformConnect).where(eq(platformConnect.uid, uid));
  return true;
};

// 评论扣费
export const commentCredits = 1;
// 聊天扣费
export const chatCredits = 100;

export const deductAmount = async (uid: string, amount: number) => {
  const [result] = await db
    .update(user)
    .set({ remainAmount: sql`${user.remainAmount} - ${amount}` })
    .where(eq(user.uid, uid))
    .returning();
  return result;
};

export const deleteDataByExternalPageId = async (
  uid: string,
  externalPageId: string,
) => {
  logger.info(
    { uid, externalPageId },
    '开始按页面信息删除 deleteDataByExternalPageId',
  );
  // 删除用户 不可撤消
  // 开始删除数据
  // comments
  logger.info({ uid, externalPageId }, '删除评论');
  await db
    .delete(socialComment)
    .where(
      and(
        eq(socialComment.uid, uid),
        eq(socialComment.externalPageId, externalPageId),
      ),
    );
  // posts
  logger.info({ uid, externalPageId }, '删除帖子');
  await db
    .delete(socialPost)
    .where(
      and(
        eq(socialPost.uid, uid),
        eq(socialPost.externalPageId, externalPageId),
      ),
    );
  // socialPage
  logger.info({ uid, externalPageId }, '删除页面');
  await db
    .delete(socialPage)
    .where(
      and(
        eq(socialPage.uid, uid),
        eq(socialPage.externalPageId, externalPageId),
      ),
    );
  // commentRule
  logger.info({ uid, externalPageId }, '删除评论规则');
  await db
    .delete(commentRule)
    .where(
      and(
        eq(commentRule.uid, uid),
        eq(commentRule.externalPageId, externalPageId),
      ),
    );

  // platformConnect
  return true;
};
