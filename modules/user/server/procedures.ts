import {
  protectedProcedure,
  createTRPCRouter,
  baseProcedure,
} from '@/trpc/init';
import {
  currentUserInfo,
  deleteData,
  deleteDataByExternalPageId,
} from '@/modules/user/service';
import { z } from 'zod';
import { login, signup } from '@/modules/auth/supabase';
import { logger } from '@/utils/logger';
import { TRPCError } from '@trpc/server';

export const userRouter = createTRPCRouter({
  currentUserInfo: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.user.id;
    const currentUser = await currentUserInfo(userId);
    return currentUser;
  }),
  deleteData: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const uid = ctx.user.uid;
      await deleteData(uid);
      return true;
    } catch (error) {
      logger.error({ email: ctx.user.email, error }, '删除用户数据失败:');
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete user data',
      });
    }
  }),
  deleteDataByExternalPageId: protectedProcedure
    .input(z.object({ externalPageId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const uid = ctx.user.uid;
        await deleteDataByExternalPageId(uid, input.externalPageId);
        return true;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '删除用户数据失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete user data',
        });
      }
    }),
  userSignup: baseProcedure
    .input(
      z.object({
        email: z.string(),
        password: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const formData = new FormData();
        formData.append('email', input.email);
        formData.append('password', input.password);
        return await signup(formData);
      } catch (error) {
        logger.error({ email: input.email, error }, '注册失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to signup',
        });
      }
    }),
  userLogin: baseProcedure
    .input(
      z.object({
        email: z.string(),
        password: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const formData = new FormData();
        formData.append('email', input.email);
        formData.append('password', input.password);
        return await login(formData);
      } catch (error) {
        logger.error({ email: input.email, error }, '登录失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to login',
        });
      }
    }),
});
