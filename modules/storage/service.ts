import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { env } from '@/env';

// S3 client configuration
const s3Client = new S3Client({
  region: env.AWS_REGION,
  endpoint: env.AWS_ENDPOINT_URL,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
  requestHandler: {
    timeout: 15000, // 15 seconds timeout
  },
  forcePathStyle: true,
});

const bucketName = env.AWS_S3_BUCKET_NAME;

export type UploadOptions = {
  contentType?: string;
  isPublic?: boolean;
  metadata?: Record<string, string>;
};

/**
 * Uploads a file to S3
 * @param key - The file key (path) in S3
 * @param body - The file data to upload (<PERSON><PERSON><PERSON>, Uint8Array, <PERSON>lob, etc.)
 * @param options - Additional upload options
 * @returns The URL of the uploaded file
 */
export async function uploadFile(
  key: string,
  body: Buffer | Uint8Array | Blob | string,
  options: UploadOptions = {},
): Promise<string> {
  const { contentType, metadata = {} } = options;

  try {
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: body,
      ContentType: contentType,
      Metadata: metadata,
    });

    await s3Client.send(command);

    return `${env.AWS_S3_PUBLIC_URL}/${key}`;
  } catch (error) {
    console.error('Error uploading file to S3:', error);
    throw new Error('Failed to upload file');
  }
}

export async function uploadFromURL(url: string, prefix?: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  const bytes = await blob.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const contentType = blob.type;
  // get url last part as file name
  const fileName = url.split('/').pop() || 'default';
  const key = generateUniqueFileKey(fileName, prefix);
  await uploadFile(key, buffer, { contentType });
  return `${env.AWS_S3_PUBLIC_URL}/${key}`;
}

/**
 * Generates a presigned URL for downloading a file from S3
 * @param key - The file key (path) in S3
 * @param expiresIn - The expiration time of the URL in seconds (default: 3600)
 * @returns A presigned URL for downloading
 */
export async function getPresignedDownloadUrl(
  key: string,
  expiresIn: number = 3600,
): Promise<string> {
  try {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    console.error('Error generating presigned download URL:', error);
    throw new Error('Failed to generate download URL');
  }
}

/**
 * Helper function to generate a unique file key for S3
 * @param fileName - The original file name
 * @param prefix - Optional prefix for the key
 * @returns A unique file key
 */
export function generateUniqueFileKey(
  fileName: string,
  prefix?: string,
): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = fileName.includes('.') ? fileName.split('.').pop() : '';

  const baseName = fileName.includes('.')
    ? fileName.substring(0, fileName.lastIndexOf('.'))
    : fileName;

  const sanitizedName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-');

  const prefixPath = prefix ? `${prefix}/` : '';

  return `${prefixPath}${sanitizedName}-${timestamp}-${randomString}${fileExtension ? `.${fileExtension}` : ''}`;
}
