// Facebook webhook完整类型定义
export interface FacebookWebhookBody {
  entry: FacebookEntry[];
  object: string; // 通常是 "page"
}

export interface FacebookEntry {
  id: string; // 页面ID
  time: number; // 时间戳
  changes: FacebookChange[];
}

export interface FacebookChange {
  value: FacebookChangeValue;
  field: string; // 通常是 "feed"
}

export interface FacebookChangeValue {
  from: {
    id: string;
    name: string;
  };
  post: {
    status_type: string;
    is_published: boolean;
    updated_time: string;
    permalink_url: string;
    promotion_status: string;
    id: string;
  };
  message: string;
  post_id: string;
  comment_id: string;
  created_time: number;
  item: string; // 通常是 "comment"
  parent_id: string;
  verb: string; // 通常是 "add"
}

// Instagram webhook完整类型定义
export interface InstagramWebhookBody {
  entry: InstagramEntry[];
  object: string; // 通常是 "instagram"
}

export interface InstagramEntry {
  id: string; // 账户ID
  time: number; // 时间戳
  changes: InstagramChange[];
}

export interface InstagramChange {
  value: InstagramChangeValue;
  field: string; // 通常是 "comments"
}

export interface InstagramChangeValue {
  from: {
    id: string;
    username: string;
  };
  media: {
    id: string;
    media_product_type: string; // 通常是 "FEED"
  };
  id: string; // 评论ID
  text: string; // 评论内容
}

