import { db } from '@/db';
import { translateText } from '@/modules/aisdk/service/translate';
import { and, count, desc, eq, gte, lte, like } from 'drizzle-orm';
import {
  CommentWithPost,
  socialPage,
  SocialPagesSchema,
  socialComment,
  SocialCommentsSchema,
  socialPost,
  user,
  userAsset,
  SocialPostsSchema,
  webhookEvent,
} from '@/db/schema';
import {
  getPagePosts,
  getInstagramMedia,
  getInstagramComments,
  hideComment,
  hideInstagramComment,
  replyFacebookComment,
  replyInstagramComment,
  likeComment,
  getPost,
  FacebookPost,
  FacebookPostAttachment,
  FacebookPostMessageTag,
  getInstagramMediaByMediaId,
  InstagramMedia,
} from '@/modules/core/facebook';
import { uploadFile, uploadFromURL } from '@/modules/storage/service';
import { getCommentEmotion } from '@/modules/aisdk/service/comment-analyze';
import { mkConfig, generateCsv, asString } from 'export-to-csv';
import { logger } from '@/utils/logger';
import {
  getCommentRuleByPageId,
  processCommentByRule,
  processCommentRule,
} from '@/modules/core/comment-rule/service';
import { deductAmount } from '@/modules/user/service';
import { commentCredits } from '@/modules/user/service';
import {
  FacebookChangeValue,
  FacebookWebhookBody,
  InstagramChangeValue,
  InstagramWebhookBody,
} from '@/modules/core/comments/types';
export const fetchLatestComments = async (uid: string) => {
  const pages: SocialPagesSchema[] = await db
    .select()
    .from(socialPage)
    .where(and(eq(socialPage.uid, uid), eq(socialPage.bindStatus, true)));
  let totalPostCount = 0;
  let totalCommentCount = 0;
  for (const page of pages) {
    const platform = page.platform;
    if (platform === 'facebook') {
      const { facbookPostCount, facebookCommentCount } =
        await getOrUpdateFacebookPost(page, uid);
      totalPostCount += facbookPostCount;
      totalCommentCount += facebookCommentCount;
    } else if (platform === 'instagram') {
      const { instagramPostCount, instagramCommentCount } =
        await getOrUpdateInstagramPost(page, uid);
      totalPostCount += instagramPostCount;
      totalCommentCount += instagramCommentCount;
    }
  }
  return {
    totalPostCount,
    totalCommentCount,
  };
};

const getOrUpdateFacebookPost = async (
  page: SocialPagesSchema,
  uid: string,
) => {
  const posts = await getPagePosts(
    page.externalPageId as string,
    page.accessToken as string,
  );
  let facbookPostCount = 0;
  let facebookCommentCount = 0;
  for (const post of posts) {
    const { id, message, created_time, full_picture, comments, permalink_url } =
      post;
    // 保存到 socialPost 表
    const picturePublicURL = full_picture
      ? await uploadFromURL(full_picture)
      : null;
    // 查询是否存在
    const [existingPost] = await db
      .select()
      .from(socialPost)
      .where(eq(socialPost.externalPostId, id));
    if (existingPost) {
      logger.info(
        `post已存在: ${JSON.stringify(existingPost)}`,
        'getOrUpdateFacebookPost',
      );
    } else {
      logger.info(
        `开始保存post: ${JSON.stringify(post)}`,
        'getOrUpdateFacebookPost',
      );
      const [newPost] = await db
        .insert(socialPost)
        .values({
          externalPageId: page.externalPageId as string,
          uid: uid,
          permalink: permalink_url,
          externalPostId: id,
          platform: 'facebook',
          content: message,
          postTime: new Date(created_time),
          metadata: {
            picturePublicURL,
          },
        })
        .returning();
      if (newPost) {
        facbookPostCount++;
      }
    }
    for (const comment of comments?.data || []) {
      const {
        id,
        message,
        created_time,
        from,
        is_hidden,
        can_hide,
        can_comment,
        can_reply_privately,
        can_like,
        like_count,
        user_likes,
        attachment,
        message_tags,
        permalink_url: commentPermalinkUrl,
      } = comment;
      // 查询是否存在
      const [existingComment] = await db
        .select()
        .from(socialComment)
        .where(eq(socialComment.externalCommentId, id));
      if (existingComment) {
        logger.info(
          `comment已存在: ${JSON.stringify(existingComment)}`,
          'getOrUpdateFacebookPost',
        );
      } else {
        // 保存到 socialComment 表
        logger.info(
          `开始保存comment: ${JSON.stringify(comment)}`,
          'getOrUpdateFacebookPost',
        );
        try {
          const [newComment] = await db
            .insert(socialComment)
            .values({
              externalPostId: post.id as string,
              uid,
              externalPageId: page.externalPageId as string,
              platform: 'facebook',
              externalCommentId: id,
              authorName: from?.name || '',
              authorId: from?.id || '',
              content: message,
              commentTime: new Date(created_time),
              isHidden: is_hidden || false,
              canHide: can_hide || false,
              canComment: can_comment || false,
              canReplyPrivately: can_reply_privately || false,
              canLike: can_like || false,
              likeCount: like_count || 0,
              userLikes: user_likes || false,
              hiddenBy: '',
              postExcerpt: '',
              attachments: attachment,
              messageTags: message_tags,
              permalinkUrl: commentPermalinkUrl,
            })
            .returning();
          if (newComment) {
            facebookCommentCount++;
          }
        } catch (error) {
          logger.error(
            `保存comment失败: ${JSON.stringify(comment)}, error: ${error}`,
            'getOrUpdateFacebookPost',
          );
        }
      }
    }
  }
  return {
    facbookPostCount,
    facebookCommentCount,
  };
};

async function getOrUpdateInstagramPost(page: SocialPagesSchema, uid: string) {
  const media = await getInstagramMedia(
    page.externalPageId as string,
    page.accessToken as string,
  );
  let instagramPostCount = 0;
  let instagramCommentCount = 0;

  for (const post of media || []) {
    const { id, caption, timestamp, media_url, permalink } = post;
    // Save to socialPost table
    const picturePublicURL = media_url ? await uploadFromURL(media_url) : null;
    // Check if post exists
    const [existingPost] = await db
      .select()
      .from(socialPost)
      .where(eq(socialPost.externalPostId, id));
    if (existingPost) {
      logger.info(
        `Instagram post already exists: ${JSON.stringify(existingPost)}`,
        'getOrUpdateInstagramPost',
      );
    } else {
      logger.info(
        `Saving Instagram post: ${JSON.stringify(post)}`,
        'getOrUpdateInstagramPost',
      );
      const [newPost] = await db
        .insert(socialPost)
        .values({
          externalPageId: page.externalPageId as string,
          uid,
          permalink,
          externalPostId: id,
          platform: 'instagram',
          content: caption,
          postTime: new Date(timestamp),
          metadata: {
            picturePublicURL,
          },
        })
        .returning();
      if (newPost) {
        instagramPostCount++;
      }
    }

    // Get comments for the post
    const postComments = await getInstagramComments(
      id,
      page.accessToken as string,
    );
    for (const comment of postComments || []) {
      const {
        id,
        text,
        timestamp,
        username,
        like_count,
        hidden,
        user_has_liked,
      } = comment;
      // Check if comment exists
      const [existingComment] = await db
        .select()
        .from(socialComment)
        .where(eq(socialComment.externalCommentId, id));
      if (existingComment) {
        logger.info(
          `Instagram comment already exists: ${JSON.stringify(existingComment)}`,
          'getOrUpdateInstagramPost',
        );
      } else {
        // Save to socialComment table
        logger.info(
          `Saving Instagram comment: ${JSON.stringify(comment)}`,
          'getOrUpdateInstagramPost',
        );
        const [newComment] = await db
          .insert(socialComment)
          .values({
            externalPostId: post.id as string,
            uid,
            platform: 'instagram',
            externalPageId: page.externalPageId as string,
            externalCommentId: id,
            authorName: username || '',
            authorId: '', // Instagram doesn't provide user_id
            content: text,
            commentTime: new Date(timestamp),
            isHidden: hidden || false,
            canHide: true,
            canComment: true,
            canReplyPrivately: false,
            canLike: true,
            likeCount: like_count || 0,
            userLikes: user_has_liked || false,
            hiddenBy: '',
            postExcerpt: '',
          })
          .returning();
        if (newComment) {
          instagramCommentCount++;
        }
      }
    }
  }
  return {
    instagramPostCount,
    instagramCommentCount,
  };
}

export const pageListComments = async (
  uid: string,
  param: {
    q: string | undefined;
    startDate: string | undefined;
    endDate: string | undefined;
    externalPageId: string | undefined;
    emotion: 'positive' | 'negative' | 'neutral' | 'pending' | undefined;
    hiddenStatus: boolean | undefined;
    pageNo: number;
    pageSize: number;
    platform: string | undefined;
  },
) => {
  const {
    q,
    pageNo,
    pageSize,
    platform,
    startDate,
    endDate,
    externalPageId,
    emotion,
    hiddenStatus,
  } = param;
  // add 1 day to endDate
  const endDatePlusOneDay = endDate
    ? new Date(endDate).getTime() + 24 * 60 * 60 * 1000
    : undefined;
  const condition = and(
    eq(socialComment.uid, uid),
    q ? like(socialComment.content, `%${q}%`) : undefined,
    platform ? eq(socialPage.platform, platform) : undefined,
    startDate ? gte(socialComment.commentTime, new Date(startDate)) : undefined,
    endDatePlusOneDay
      ? lte(socialComment.commentTime, new Date(endDatePlusOneDay))
      : undefined,
    externalPageId
      ? eq(socialComment.externalPageId, externalPageId)
      : undefined,
    emotion ? eq(socialComment.emotion, emotion) : undefined,
    hiddenStatus ? eq(socialComment.isHidden, hiddenStatus) : undefined,
  );
  const [total] = await db
    .select({ count: count() })
    .from(socialComment)
    .innerJoin(
      socialPost,
      eq(socialComment.externalPostId, socialPost.externalPostId),
    )
    .innerJoin(
      socialPage,
      eq(socialPost.externalPageId, socialPage.externalPageId),
    )
    .where(condition);
  const comments: CommentWithPost[] = await db
    .select({
      comment: socialComment,
      post: socialPost,
      page: socialPage,
    })
    .from(socialComment)
    .innerJoin(
      socialPost,
      eq(socialComment.externalPostId, socialPost.externalPostId),
    )
    .innerJoin(
      socialPage,
      eq(socialPost.externalPageId, socialPage.externalPageId),
    )
    .where(condition)
    .orderBy(desc(socialComment.commentTime))
    .limit(pageSize)
    .offset((pageNo - 1) * pageSize);
  return {
    data: comments,
    total: total.count,
    pageNo,
    pageSize,
  };
};

export const replyToComment = async (
  uid: string,
  commentId: string,
  replyText: string,
) => {
  const [comment] = await db
    .select()
    .from(socialComment)
    .where(eq(socialComment.id, commentId));
  if (comment) {
    // 获取pageToken
    const externalPageId = comment.externalPageId;
    const [page] = await db
      .select()
      .from(socialPage)
      .where(eq(socialPage.externalPageId, externalPageId as string))
      .limit(1);
    if (page) {
      const pageToken = page.accessToken;
      let result = null;
      if (comment.platform === 'facebook') {
        result = await replyFacebookComment(
          comment.externalCommentId as string,
          replyText,
          pageToken as string,
        );
      } else if (comment.platform === 'instagram') {
        result = await replyInstagramComment(
          comment.externalCommentId as string,
          replyText,
          pageToken as string,
        );
      }
      if (result) {
        //
        const [updatedComment] = await db
          .update(socialComment)
          .set({ replyText })
          .where(eq(socialComment.id, commentId))
          .returning();
        return updatedComment;
      } else {
        return null;
      }
    }
    return null;
  }
  return null;
};

export const changeCommentLikeStatus = async (
  uid: string,
  commentIds: string[],
  likeStatus: boolean,
) => {
  logger.info(
    `changeCommentLikeStatus: ${uid}, ${commentIds}, ${likeStatus}`,
    'changeCommentLikeStatus',
  );
  for (const commentId of commentIds) {
    logger.info(
      `处理评论 ${commentId} likeStatus: ${likeStatus}`,
      'changeCommentLikeStatus',
    );

    const [comment] = await db
      .select()
      .from(socialComment)
      .where(eq(socialComment.id, commentId))
      .limit(1);

    if (comment) {
      // 调用 facebook api 隐藏评论
      const externalCommentId = comment.externalCommentId;
      if (comment.platform === 'facebook') {
        const result = await likeComment(
          uid,
          externalCommentId as string,
          likeStatus,
        );
        if (result) {
          await db
            .update(socialComment)
            .set({ userLikes: likeStatus })
            .where(eq(socialComment.id, commentId))
            .returning();
        }
      }
    } else {
      logger.warn(`评论 ${commentId} 不存在`, 'changeCommentLikeStatus');
    }
  }
  return null;
};

export const changeCommentHideStatus = async (
  uid: string,
  commentIds: string[],
  platform: string,
  hiddenStatus: boolean,
) => {
  logger.info(
    `changeCommentHideStatus: ${uid}, ${commentIds}, ${platform}, ${hiddenStatus}`,
    'changeCommentHideStatus',
  );
  for (const commentId of commentIds) {
    logger.info(
      `处理评论 ${commentId} hideStatus: ${hiddenStatus}`,
      'changeCommentHideStatus',
    );
    const [comment] = await db
      .select()
      .from(socialComment)
      .where(and(eq(socialComment.uid, uid), eq(socialComment.id, commentId)));
    if (comment) {
      // 调用 facebook api 隐藏评论
      if (platform === 'facebook') {
        const result = await changeFacebookCommentHideStatus(
          uid,
          commentId,
          comment,
          hiddenStatus,
        );
        return result;
      } else if (platform === 'instagram') {
        const result = await changeInstagramCommentHideStatus(
          uid,
          commentId,
          comment,
          hiddenStatus,
        );
        return result;
      }
    }
  }
  return null;
};

const changeFacebookCommentHideStatus = async (
  uid: string,
  commentId: string,
  comment: SocialCommentsSchema,
  hiddenStatus: boolean,
) => {
  logger.info('facebook 获取 pageToken', 'changeFacebookCommentHideStatus');
  // 获取 pageToken
  const [page] = await db
    .select({
      accessToken: socialPage.accessToken,
    })
    .from(socialPage)
    .innerJoin(
      socialPost,
      eq(socialPage.externalPageId, socialPost.externalPageId),
    )
    .where(
      and(
        eq(socialPage.uid, uid),
        eq(socialPost.externalPostId, comment.externalPostId as string),
      ),
    );
  const pageToken = page.accessToken;
  logger.info(
    `facebook 获取 pageToken: ${pageToken}`,
    'changeFacebookCommentHideStatus',
  );
  const ok = await hideComment(
    comment.externalCommentId as string,
    hiddenStatus,
    pageToken as string,
  );
  if (ok) {
    const [result] = await db
      .update(socialComment)
      .set({ isHidden: hiddenStatus })
      .where(eq(socialComment.id, commentId))
      .returning();
    return result;
  } else {
    throw new Error('change hide status failed');
  }
};
const changeInstagramCommentHideStatus = async (
  uid: string,
  commentId: string,
  comment: SocialCommentsSchema,
  hiddenStatus: boolean,
) => {
  logger.info(
    `instagram 获取 pageToken: ${uid}, ${commentId}, ${comment}, ${hiddenStatus}`,
    'changeInstagramCommentHideStatus',
  );
  // 获取 pageToken
  const [page] = await db
    .select({
      accessToken: socialPage.accessToken,
    })
    .from(socialPage)
    .innerJoin(
      socialPost,
      eq(socialPage.externalPageId, socialPost.externalPageId),
    )
    .where(
      and(
        eq(socialPage.uid, uid),
        eq(socialPost.externalPostId, comment.externalPostId as string),
      ),
    );
  const pageToken = page.accessToken;
  logger.info(
    `instagram 获取 pageToken: ${pageToken}`,
    'changeInstagramCommentHideStatus',
  );
  logger.info(
    `instagram 隐藏评论: ${comment.externalCommentId}, ${hiddenStatus}`,
    'changeInstagramCommentHideStatus',
  );
  const ok = await hideInstagramComment(
    comment.externalCommentId as string,
    hiddenStatus,
    pageToken as string,
  );
  if (ok) {
    const [result] = await db
      .update(socialComment)
      .set({ isHidden: hiddenStatus })
      .where(eq(socialComment.id, commentId))
      .returning();
    return result;
  } else {
    throw new Error('change hide status failed');
  }
  return null;
};

export const generateCommentEmotion = async (
  uid: string,
  commentId: string,
  commentText: string,
) => {
  logger.info(
    `generateCommentEmotion: ${uid}, ${commentId}, ${commentText}`,
    'generateCommentEmotion',
  );
  const [comment] = await db
    .select()
    .from(socialComment)
    .where(and(eq(socialComment.uid, uid), eq(socialComment.id, commentId)))
    .limit(1);
  logger.info(
    `generateCommentEmotion: ${JSON.stringify(comment)}`,
    'generateCommentEmotion',
  );
  if (comment) {
    logger.info('generate comment emotion', 'generateCommentEmotion');
    const { emotion, reason } = await getCommentEmotion(commentText);
    logger.info(
      `emotion: ${emotion}, reason: ${reason}`,
      'generateCommentEmotion',
    );
    // 扣点数
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.uid, uid))
      .limit(1);
    if (currentUser) {
      logger.info(
        `user: ${JSON.stringify(currentUser)}`,
        'generateCommentEmotion',
      );
      const [updatedUser] = await db
        .update(user)
        .set({ remainAmount: currentUser.remainAmount - 1 })
        .where(eq(user.uid, uid))
        .returning();
      logger.info(
        `updatedUser: ${JSON.stringify(updatedUser)}`,
        'generateCommentEmotion',
      );
      if (updatedUser) {
        // 更新评论
        const [updatedComment] = await db
          .update(socialComment)
          .set({ emotion, ai_reason: reason })
          .where(eq(socialComment.id, commentId))
          .returning();
        logger.info(
          `updatedComment: ${JSON.stringify(updatedComment)}`,
          'generateCommentEmotion',
        );
        return updatedComment;
      }
    }
  }
  return null;
};

export const anaylyzeFromFacebookComment = async (
  comment: SocialCommentsSchema,
) => {
  // 隐藏分析
  const rule = await getCommentRuleByPageId(
    comment.uid,
    comment.externalPageId as string,
  );
  if (!rule) {
    throw new Error('Rule not found');
  }

  const hideResult = await processCommentByRule(
    rule,
    comment.content || '',
    comment.attachments as FacebookPostAttachment,
    comment.messageTags as FacebookPostMessageTag[],
  );

  // 情感分析
  const { emotion, reason } = await getCommentEmotion(
    comment.content as string,
  );
  logger.info(`emotion: ${emotion}, reason: ${reason}`, 'analyzeComment');

  // 如果有一个隐藏规则被击中，则隐藏评论
  const hideStatus = hideResult.length > 0;
  logger.info(
    { hideStatus, emotion, reason, uid: comment.uid },
    'analyzeComment',
  );
  // 更新评论
  await db
    .update(socialComment)
    .set({ isHidden: hideStatus, emotion, ai_reason: reason })
    .where(eq(socialComment.id, comment.id))
    .returning();
  // 扣除点数
  await deductAmount(comment.uid, commentCredits);
  return {
    hideResult,
    emotionResult: {
      emotion,
      reason,
    },
  };
};

export const anaylyzeComment = async (uid: string, commentId: string) => {
  const [comment] = await db
    .select()
    .from(socialComment)
    .where(and(eq(socialComment.uid, uid), eq(socialComment.id, commentId)))
    .limit(1);
  if (comment) {
    // 隐藏分析
    const hideResult = await processCommentRule(uid, comment.id, comment);
    // 情感分析
    const { emotion, reason } = await getCommentEmotion(
      comment.content as string,
    );
    logger.info(`emotion: ${emotion}, reason: ${reason}`, 'analyzeComment');

    // 如果有一个隐藏规则被击中，则隐藏评论
    const hideStatus = hideResult.length > 0;
    logger.info({ hideStatus, emotion, reason, uid }, 'analyzeComment');
    // 更新评论
    await db
      .update(socialComment)
      .set({ isHidden: hideStatus, emotion, ai_reason: reason })
      .where(eq(socialComment.id, commentId))
      .returning();
    // 扣除点数
    await deductAmount(uid, commentCredits);
    return {
      hideResult,
      emotionResult: {
        emotion,
        reason,
      },
    };
  } else {
    return null;
  }
};

export const exportCsv = async (
  uid: string,
  param: {
    q: string | undefined;
    platform: string | undefined;
    startDate: string | undefined;
    endDate: string | undefined;
    externalPageId: string | undefined;
    emotion: 'positive' | 'negative' | 'neutral' | 'pending' | undefined;
    hiddenStatus: boolean | undefined;
  },
) => {
  const {
    q,
    platform,
    startDate,
    endDate,
    externalPageId,
    emotion,
    hiddenStatus,
  } = param;
  const endDatePlusOneDay = endDate
    ? new Date(endDate).getTime() + 24 * 60 * 60 * 1000
    : undefined;
  const condition = and(
    eq(socialComment.uid, uid),
    q ? like(socialComment.content, `%${q}%`) : undefined,
    platform ? eq(socialPage.platform, platform) : undefined,
    startDate ? gte(socialComment.commentTime, new Date(startDate)) : undefined,
    endDatePlusOneDay
      ? lte(socialComment.commentTime, new Date(endDatePlusOneDay))
      : undefined,
    externalPageId
      ? eq(socialComment.externalPageId, externalPageId)
      : undefined,
    emotion ? eq(socialComment.emotion, emotion) : undefined,
    hiddenStatus ? eq(socialComment.isHidden, hiddenStatus) : undefined,
  );
  const comments = await db
    .select({
      commentContent: socialComment.content,
      commentEmotion: socialComment.emotion,
      hiddenStatus: socialComment.isHidden,
      commentName: socialComment.authorName,
      commentTime: socialComment.commentTime,
      postContent: socialPost.content,
      pageName: socialPage.pageName,
      platform: socialPage.platform,
    })
    .from(socialComment)
    .innerJoin(
      socialPost,
      eq(socialComment.externalPostId, socialPost.externalPostId),
    )
    .innerJoin(
      socialPage,
      eq(socialComment.externalPageId, socialPage.externalPageId),
    )
    .where(condition)
    .orderBy(desc(socialComment.commentTime));

  const exportData = comments.map((comment) => ({
    ...comment,
    hiddenStatus: comment.hiddenStatus ? 'hidden' : 'active',
    commentTime: comment.commentTime?.toISOString(),
  }));
  const csvConfig = mkConfig({
    useKeysAsHeaders: false,
    columnHeaders: [
      { key: 'commentContent', displayLabel: 'comment content' },
      { key: 'commentEmotion', displayLabel: 'emotion' },
      { key: 'hiddenStatus', displayLabel: 'status' },
      { key: 'commentName', displayLabel: 'comment name' },
      { key: 'commentTime', displayLabel: 'comment time' },
      { key: 'postContent', displayLabel: 'post content' },
      { key: 'pageName', displayLabel: 'page name' },
      { key: 'platform', displayLabel: 'platform' },
    ],
  });
  const csv = generateCsv(csvConfig)(exportData);
  // convert to buffer
  const csvBuffer = new Uint8Array(Buffer.from(asString(csv)));
  // get current date
  const currentDate = new Date().toISOString().split('T')[0];
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
  const filename = `export/comments-export-${currentDate}-${uuid}.csv`;
  // upload to storage
  const fileUrl = await uploadFile(filename, csvBuffer, {
    contentType: 'text/csv',
  });
  // save to user asset
  const [asset] = await db
    .insert(userAsset)
    .values({
      uid,
      type: 'export_data',
      url: fileUrl,
    })
    .returning();
  return {
    fileUrl,
    filename,
    assetId: asset.id,
  };
};

export const translateComment = async (
  uid: string,
  commentId: string,
  message: string,
  targetLanguage: string,
) => {
  const translatedMessage = await translateText(message, targetLanguage);
  // update comment
  await db
    .update(socialComment)
    .set({ contentTranslated: translatedMessage })
    .where(eq(socialComment.id, commentId))
    .returning();
  // 查询 user 信息 判断是否存在语言数据
  const [currentUser] = await db
    .select()
    .from(user)
    .where(eq(user.uid, uid))
    .limit(1);
  if (currentUser && currentUser.preferedTranslateLanguage !== targetLanguage) {
    // 更新 user 信息
    await db
      .update(user)
      .set({ preferedTranslateLanguage: targetLanguage })
      .where(eq(user.uid, uid))
      .returning();
  }
  return translatedMessage;
};

export interface CommentData {
  from?: {
    id: string;
    name: string;
  };
  comment_id: string;
  post_id: string;
  message?: string;
  created_time: number;
  parent_id?: string;
}

export async function handleFacebookCommentEvents(
  eventBody: FacebookWebhookBody,
  id: string,
) {
  logger.info({ eventBody, id }, 'handleFacebookCommentEvents');
  // 检查对象类型
  if (eventBody.object === 'page') {
    // 遍历所有entry
    for (const entry of eventBody.entry) {
      const { id: externalPageId, time } = entry;
      const pageData = await getFacebookSocialPage(externalPageId);
      if (!pageData) {
        logger.error(
          { externalPageId, time, eventBody },
          'social page not exist',
        );
        throw new Error('social page not exist');
      }
      // 遍历所有changes
      for (const change of entry.changes) {
        const { value } = change;
        // 判断 post 是否存在，不存在则新建
        const postItem = await getOrCreateFacebookSocialPost(
          value.post,
          pageData,
        );

        // 处理页面评论事件
        if (change.field === 'feed' && change.value.item === 'comment') {
          // await handleComment(change.value);
          const commentItem = await handleFacebookCommentData(value, postItem);
          await anaylyzeFromFacebookComment(commentItem);
        }
      }
    }
  }
  await db
    .update(webhookEvent)
    .set({ status: 'completed' })
    .where(eq(webhookEvent.id, id));
}

async function getFacebookSocialPage(externalPageId: string) {
  const [page] = await db
    .select()
    .from(socialPage)
    .where(eq(socialPage.externalPageId, externalPageId))
    .limit(1);
  return page;
}
async function getOrCreateFacebookSocialPost(
  post: FacebookChangeValue['post'],
  page: SocialPagesSchema,
) {
  const [socialPostItem] = await db
    .select()
    .from(socialPost)
    .where(eq(socialPost.externalPostId, post.id))
    .limit(1);
  if (socialPostItem) {
    return socialPostItem;
  }

  // 根据 accessKey 获取 post 信息
  const metaPostData: FacebookPost = await getPost(
    post.id,
    page.accessToken as string,
  );
  const [newItem] = await db
    .insert(socialPost)
    .values({
      externalPageId: page.externalPageId as string,
      uid: page.uid,
      permalink: metaPostData.permalink_url,
      externalPostId: post.id,
      platform: 'facebook',
      content: metaPostData.message,
      postTime: new Date(post.updated_time),
      metadata: {},
    })
    .returning();
  return newItem;
}
async function handleFacebookCommentData(
  value: FacebookChangeValue,
  postItem: SocialPostsSchema,
) {
  const { from, message: commentMessage, comment_id, created_time } = value;
  const [commentItem] = await db
    .insert(socialComment)
    .values({
      externalPostId: postItem.externalPostId as string,
      uid: postItem.uid as string,
      externalPageId: postItem.externalPageId as string,
      platform: 'facebook',
      externalCommentId: comment_id,
      authorName: from?.name || '',
      authorId: from?.id || '',
      content: commentMessage,
      commentTime: new Date(created_time * 1000),
      isHidden: false,
      canHide: false,
      canComment: false,
      canReplyPrivately: false,
      canLike: false,
      likeCount: 0,
      userLikes: false,
      hiddenBy: '',
      postExcerpt: '',
    })
    .returning();

  return commentItem;
}

export async function handleInstagramCommentEvents(
  eventBody: InstagramWebhookBody,
  id: string,
) {
  logger.info({ eventBody, id }, 'handleInstagramCommentEvents');
  // 检查对象类型
  if (eventBody.object === 'instagram') {
    // 遍历所有entry
    for (const entry of eventBody.entry) {
      const { id: externalPageId, time } = entry;
      const pageData = await getInstagramSocialPage(externalPageId);
      if (!pageData) {
        logger.error(
          { externalPageId, time, eventBody },
          'social page not exist',
        );
        throw new Error('social page not exist');
      }
      // 遍历所有changes
      for (const change of entry.changes) {
        const { value } = change;
        // 判断 post 是否存在，不存在则新建
        const postItem = await getOrCreateInstagramSocialPost(
          value.media,
          pageData,
        );

        // 处理页面评论事件
        if (change.field === 'comments') {
          // await handleComment(change.value);
          const commentItem = await handleInstagramCommentData(
            value,
            postItem,
            time,
          );
          await anaylyzeFromInstagramComment(commentItem);
        }
      }
    }
  }
  await db
    .update(webhookEvent)
    .set({ status: 'completed' })
    .where(eq(webhookEvent.id, id));
}

async function getInstagramSocialPage(externalPageId: string) {
  const [page] = await db
    .select()
    .from(socialPage)
    .where(eq(socialPage.externalPageId, externalPageId))
    .limit(1);
  return page;
}
async function getOrCreateInstagramSocialPost(
  post: InstagramChangeValue['media'],
  page: SocialPagesSchema,
) {
  const [socialPostItem] = await db
    .select()
    .from(socialPost)
    .where(eq(socialPost.externalPostId, post.id))
    .limit(1);
  if (socialPostItem) {
    return socialPostItem;
  }

  // 根据 accessKey 获取 post 信息
  const metaPostData: InstagramMedia = await getInstagramMediaByMediaId(
    post.id,
    page.accessToken as string,
  );
  const [newItem] = await db
    .insert(socialPost)
    .values({
      externalPageId: page.externalPageId as string,
      uid: page.uid,
      permalink: metaPostData.permalink,
      externalPostId: post.id,
      platform: 'instagram',
      content: metaPostData.caption,
      postTime: new Date(metaPostData.timestamp),
      metadata: {},
    })
    .returning();
  return newItem;
}
async function handleInstagramCommentData(
  value: InstagramChangeValue,
  postItem: SocialPostsSchema,
  time: number,
) {
  const { from, text: commentMessage, id: comment_id } = value;
  const [commentItem] = await db
    .insert(socialComment)
    .values({
      externalPostId: postItem.externalPostId as string,
      uid: postItem.uid as string,
      externalPageId: postItem.externalPageId as string,
      platform: 'instagram',
      externalCommentId: comment_id,
      authorName: from?.username || '',
      authorId: from?.id || '',
      content: commentMessage,
      commentTime: new Date(time * 1000),
      isHidden: false,
      canHide: false,
      canComment: false,
      canReplyPrivately: false,
      canLike: false,
      likeCount: 0,
      userLikes: false,
      hiddenBy: '',
      postExcerpt: '',
    })
    .returning();

  return commentItem;
}

export const anaylyzeFromInstagramComment = async (
  comment: SocialCommentsSchema,
) => {
  // 隐藏分析
  const rule = await getCommentRuleByPageId(
    comment.uid,
    comment.externalPageId as string,
  );
  if (!rule) {
    throw new Error('Rule not found');
  }

  const hideResult = await processCommentByRule(
    rule,
    comment.content || '',
    comment.attachments as FacebookPostAttachment,
    comment.messageTags as FacebookPostMessageTag[],
  );

  // 情感分析
  const { emotion, reason } = await getCommentEmotion(
    comment.content as string,
  );
  logger.info(`emotion: ${emotion}, reason: ${reason}`, 'analyzeComment');

  // 如果有一个隐藏规则被击中，则隐藏评论
  const hideStatus = hideResult.length > 0;
  logger.info(
    { hideStatus, emotion, reason, uid: comment.uid },
    'analyzeComment',
  );
  // 更新评论
  await db
    .update(socialComment)
    .set({ isHidden: hideStatus, emotion, ai_reason: reason })
    .where(eq(socialComment.id, comment.id))
    .returning();
  // 扣除点数
  await deductAmount(comment.uid, commentCredits);
  return {
    hideResult,
    emotionResult: {
      emotion,
      reason,
    },
  };
};
