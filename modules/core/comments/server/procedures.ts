import { z } from 'zod';
import { protectedProcedure, createTRPCRouter } from '@/trpc/init';
import {
  fetchLatestComments,
  pageListComments,
  replyToComment,
  changeCommentLikeStatus,
  changeCommentHideStatus,
  generateCommentEmotion,
  exportCsv,
  translateComment,
  anaylyzeComment,
} from '@/modules/core/comments/service';
import { logger } from '@/utils/logger';
import { TRPCError } from '@trpc/server';

export const commentsRouter = createTRPCRouter({
  fetchLatestComments: protectedProcedure.mutation(async ({ ctx }) => {
    const uid = ctx.user.uid;
    const result = await fetchLatestComments(uid);
    return result;
  }),
  pageListComments: protectedProcedure
    .input(
      z.object({
        q: z.string().optional().describe('query keywords'),
        startDate: z.string().optional().describe('start date'),
        endDate: z.string().optional().describe('end date'),
        externalPageId: z.string().optional().describe('external page id'),
        emotion: z
          .enum(['positive', 'negative', 'neutral', 'pending'])
          .optional()
          .describe('emotion'),
        hiddenStatus: z.boolean().optional().describe('hidden status'),
        pageNo: z.number().gte(0).describe('page number'),
        pageSize: z.number().gt(0).describe('page size'),
        platform: z.string().optional().describe('platform'),
      }),
    )
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const comments = await pageListComments(uid, {
        q: input.q,
        startDate: input.startDate,
        endDate: input.endDate,
        externalPageId: input.externalPageId,
        emotion: input.emotion,
        hiddenStatus: input.hiddenStatus,
        pageNo: input.pageNo,
        pageSize: input.pageSize,
        platform: input.platform,
      });
      return comments;
    }),
  replyToComment: protectedProcedure
    .input(
      z.object({
        commentId: z.string(),
        replyText: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await replyToComment(
          uid,
          input.commentId,
          input.replyText,
        );
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '回复评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to reply to comment',
        });
      }
    }),
  changeCommentLikeStatus: protectedProcedure
    .input(
      z.object({
        commentIds: z.array(z.string()),
        likeStatus: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await changeCommentLikeStatus(
          uid,
          input.commentIds,
          input.likeStatus,
        );
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '点赞评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to change comment like status',
        });
      }
    }),
  changeCommentHideStatus: protectedProcedure
    .input(
      z.object({
        commentIds: z.array(z.string()),
        platform: z.string(),
        hiddenStatus: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await changeCommentHideStatus(
          uid,
          input.commentIds,
          input.platform,
          input.hiddenStatus,
        );
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '隐藏评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to change comment hide status',
        });
      }
    }),
  generateCommentEmotion: protectedProcedure
    .input(z.object({ commentId: z.string(), commentText: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await generateCommentEmotion(
          uid,
          input.commentId,
          input.commentText,
        );
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '生成评论情感失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate comment emotion',
        });
      }
    }),
  anaylyzeComment: protectedProcedure
    .input(z.object({ commentId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await anaylyzeComment(uid, input.commentId);
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '分析评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to analyze comment',
        });
      }
    }),
  exportCsv: protectedProcedure
    .input(
      z.object({
        q: z.string().optional(),
        platform: z.string().optional(),
        startDate: z.string().optional().describe('start date'),
        endDate: z.string().optional().describe('end date'),
        externalPageId: z.string().optional().describe('external page id'),
        emotion: z
          .enum(['positive', 'negative', 'neutral', 'pending'])
          .optional()
          .describe('emotion'),
        hiddenStatus: z.boolean().optional().describe('hidden status'),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await exportCsv(uid, {
          q: input.q,
          platform: input.platform,
          startDate: input.startDate,
          endDate: input.endDate,
          externalPageId: input.externalPageId,
          emotion: input.emotion,
          hiddenStatus: input.hiddenStatus,
        });
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '导出CSV失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to export CSV',
        });
      }
    }),
  translateComment: protectedProcedure
    .input(
      z.object({
        commentId: z.string(),
        message: z.string(),
        targetLanguage: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const comments = await translateComment(
          uid,
          input.commentId,
          input.message,
          input.targetLanguage,
        );
        return comments;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '翻译评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to translate comment',
        });
      }
    }),
});
