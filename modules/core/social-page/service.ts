import { db } from '@/db';
import {
  socialPage,
  commentRule,
  SocialPagesSchema,
  socialComment,
  commentJob,
} from '@/db/schema';
import { platformConnect } from '@/db/schema';
import {
  getFacebookPages,
  getInstagramAccounts,
  subscribeToWebhook,
} from '@/modules/core/facebook';
import { uploadFromURL } from '@/modules/storage/service';
import { and, eq, desc, inArray, sql } from 'drizzle-orm';
import { logger } from '@/utils/logger';
import { createCommentRule } from '@/modules/core/comment-rule/service';

export const refreshSocialPages = async (
  uid: string,
  platformName: 'facebook' | 'instagram',
) => {
  let newCount = 0;
  let updateCount = 0;
  const externalPageIds = [];
  const [platform] = await db
    .select()
    .from(platformConnect)
    .where(eq(platformConnect.uid, uid))
    .limit(1);

  if (!platform) {
    return [];
  }
  const { accessToken } = platform;
  if (!accessToken) {
    return [];
  }
  if (platformName === 'facebook') {
    const pages = await getFacebookPages(accessToken);
    const pageList = pages?.data.map((page) => {
      const { id, name, access_token, picture, link } = page;
      return {
        id,
        name,
        access_token,
        avatarUrl: picture?.data?.url,
        link,
      };
    });
    for (const page of pageList || []) {
      const { avatarUrl } = page;
      const [existPage] = await db
        .select()
        .from(socialPage)
        .where(
          and(
            eq(socialPage.uid, uid),
            eq(socialPage.platform, 'facebook'),
            eq(socialPage.externalPageId, page.id),
          ),
        )
        .limit(1);
      if (existPage) {
        const publicUrl = avatarUrl
          ? await uploadFromURL(avatarUrl, 'social-pages')
          : null;
        await db
          .update(socialPage)
          .set({
            avatarUrl: publicUrl,
            link: page.link,
            accessToken: page.access_token,
          })
          .where(
            and(
              eq(socialPage.uid, uid),
              eq(socialPage.platform, 'facebook'),
              eq(socialPage.externalPageId, page.id),
            ),
          )
          .returning();
        updateCount++;
      } else {
        const publicUrl = avatarUrl
          ? await uploadFromURL(avatarUrl, 'social-pages')
          : null;
        await db.insert(socialPage).values({
          uid: uid,
          platform: 'facebook',
          externalPageId: page.id,
          link: page.link,
          pageName: page.name,
          avatarUrl: publicUrl,
          accessToken: page.access_token,
          bindStatus: true,
        });
        await subscribeToWebhook(page.id, page.access_token);

        const extenralPageId = page.id;
        const [rule] = await db
          .select()
          .from(commentRule)
          .where(eq(commentRule.externalPageId, extenralPageId))
          .limit(1);
        if (!rule) {
          await createCommentRule(uid, extenralPageId);
        }
        newCount++;
        externalPageIds.push(page.id);
      }
    }
  } else if (platformName === 'instagram') {
    const instagramAccounts = await getInstagramAccounts(accessToken);
    for (const account of instagramAccounts || []) {
      const { id, name, profile_picture_url, token } = account;
      const publicUrl = profile_picture_url
        ? await uploadFromURL(profile_picture_url, 'social-pages')
        : null;
      const [existPage] = await db
        .select()
        .from(socialPage)
        .where(
          and(
            eq(socialPage.uid, uid),
            eq(socialPage.platform, 'instagram'),
            eq(socialPage.externalPageId, id),
          ),
        )
        .limit(1);
      if (existPage) {
        await db
          .update(socialPage)
          .set({
            avatarUrl: publicUrl,
            accessToken: token,
          })
          .where(
            and(
              eq(socialPage.uid, uid),
              eq(socialPage.platform, 'instagram'),
              eq(socialPage.externalPageId, id),
            ),
          )
          .returning();
        updateCount++;
      } else {
        await db.insert(socialPage).values({
          uid: uid,
          platform: 'instagram',
          externalPageId: id,
          pageName: name,
          avatarUrl: publicUrl,
          accessToken: token,
          bindStatus: true,
        });
        const extenralPageId = id;
        const [rule] = await db
          .select()
          .from(commentRule)
          .where(eq(commentRule.externalPageId, extenralPageId))
          .limit(1);
        if (!rule) {
          await createCommentRule(uid, extenralPageId);
        }
        externalPageIds.push(id);
        newCount++;
      }
    }
  }
  try {
    await createCommentJob(uid, externalPageIds);
  } catch (error) {
    logger.error(error, 'createCommentJob error');
  }
  return {
    newCount,
    updateCount,
  };
};

export const getSocialPages = async (uid: string, externalPageId?: string) => {
  const pages = await db
    .select({
      page: socialPage,
      rule: commentRule,
    })
    .from(socialPage)
    .leftJoin(
      commentRule,
      eq(socialPage.externalPageId, commentRule.externalPageId),
    )
    .where(
      and(
        eq(socialPage.uid, uid),
        externalPageId
          ? eq(socialPage.externalPageId, externalPageId)
          : undefined,
      ),
    )
    .orderBy(desc(socialPage.createdAt));
  return pages;
};

export const getPages = async (uid: string, externalPageId?: string) => {
  const pages: SocialPagesSchema[] = await db
    .select()
    .from(socialPage)
    .where(
      and(
        eq(socialPage.uid, uid),
        externalPageId
          ? eq(socialPage.externalPageId, externalPageId)
          : undefined,
      ),
    )
    .orderBy(desc(socialPage.createdAt));
  const externalPageIds = pages.map((page) => page.externalPageId as string);
  if (externalPageIds.length === 0) {
    return [];
  }
  const commentData = await db
    .select({
      externalPageId: socialComment.externalPageId,
      count: sql<number>`count(*)`,
    })
    .from(socialComment)
    .where(inArray(socialComment.externalPageId, externalPageIds))
    .groupBy(socialComment.externalPageId);
  const commentMap = new Map(
    commentData.map((item) => [item.externalPageId, item.count]),
  );
  const pagesWithCommentCount = pages.map((page) => {
    const commentCount = commentMap.get(page.externalPageId) || 0;
    return { ...page, commentCount: Number(commentCount) };
  });
  return pagesWithCommentCount;
};

export const changeBindStatus = async (
  uid: string,
  pageId: string,
  bindStatus: boolean,
) => {
  const page = await db
    .update(socialPage)
    .set({
      bindStatus: bindStatus,
    })
    .where(and(eq(socialPage.id, pageId), eq(socialPage.uid, uid)));
  return page;
};

async function createCommentJob(uid: string, externalPageIds: string[]) {
  // 创建 job
  const jobs = [];
  const nextRunAt = new Date(new Date().getTime() + 24 * 60 * 60 * 1000);
  // 先判断是否已经存在 uid 的 job
  const [job] = await db
    .select()
    .from(commentJob)
    .where(and(eq(commentJob.uid, uid), eq(commentJob.type, 'all-page')));
  if (!job) {
    // 创建
    const [newJob] = await db
      .insert(commentJob)
      .values({
        uid,
        type: 'all-page',
        status: 'running',
        nextRunAt,
      })
      .returning();
    jobs.push(newJob);
  }
  if (externalPageIds.length > 0) {
    for (const externalPageId of externalPageIds) {
      const [pageJob] = await db
        .select()
        .from(commentJob)
        .where(
          and(
            eq(commentJob.uid, uid),
            eq(commentJob.type, 'page'),
            eq(commentJob.externalPageId, externalPageId),
          ),
        );
      if (!pageJob) {
        const [newPageJob] = await db
          .insert(commentJob)
          .values({
            uid,
            externalPageId,
            type: 'page',
            status: 'running',
            nextRunAt,
          })
          .returning();
        jobs.push(newPageJob);
      }
    }
  }
  return jobs;
}
