import { z } from 'zod';
import { protectedProcedure, createTRPCRouter } from '@/trpc/init';
import {
  changeBindStatus,
  getPages,
  getSocialPages,
  refreshSocialPages,
} from '@/modules/core/social-page/service';
import { logger } from '@/utils/logger';
import { TRPCError } from '@trpc/server';

export const socialPageRouter = createTRPCRouter({
  refreshFacebookPages: protectedProcedure.mutation(async ({ ctx }) => {
    const uid = ctx.user.uid;
    try {
      const pages = await refreshSocialPages(uid, 'facebook');
      return pages;
    } catch (error) {
      logger.error({ email: ctx.user.email, error }, '刷新Facebook页面失败:');
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to refresh Facebook pages',
      });
    }
  }),
  refreshInstagramAccounts: protectedProcedure.mutation(async ({ ctx }) => {
    const uid = ctx.user.uid;
    try {
      const pages = await refreshSocialPages(uid, 'instagram');
      return pages;
    } catch (error) {
      logger.error({ email: ctx.user.email, error }, '刷新Instagram页面失败:');
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to refresh Instagram pages',
      });
    }
  }),
  getSoicalPages: protectedProcedure
    .input(
      z.object({
        externalPageId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const pages = await getSocialPages(uid, input.externalPageId);
      return pages;
    }),
  getPages: protectedProcedure
    .input(z.object({ externalPageId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const pages = await getPages(uid, input.externalPageId);
      return pages;
    }),
  changeBindStatus: protectedProcedure
    .input(
      z.object({
        pageId: z.string(),
        bindStatus: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const pageId = input.pageId;
        const bindStatus = input.bindStatus;
        const page = await changeBindStatus(uid, pageId, bindStatus);
        return page;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '绑定状态失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to change bind status',
        });
      }
    }),
});
