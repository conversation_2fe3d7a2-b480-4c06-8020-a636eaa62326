import { z } from 'zod';
import { protectedProcedure, createTRPCRouter } from '@/trpc/init';
import {
  createRecommandCommentRule,
  createCommentRule,
  deleteCommentRule,
  getCommentRuleByPageId,
  getCommentRules,
  getRuleById,
  updateCommentRule,
  processCommentRule,
} from '@/modules/core/comment-rule/service';
import { commentRuleCreateSchema } from '@/db/schema';
import { logger } from '@/utils/logger';
import { TRPCError } from '@trpc/server';
export const ruleRouter = createTRPCRouter({
  createRecommandRule: protectedProcedure
    .input(z.object({ pageIds: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const result = await createRecommandCommentRule(uid, input.pageIds);
        return result;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '创建推荐评论规则失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create recommand comment rule',
        });
      }
    }),
  createRule: protectedProcedure
    .input(z.object({ pageId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const result = await createCommentRule(uid, input.pageId);
        return result;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '创建评论规则失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create comment rule',
        });
      }
    }),
  getRuleById: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const rule = await getRuleById(uid, input.id);
      return rule;
    }),
  getRuleByPageId: protectedProcedure
    .input(
      z.object({
        pageId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const rule = await getCommentRuleByPageId(uid, input.pageId);
      return rule;
    }),
  getRules: protectedProcedure.query(async ({ ctx }) => {
    const uid = ctx.user.uid;
    const rules = await getCommentRules(uid);
    return rules;
  }),
  updateRule: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: commentRuleCreateSchema,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const rule = await updateCommentRule(
          uid,
          input.id,
          commentRuleCreateSchema.parse(input.data),
        );
        return rule;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '更新评论规则失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update comment rule',
        });
      }
    }),
  deleteRule: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const rule = await deleteCommentRule(uid, input.id);
        return rule;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '删除评论规则失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete comment rule',
        });
      }
    }),
  processCommentRule: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        message: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const rule = await processCommentRule(uid, input.id, input.message);
        return rule;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '处理评论规则失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process comment rule',
        });
      }
    }),
});
