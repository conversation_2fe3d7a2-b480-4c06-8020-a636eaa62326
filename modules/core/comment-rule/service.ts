import { db } from '@/db';
import {
  commentRule,
  CommentRuleSchema,
  CreateCommentRule,
  SocialCommentsSchema,
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { logger } from '@/utils/logger';
import { RuleProcessResult } from './type';
import {
  analyzeCommentIsNegativity,
  analyzeCommentIsProfanity,
} from '@/modules/aisdk/service/comment-analyze';
import { FacebookPostAttachment, FacebookPostMessageTag } from '../facebook';
export const createRecommandCommentRule = async (
  uid: string,
  externalPageIds: string[],
) => {
  const createItems = [];
  for (const externalPageId of externalPageIds) {
    const updateItem = await db
      .update(commentRule)
      .set({
        hideProfanityStatus: true,
        hideNegativityStatus: true,
      })
      .where(
        and(
          eq(commentRule.externalPageId, externalPageId),
          eq(commentRule.uid, uid),
        ),
      )
      .returning();
    createItems.push(updateItem);
  }
  return createItems;
};

export const createCommentRule = async (
  uid: string,
  externalPageId: string,
) => {
  const createItem = await db
    .insert(commentRule)
    .values({ uid, externalPageId })
    .returning();
  return createItem;
};

export const getRuleById = async (uid: string, id: string) => {
  const [item] = await db
    .select()
    .from(commentRule)
    .where(and(eq(commentRule.id, id), eq(commentRule.uid, uid)))
    .limit(1);
  return item;
};

export const getCommentRuleByPageId = async (
  uid: string,
  externalPageId: string,
) => {
  const [item] = await db
    .select()
    .from(commentRule)
    .where(
      and(
        eq(commentRule.uid, uid),
        eq(commentRule.externalPageId, externalPageId),
      ),
    )
    .limit(1);
  if (!item) {
    return null;
  }
  return item;
};

export const getCommentRules = async (uid: string) => {
  const items = await db
    .select()
    .from(commentRule)
    .where(eq(commentRule.uid, uid));
  return items;
};

export const updateCommentRule = async (
  uid: string,
  id: string,
  value: Omit<CreateCommentRule, 'id' | 'uid' | 'createdAt' | 'updatedAt'>,
) => {
  logger.info({ uid, id, value }, 'updateCommentRule');
  const updateItem = await db
    .update(commentRule)
    .set(value)
    .where(and(eq(commentRule.id, id), eq(commentRule.uid, uid)))
    .returning();
  return updateItem;
};

export const deleteCommentRule = async (uid: string, id: string) => {
  const deleteItem = await db
    .delete(commentRule)
    .where(and(eq(commentRule.id, id), eq(commentRule.uid, uid)))
    .returning();
  return deleteItem;
};

export const processCommentRule = async (
  uid: string,
  id: string,
  content: string | SocialCommentsSchema,
) => {
  const rule = await getRuleById(uid, id);
  if (!rule) {
    throw new Error('Rule not found');
  }
  if (typeof content === 'string') {
    return await processCommentByRule(rule, content, undefined, undefined);
  } else {
    return await processCommentByRule(
      rule,
      content.content || '',
      content.attachments as FacebookPostAttachment,
      content.messageTags as FacebookPostMessageTag[],
    );
  }
};

export const processCommentByRule = async (
  rule: CommentRuleSchema,
  content: string,
  attachments?: FacebookPostAttachment,
  messageTags?: FacebookPostMessageTag[],
): Promise<RuleProcessResult[]> => {
  const hitRule: RuleProcessResult[] = [];

  // Check for profanity if enabled
  if (rule.hideProfanityStatus && content) {
    const { isProfanity, reason } = await analyzeCommentIsProfanity(content);
    if (isProfanity) {
      hitRule.push({
        hitRule: 'hideProfanityStatus',
        reason: reason,
      });
    }
  }

  // Check for negativity if enabled
  if (rule.hideNegativityStatus && content) {
    const { isNegativity, reason } = await analyzeCommentIsNegativity(content);
    if (isNegativity) {
      hitRule.push({
        hitRule: 'hideNegativityStatus',
        reason: reason,
      });
    }
  }

  // Check for emojis and phone numbers if enabled
  if (rule.hideEmailAndPhoneNumberStatus && content) {
    const emailRegex = /^\S+@\S+\.\S+$/;
    // Phone number patterns (basic examples for different formats)
    const phoneRegex = /(\+\d{1,3}[\s-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/g;

    if (emailRegex.test(content) || phoneRegex.test(content)) {
      hitRule.push({
        hitRule: 'hideEmailAndPhoneNumberStatus',
        reason: 'Emoji or phone number found',
      });
    }
  }

  if (rule.hideImageStatus && attachments) {
    if (attachments) {
      if (
        attachments.type === 'photo' ||
        attachments.type === 'animated_image_share' ||
        attachments.type === 'sticker'
      ) {
        // end with .jpg, .jpeg, .png, .gif, .webp
        hitRule.push({
          hitRule: 'hideImageStatus',
          reason: 'Image found',
        });
      }
    } else {
      // end with .jpg, .jpeg, .png, .gif, .webp
      const imageRegex = /\.jpg$|\.jpeg$|\.png$|\.gif$|\.webp$/;
      if (imageRegex.test(content)) {
        hitRule.push({
          hitRule: 'hideImageStatus',
          reason: 'Image found',
        });
      }
    }
  }

  // Check for URLs if enabled
  if (rule.hideUrlStatus) {
    // URL regex pattern
    const urlRegex = /(https?:\/\/[^\s]+)|(www\.[^\s]+)/g;

    // Check custom URLs list if available
    let customUrls: string[] = [];
    if (rule.urls) {
      try {
        customUrls = rule.urls.split(',').map((url) => url.trim());
        // match
      } catch {
        // Handle parsing error
      }
    }

    const hasUrl =
      urlRegex.test(content) ||
      customUrls.some((url) =>
        content.toLowerCase().includes(url.toLowerCase()),
      );

    if (hasUrl) {
      hitRule.push({
        hitRule: 'hideUrlStatus',
        reason: 'URL found',
      });
    }
  }

  // Check for mentions if enabled
  if (rule.hideMentionStatus) {
    if (messageTags) {
      if (messageTags.find((messageTag) => messageTag.type === 'user')) {
        hitRule.push({
          hitRule: 'hideMentionStatus',
          reason: 'Mention found',
        });
      }
    } else {
      // Mention pattern (@username)
      const mentionRegex = /@\w+/g;
      if (mentionRegex.test(content)) {
        hitRule.push({
          hitRule: 'hideMentionStatus',
          reason: 'Mention found',
        });
      }
    }
  }

  // Check for hashtags if enabled
  if (rule.hideHashTags && rule.tags) {
    // Check custom tags if available
    let customTags: string[] = [];
    if (rule.tags) {
      try {
        customTags = rule.tags.split(',').map((tag) => tag.trim());
      } catch {
        // Handle parsing error
      }
    }

    const hasHashtag = customTags.some((tag) =>
      content.toLowerCase().includes(tag.toLowerCase()),
    );

    if (hasHashtag) {
      hitRule.push({
        hitRule: 'hideHashTags',
        reason: 'Hashtag found',
      });
    }
  }

  // Check for emojis if enabled (separate from phone numbers check)
  if (rule.hideEmojisStatus) {
    // Emoji regex pattern
    const emojiRegex =
      /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu;

    // Check custom emojis if available
    let customEmojis: string[] = [];
    if (rule.emojis) {
      try {
        customEmojis = rule.emojis.split(',').map((emoji) => emoji.trim());
      } catch {
        // Handle parsing error
      }
    }

    const hasEmoji =
      emojiRegex.test(content) ||
      customEmojis.some((emoji) => content.includes(emoji));

    if (hasEmoji) {
      hitRule.push({
        hitRule: 'hideEmojisStatus',
        reason: 'Emoji found',
      });
    }
  }

  // Check for keywords if enabled
  if (rule.hideKeywordsStatus && rule.keywords) {
    let keywordsList: string[] = [];
    try {
      keywordsList = rule.keywords.split(',').map((keyword) => keyword.trim());
    } catch {
      // Handle parsing error
    }

    if (
      keywordsList.some((keyword) =>
        new RegExp(`\\b${keyword}\\b`, 'i').test(content),
      )
    ) {
      hitRule.push({
        hitRule: 'hideKeywordsStatus',
        reason: 'Keyword found',
      });
    }
  }

  // Check if all comments should be hidden
  if (rule.hideAllCommentsStatus) {
    hitRule.push({
      hitRule: 'hideAllCommentsStatus',
      reason: 'All comments should be hidden',
    });
  }

  return hitRule;
};
