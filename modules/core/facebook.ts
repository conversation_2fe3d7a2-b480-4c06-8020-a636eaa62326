/**
 * Facebook API客户端
 * 用于与Facebook Graph API交互
 */

import { logger } from '@/utils/logger';

// Facebook Graph API基础URL
const GRAPH_API_BASE_URL = 'https://graph.facebook.com/v22.0';

// 评论详情接口
interface CommentDetails {
  id: string;
  message?: string;
  from?: {
    id: string;
    name: string;
  };
  created_time: string;
  error?: {
    message: string;
    type: string;
    code: number;
  };
}

// Facebook 页面接口
export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category?: string;
  tasks?: string[];
  link?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface FacebookPostAttachment {
  media?: {
    image?: {
      src: string;
      width: number;
      height: number;
    };
  };
  target?: {
    id: string;
    url: string;
  };
  type?: string;
  url?: string;
}

export interface FacebookPostMessageTag {
  id: string;
  length: number;
  offset: number;
  name: string;
  type?: string;
}

export interface FacebookPost {
  id: string;
  message?: string;
  permalink_url?: string;
  created_time: string;
  from?: {
    name: string;
    id: string;
  };
  full_picture?: string;
  comments?: {
    data: {
      id: string;
      message: string;
      from?: {
        name: string;
        id: string;
      };
      attachment?: FacebookPostAttachment;
      message_tags?: FacebookPostMessageTag[];
      created_time: string;
      is_hidden?: boolean;
      can_hide?: boolean;
      can_comment?: boolean;
      can_reply_privately?: boolean;
      can_like?: boolean;
      can_remove?: boolean;
      like_count?: number;
      user_likes?: boolean;
      permalink_url?: string;
    }[];
    paging?: {
      cursors: {
        before: string;
        after: string;
      };
    };
  };
}

// API响应接口
export interface PagesResponse {
  data: FacebookPage[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
    next?: string;
  };
  error?: unknown;
}

// Facebook 评论接口
export interface FacebookComment {
  id: string;
  message: string;
  created_time: string;
  from: {
    name: string;
    id: string;
  };
  attachment?: FacebookPostAttachment;
  message_tags?: FacebookPostMessageTag[];
  permalink_url?: string;
  can_reply: boolean;
  can_hide: boolean;
  can_like: boolean;
  is_hidden: boolean;
  can_comment: boolean;
  can_reply_privately: boolean;
  is_private: boolean;
  like_count: number;
  user_likes: boolean;
}

// Instagram 账号接口
export interface InstagramAccount {
  id: string;
  name: string;
  username: string;
  profile_picture_url?: string;
  token?: string;
}

// Facebook 页面与 Instagram 账号接口
export interface FacebookPageWithInstagram {
  id: string;
  name: string;
  access_token: string;
  instagram_business_account?: {
    id: string;
    username: string;
    name?: string;
    profile_picture_url?: string;
  };
}

// Instagram 媒体接口
export interface InstagramMedia {
  id: string;
  caption?: string;
  media_type: string;
  media_url?: string;
  permalink: string;
  thumbnail_url?: string;
  timestamp: string;
}

// Instagram 评论接口
export interface InstagramComment {
  id: string;
  text: string;
  timestamp: string;
  username: string;
  like_count: number;
  hidden: boolean;
  user_has_liked: boolean;
}

// 获取长期用户访问口令
export async function getLongLivedUserToken(
  appId: string,
  appSecret: string,
  shortLivedToken: string,
): Promise<{ access_token?: string; expires_in?: number; error?: unknown }> {
  try {
    const url = `${GRAPH_API_BASE_URL}/oauth/access_token?grant_type=fb_exchange_token&client_id=${appId}&client_secret=${appSecret}&fb_exchange_token=${shortLivedToken}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      logger.error(errorData, '获取长期用户访问口令失败:');
      return { error: errorData };
    }

    const data = await response.json();
    return {
      access_token: data.access_token,
      expires_in: data.expires_in,
    };
  } catch (error) {
    logger.error(error, '获取长期用户访问口令时出错:');
    return { error };
  }
}

// 获取长期页面访问口令
export async function getLongLivedPageTokens(
  longLivedUserToken: string,
): Promise<PagesResponse | null> {
  try {
    const url = `${GRAPH_API_BASE_URL}/me/accounts?access_token=${longLivedUserToken}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      logger.error(errorData, '获取长期页面访问口令失败:');
      return { data: [], error: errorData };
    }

    const data: PagesResponse = await response.json();
    return data;
  } catch (error) {
    logger.error(error, '获取长期页面访问口令时出错:');
    return {
      data: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 获取 Facebook 页面列表
export async function getFacebookPages(
  accessToken: string,
): Promise<PagesResponse | null> {
  const url = `${GRAPH_API_BASE_URL}/me/accounts?fields=id,name,access_token,category,tasks,picture,link&access_token=${accessToken}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json();
      logger.error(errorData, '获取Facebook页面失败:');
      return { data: [], error: errorData };
    }
    const data: PagesResponse = await response.json();
    return data;
  } catch (error) {
    logger.error(error, '获取Facebook页面失败:');
    return {
      data: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 获取页面的帖子
export async function getPagePosts(
  pageId: string,
  pageAccessToken: string,
): Promise<FacebookPost[]> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${pageId}/feed?fields=id,message,created_time,permalink_url,from,full_picture,comments{id,message,from,created_time,is_hidden,can_hide,can_comment,can_reply_privately,can_like,can_remove,likes,like_count,user_likes,attachment,message_tags,permalink_url}&access_token=${pageAccessToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch page posts');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    logger.error(error, '获取页面帖子失败:');
    throw error;
  }
}

export async function getPost(
  postId: string,
  pageAccessToken: string,
): Promise<FacebookPost> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${postId}?fields=id,message,created_time,from,full_picture,attachments,permalink_url&access_token=${pageAccessToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch comments');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    logger.error(error, '获取帖子评论失败:');
    throw error;
  }
}

// 获取帖子的评论
export async function getPostComments(
  postId: string,
  pageAccessToken: string,
): Promise<FacebookComment[]> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${postId}/comments?fields=id,message,created_time,from,can_reply,can_hide,can_like,can_comment,can_reply_privately,is_hidden,is_private,like_count,user_likes,attachment,message_tags,permalink_url&access_token=${pageAccessToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch comments');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    logger.error(error, '获取帖子评论失败:');
    throw error;
  }
}

// 给评论点赞
export async function likeComment(
  commentId: string,
  pageToken: string,
  likeStatus: boolean,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}/likes?access_token=${pageToken}`,
      {
        method: likeStatus ? 'POST' : 'DELETE',
      },
    );

    return response.ok;
  } catch (error) {
    logger.error(error, '给评论点赞失败:');
    return false;
  }
}

// 隐藏/显示评论
export async function hideComment(
  commentId: string,
  is_hidden: boolean,
  pageToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}?is_hidden=${is_hidden}&access_token=${pageToken}`,
      {
        method: 'POST',
      },
    );
    const data = await response.json();
    logger.info(data, '隐藏/显示评论:');
    return response.ok;
  } catch (error) {
    logger.error(error, '隐藏/显示评论失败:');
    return false;
  }
}

// 回复评论
export async function replyFacebookComment(
  commentId: string,
  message: string,
  pageAccessToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}/comments`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          access_token: pageAccessToken,
        }),
      },
    );

    const data = await response.json();

    if (data.id) {
      logger.info(data, '回复评论成功:');
      return true;
    }

    logger.error(data, '回复评论失败:');
    return false;
  } catch (error) {
    logger.error(error, '回复评论时出错:');
    return false;
  }
}

// 获取评论详情
export async function getCommentDetails(
  commentId: string,
  pageAccessToken: string,
): Promise<CommentDetails | null> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}?fields=message,from,created_time&access_token=${pageAccessToken}`,
    );

    return await response.json();
  } catch (error) {
    logger.error(error, '获取评论详情时出错:');
    return null;
  }
}

// 获取 Instagram 账号
export async function getInstagramAccounts(
  accessToken: string,
): Promise<InstagramAccount[]> {
  const url = `${GRAPH_API_BASE_URL}/me/accounts?fields=name,access_token,instagram_business_account{id,name,username,profile_picture_url}&access_token=${accessToken}`;
  logger.info(url, '获取Instagram账号:');
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json();
      logger.error(errorData, '获取Instagram账号失败:');
      return [];
      // throw new Error(`Facebook API error: ${JSON.stringify(errorData)}`);
    }

    const data: { data: FacebookPageWithInstagram[] } = await response.json();

    // Filter pages with Instagram accounts and extract Instagram account data
    const instagramAccounts: InstagramAccount[] = [];

    data.data.forEach((page) => {
      if (page.instagram_business_account) {
        instagramAccounts.push({
          id: page.instagram_business_account.id,
          name: page.instagram_business_account.name || page.name,
          username: page.instagram_business_account.username,
          profile_picture_url:
            page.instagram_business_account.profile_picture_url,
          token: page.access_token,
        });
      }
    });

    return instagramAccounts;
  } catch (error) {
    logger.error(error, '获取Instagram账号失败:');
    return [];
  }
}

// 获取 Instagram 媒体
export async function getInstagramMediaByMediaId(
  mediaId: string,
  accountToken: string,
): Promise<InstagramMedia> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${mediaId}?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp&access_token=${accountToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch Instagram media');
    }

    const data = await response.json();
    return data || {};
  } catch (error) {
    logger.error(error, '获取Instagram媒体失败:');
    throw error;
  }
}

// 获取 Instagram 媒体
export async function getInstagramMedia(
  accountId: string,
  accountToken: string,
): Promise<InstagramMedia[]> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${accountId}/media?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp&access_token=${accountToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch Instagram media');
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    logger.error(error, '获取Instagram媒体失败:');
    throw error;
  }
}

// 获取 Instagram 媒体的评论
export async function getInstagramComments(
  mediaId: string,
  accountToken: string,
): Promise<InstagramComment[]> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${mediaId}/comments?fields=id,text,timestamp,username,like_count,hidden,user_has_liked&access_token=${accountToken}`,
    );

    if (!response.ok) {
      throw new Error('Failed to fetch Instagram comments');
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    logger.error(error, '获取Instagram评论失败:');
    throw error;
  }
}

// 给 Instagram 评论点赞
export async function likeInstagramComment(
  commentId: string,
  accountToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}/like?access_token=${accountToken}`,
      {
        method: 'POST',
      },
    );

    return response.ok;
  } catch (error) {
    logger.error(error, '给Instagram评论点赞失败:');
    return false;
  }
}

// 隐藏/显示 Instagram 评论
export async function hideInstagramComment(
  commentId: string,
  is_hidden: boolean,
  accountToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}?hide=${is_hidden}&access_token=${accountToken}`,
      {
        method: 'POST',
      },
    );

    return response.ok;
  } catch (error) {
    logger.error(error, '隐藏/显示Instagram评论失败:');
    return false;
  }
}

// 回复 Instagram 评论
export async function replyInstagramComment(
  commentId: string,
  message: string,
  accountToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${commentId}/replies?message=${encodeURIComponent(
        message,
      )}&access_token=${accountToken}`,
      {
        method: 'POST',
      },
    );

    return response.ok;
  } catch (error) {
    logger.error(error, '回复Instagram评论失败:');
    return false;
  }
}

// 设置Webhook订阅
export async function subscribeToWebhook(
  pageId: string,
  accessToken: string,
): Promise<boolean> {
  try {
    const formData = new FormData();
    formData.append('subscribed_fields', 'feed');
    formData.append('access_token', accessToken);

    const response = await fetch(
      `${GRAPH_API_BASE_URL}/${pageId}/subscribed_apps`,
      {
        method: 'POST',
        body: formData,
      },
    );
    const data = await response.json();

    logger.info(data, '订阅Webhook回调信息:');
    if (data.success) {
      logger.info(data, '成功订阅Webhook:');
      return true;
    }

    logger.error(data, '订阅Webhook失败:');
    return false;
  } catch (error) {
    logger.error(error, '订阅Webhook时出错:');
    return false;
  }
}
