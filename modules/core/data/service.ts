import { and, asc, count, desc, eq, sql } from 'drizzle-orm';
import { commentInsight, socialComment } from '@/db/schema';
import { db } from '@/db';
import { logger } from '@/utils/logger';

export type CommentData = {
  pageId?: string;
  summary: {
    total: number;
    increaseNumber: number;
    positiveNumber: number;
    negativeNumber?: number;
    neutralNumber?: number;
  };
  emotions: {
    totalPositive: number;
    increasePositive: number;
    totalNegative: number;
    increaseNegative: number;
    totalNeutral: number;
    increaseNeutral: number;
  };
  commentsData: {
    date: string;
    total: number;
    positiveNumber: number;
    negativeNumber: number;
    neutralNumber: number;
  }[];
  insights?: string[];
};

export const platformData = async (uid: string, externalPageId?: string) => {
  logger.info({ uid, externalPageId }, 'get platform data');
  const condition = and(
    eq(socialComment.uid, uid),
    externalPageId
      ? eq(socialComment.externalPageId, externalPageId)
      : undefined,
  );
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  // remove time
  const yesterdayCondition = and(
    eq(socialComment.uid, uid),
    externalPageId
      ? eq(socialComment.externalPageId, externalPageId)
      : undefined,
    eq(sql`date(${socialComment.commentTime})`, yesterday.toDateString()),
  );

  // find comments group by createdAt as date
  const comments = await db
    .select({
      date: sql<string>`date(${socialComment.commentTime})`,
      total: count(),
      positiveNumber: sql<number>` count(*) filter (where emotion = 'positive')`,
      negativeNumber: sql<number>` count(*) filter (where emotion = 'negative')`,
      neutralNumber: sql<number>` count(*) filter (where emotion = 'neutral')`,
    })
    .from(socialComment)
    .where(condition)
    .groupBy(sql<string>`date(${socialComment.commentTime})`)
    .orderBy(asc(sql<string>`date(${socialComment.commentTime})`));
  // find yesterday increase comments count
  const [yesterdayComments] = await db
    .select({
      date: sql<string>`date(${socialComment.commentTime})`,
      total: count(),
      positiveNumber: sql<number>` count(*) filter (where emotion = 'positive')`,
      negativeNumber: sql<number>` count(*) filter (where emotion = 'negative')`,
      neutralNumber: sql<number>` count(*) filter (where emotion = 'neutral')`,
    })
    .from(socialComment)
    .where(yesterdayCondition)
    .groupBy(sql<string>`date(${socialComment.commentTime})`)
    .orderBy(asc(sql<string>`date(${socialComment.commentTime})`));

  const [totalComments] = await db
    .select({
      total: count(),
      positiveNumber: sql<number>` count(*) filter (where emotion = 'positive')`,
      negativeNumber: sql<number>` count(*) filter (where emotion = 'negative')`,
      neutralNumber: sql<number>` count(*) filter (where emotion = 'neutral')`,
    })
    .from(socialComment)
    .where(condition);
  const [insight] = await db
    .select()
    .from(commentInsight)
    .where(
      and(
        eq(commentInsight.uid, uid),
        externalPageId
          ? eq(commentInsight.externalPageId, externalPageId)
          : undefined,
        externalPageId ? undefined : eq(commentInsight.fullInsight, true),
      ),
    )
    .orderBy(desc(commentInsight.createdAt))
    .limit(1);
  const result: CommentData = {
    pageId: externalPageId,
    summary: {
      total: totalComments ? Number(totalComments.total) : 0,
      increaseNumber: yesterdayComments ? Number(yesterdayComments.total) : 0,
      positiveNumber: totalComments ? Number(totalComments.positiveNumber) : 0,
    },
    emotions: {
      totalPositive: totalComments ? Number(totalComments.positiveNumber) : 0,
      increasePositive: yesterdayComments
        ? Number(yesterdayComments.positiveNumber)
        : 0,
      totalNegative: totalComments ? Number(totalComments.negativeNumber) : 0,
      increaseNegative: yesterdayComments
        ? Number(yesterdayComments.negativeNumber)
        : 0,
      totalNeutral: totalComments ? Number(totalComments.neutralNumber) : 0,
      increaseNeutral: yesterdayComments
        ? Number(yesterdayComments.neutralNumber)
        : 0,
    },
    commentsData: comments,
    insights: insight?.insight?.split('\n') ?? [],
  };
  return result;
};
