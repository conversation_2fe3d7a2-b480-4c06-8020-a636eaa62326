import { z } from 'zod';
import { protectedProcedure, createTRPCRouter } from '@/trpc/init';
import { platformData } from '@/modules/core/data/service';

export const dataRouter = createTRPCRouter({
  platformData: protectedProcedure
    .input(z.object({ externalPageId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const result = await platformData(uid, input.externalPageId);
      return result;
    }),
});
