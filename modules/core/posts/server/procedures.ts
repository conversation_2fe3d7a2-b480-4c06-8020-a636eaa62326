import { protectedProcedure, createTRPCRouter } from '@/trpc/init';
import { getPosts, fetchCommentsByPostId } from '@/modules/core/posts/service';
import { z } from 'zod';
import { logger } from '@/utils/logger';
import { TRPCError } from '@trpc/server';

export const socialPostRouter = createTRPCRouter({
  getPosts: protectedProcedure
    .input(
      z.object({
        pageNo: z.number().gte(0),
        pageSize: z.number().gt(0),
        platform: z.string().optional(),
        q: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      const posts = await getPosts(uid, {
        pageNo: input.pageNo,
        pageSize: input.pageSize,
        platform: input.platform,
        q: input.q,
      });
      return posts;
    }),
  fetchComments: protectedProcedure
    .input(
      z.object({
        externalPostId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uid = ctx.user.uid;
      try {
        const result = await fetchCommentsByPostId(uid, input.externalPostId);
        return result;
      } catch (error) {
        logger.error({ email: ctx.user.email, error }, '获取评论失败:');
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch comments',
        });
      }
    }),
});
