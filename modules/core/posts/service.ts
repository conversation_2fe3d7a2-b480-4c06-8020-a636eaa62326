import { db } from '@/db';
import { and, like, eq, count, desc, sql } from 'drizzle-orm';
import {
  socialComment,
  socialPage,
  SocialPagesSchema,
  socialPost,
  SocialPostWithPage,
} from '@/db/schema';
import {
  getInstagramComments,
  getInstagramMedia,
  getPagePosts,
  getPostComments,
} from '@/modules/core/facebook';
import { uploadFromURL } from '@/modules/storage/service';
import { logger } from '@/utils/logger';

export const getPosts = async (
  uid: string,
  param: {
    q: string | undefined;
    pageNo: number;
    pageSize: number;
    platform: string | undefined;
  },
) => {
  const { q, pageNo, pageSize, platform } = param;
  const condition = and(
    eq(socialPost.uid, uid),
    q ? like(socialPost.content, `%${q}%`) : undefined,
    platform ? eq(socialPost.platform, platform) : undefined,
  );
  const [total] = await db
    .select({ count: count() })
    .from(socialPost)
    .innerJoin(
      socialPage,
      eq(socialPost.externalPageId, socialPage.externalPageId),
    )
    .where(condition);
  const postsData = await db
    .select({
      post: socialPost,
      page: socialPage,
      commentCount: count(socialComment.id),
      positiveComments: sql<number>`count(*) filter (where emotion = 'positive')`,
      negativeComments: sql<number>`count(*) filter (where emotion = 'negative')`,
    })
    .from(socialPost)
    .innerJoin(
      socialPage,
      eq(socialPost.externalPageId, socialPage.externalPageId),
    )
    .leftJoin(
      socialComment,
      eq(socialPost.externalPostId, socialComment.externalPostId),
    )
    .where(condition)
    .groupBy(socialPost.id, socialPage.id)
    .orderBy(desc(socialPost.postTime))
    .limit(pageSize)
    .offset(pageNo * pageSize);
  return {
    data: postsData.map((item) => ({
      post: item.post,
      page: item.page,
      commentCount: item.commentCount,
      positiveComments: item.positiveComments,
      negativeComments: item.negativeComments,
    })),
    total: total.count,
    pageNo,
    pageSize,
  };
};

export const fetchCommentsByPostId = async (
  uid: string,
  externalPostId: string,
) => {
  // 根据postID 和 uid 去查询当前的post
  const [item]: SocialPostWithPage[] = await db
    .select({
      post: socialPost,
      page: socialPage,
    })
    .from(socialPost)
    .innerJoin(
      socialPage,
      eq(socialPost.externalPageId, socialPage.externalPageId),
    )
    .where(
      and(
        eq(socialPost.uid, uid),
        eq(socialPost.externalPostId, externalPostId),
      ),
    )
    .limit(1);

  if (item) {
    if (item.post.platform === 'facebook') {
      await fetchCommentsFromFacebookByPostId(item, externalPostId);
    } else if (item.post.platform === 'instagram') {
      await fetchCommentsFromInstagramByPostId(item, externalPostId);
    }
  }
};

async function fetchCommentsFromFacebookByPostId(
  item: SocialPostWithPage,
  externalPostId: string,
) {
  // 获取当前页面pageToken
  // 根据postID 去获取 comment
  const comments = await getPostComments(
    externalPostId,
    (item.page.accessToken || '') as string,
  );

  // 遍历每个comment
  for (const commentFromPlatform of comments) {
    const externalCommentId = commentFromPlatform.id;
    // 根据 commentId 判断数据库里是否存在
    const [comment] = await db
      .select()
      .from(socialComment)
      .where(eq(socialComment.externalCommentId, externalCommentId))
      .limit(1);
    if (!comment) {
      // 如果不存在，就保存
      console.log(`开始保存comment: ${JSON.stringify(commentFromPlatform)}`);
      await db
        .insert(socialComment)
        .values({
          externalPostId: item.post.externalPostId as string,
          uid: item.post.uid,
          externalPageId: item.page.externalPageId as string,
          platform: 'facebook',
          externalCommentId: externalCommentId,
          authorName: commentFromPlatform.from?.name || '',
          authorId: commentFromPlatform.from?.id || '',
          content: commentFromPlatform.message,
          commentTime: new Date(commentFromPlatform.created_time),
          isHidden: commentFromPlatform.is_hidden || false,
          canHide: commentFromPlatform.can_hide || false,
          canComment: commentFromPlatform.can_comment || false,
          canReplyPrivately: commentFromPlatform.can_reply_privately || false,
          canLike: commentFromPlatform.can_like || false,
          likeCount: commentFromPlatform.like_count || 0,
          userLikes: commentFromPlatform.user_likes || false,
          hiddenBy: '',
          postExcerpt: '',
          attachments: commentFromPlatform.attachment,
          messageTags: commentFromPlatform.message_tags,
          permalinkUrl: commentFromPlatform.permalink_url,
        })
        .returning();
    }
  }
}
async function fetchCommentsFromInstagramByPostId(
  item: SocialPostWithPage,
  postId: string,
) {
  // 获取当前页面pageToken
  // 根据postID 去获取 comment
  const postComments = await getInstagramComments(
    postId,
    (item.page.accessToken || '') as string,
  );

  for (const comment of postComments || []) {
    const {
      id,
      text,
      timestamp,
      username,
      like_count,
      hidden,
      user_has_liked,
    } = comment;
    // Check if comment exists
    const [existingComment] = await db
      .select()
      .from(socialComment)
      .where(eq(socialComment.externalCommentId, id));
    if (existingComment) {
      console.log(
        `Instagram comment already exists: ${JSON.stringify(existingComment)}`,
      );
    } else {
      // Save to socialComment table
      console.log(`Saving Instagram comment: ${JSON.stringify(comment)}`);
      await db
        .insert(socialComment)
        .values({
          externalPostId: item.post.externalPostId as string,
          uid: item.post.uid,
          platform: 'instagram',
          externalPageId: item.page.externalPageId as string,
          externalCommentId: id,
          authorName: username || '',
          authorId: '', // Instagram doesn't provide user_id
          content: text,
          commentTime: new Date(timestamp),
          isHidden: hidden || false,
          canHide: true,
          canComment: true,
          canReplyPrivately: false,
          canLike: true,
          likeCount: like_count || 0,
          userLikes: user_has_liked || false,
          hiddenBy: '',
          postExcerpt: '',
        })
        .returning();
    }
  }
}

export const fetchPosts = async (uid: string) => {
  const pages: SocialPagesSchema[] = await db
    .select()
    .from(socialPage)
    .where(and(eq(socialPage.uid, uid), eq(socialPage.bindStatus, true)));
  let totalPostCount = 0;
  for (const page of pages) {
    const platform = page.platform;
    if (platform === 'facebook') {
      const { facbookPostCount } = await getOrUpdateFacebookPost(page, uid);
      totalPostCount += facbookPostCount;
    } else if (platform === 'instagram') {
      const { instagramPostCount } = await getOrUpdateInstagramPost(page, uid);
      totalPostCount += instagramPostCount;
    }
  }
  return {
    totalPostCount,
  };
};

const getOrUpdateFacebookPost = async (
  page: SocialPagesSchema,
  uid: string,
) => {
  const posts = await getPagePosts(
    page.externalPageId as string,
    page.accessToken as string,
  );
  let facbookPostCount = 0;
  for (const post of posts) {
    const { id, message, created_time, full_picture, permalink_url } = post;
    // 保存到 socialPost 表
    const picturePublicURL = full_picture
      ? await uploadFromURL(full_picture)
      : null;
    // 查询是否存在
    const [existingPost] = await db
      .select()
      .from(socialPost)
      .where(eq(socialPost.externalPostId, id));
    if (existingPost) {
      console.log(`post已存在: ${JSON.stringify(existingPost)}`);
    } else {
      console.log(`开始保存post: ${JSON.stringify(post)}`);
      const [newPost] = await db
        .insert(socialPost)
        .values({
          externalPageId: page.externalPageId as string,
          uid: uid,
          permalink: permalink_url,
          externalPostId: id,
          platform: 'facebook',
          content: message,
          postTime: new Date(created_time),
          metadata: {
            picturePublicURL,
          },
        })
        .returning();
      if (newPost) {
        facbookPostCount++;
      }
    }
  }
  return {
    facbookPostCount,
  };
};

async function getOrUpdateInstagramPost(page: SocialPagesSchema, uid: string) {
  const media = await getInstagramMedia(
    page.externalPageId as string,
    page.accessToken as string,
  );
  let instagramPostCount = 0;

  for (const post of media || []) {
    const { id, caption, timestamp, media_url, permalink } = post;
    // Save to socialPost table
    const picturePublicURL = media_url ? await uploadFromURL(media_url) : null;
    // Check if post exists
    const [existingPost] = await db
      .select()
      .from(socialPost)
      .where(eq(socialPost.externalPostId, id));
    if (existingPost) {
      logger.info(
        `Instagram post already exists: ${JSON.stringify(existingPost)}`,
      );
    } else {
      logger.info(`Saving Instagram post: ${JSON.stringify(post)}`);
      const [newPost] = await db
        .insert(socialPost)
        .values({
          externalPageId: page.externalPageId as string,
          uid,
          permalink,
          externalPostId: id,
          platform: 'instagram',
          content: caption,
          postTime: new Date(timestamp),
          metadata: {
            picturePublicURL,
          },
        })
        .returning();
      if (newPost) {
        instagramPostCount++;
      }
    }
  }
  return {
    instagramPostCount,
  };
}
