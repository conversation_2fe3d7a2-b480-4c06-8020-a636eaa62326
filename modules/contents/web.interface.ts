export interface BaseResponse<T> {
  code?: number;
  message?: string;
  total?: number;
  data: T;
}
export interface BaseResponsePage<T> {
  message: string;
  code: number;
  total: number;
  data: T;
  has_next: boolean;
}
export interface HomeArticleModel {
  article_id: number;
  title: string;
  description: string;
  cover_image: string;
  content_url: string;
  create_time: string;
  labels?: [string];
  type?: string | string[];
  title_id: string;
  article_button?: string;
}

export interface BlogArticleQueryParamsModel {
  page_no: number;
  page_size: number;
  type?: string | string[];
}

export interface HomeThemeModel {
  main_image: string;
  background_image: string;
  classifies: Classify[];
}

export interface Classify {
  k: string;
  v: string;
}

export interface HomeThemeClassifyModel {
  id?: number;
  name?: string;
  image?: string;
}

export interface BlogDetailContentModel {
  image_url?: string;
  title?: string;
  body?: string;
  introduction?: string;
}

export interface IUserInfoInterface {
  user: LoginUser;
  platforms: Platform[];
}

export interface LoginUser {
  id: string;
  uid: string;
  email: string;
  avatar: string;
  name: number;
  stripeCustomerId: string;
  subscriptionStatus: string;
  subscriptionExpiresAt: string;
  amount: number;
  createdAt: string;
  updatedAt: string;
  currentPlan: string;
  remainAmount: number;
}

export interface Platform {
  id: string;
  uid: string;
  platform: string;
  identityId: string;
  accountName: string;
  email: string;
  externalId: string;
  accessToken: string;
  refreshToken: string;
  userLongLivedToken: string;
  pageLongLivedToken: string;
  tokenExpiry: string;
  createdAt: string;
  updatedAt: string;
}

export interface PlatformPageRoot {
  page: PlatformPage;
  rule: PlatformPageRule | null;
}

export interface PlatformPage {
  id: string;
  uid: string;
  platform: string;
  link: string;
  externalPageId: string;
  pageName: string;
  bindStatus: boolean;
  avatarUrl: string;
  accessToken: string;
  createdAt: string;
  updatedAt: string;
}

export interface PlatformPageRule {
  id: string;
  uid: string;
  externalPageId: string;
  hideProfanityStatus: boolean;
  hideNegativityStatus: boolean;
  hideEmojiAndPhoneNumberStatus: boolean;
  hideImageStatus: boolean;
  hideUrlStatus: boolean;
  urls: string[];
  hideMentionStatus: boolean;
  hideHashTags: boolean;
  tags: string;
  hideEmojisStatus: boolean;
  emojis: string;
  hideKeywordsStatus: boolean;
  keywords: string[];
  hideAllCommentsStatus: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Account {
  uid: string;
  link: string | null;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  platform: string;
  accessToken: string | null;
  externalPageId: string | null;
  pageName: string | null;
  bindStatus: boolean;
  avatarUrl: string | null;
  commentCount: number;
}

export interface TdkResponse {
  title: string;
  description: string;
  keywords: string;
}
export interface LandingResponse {
  faqList: FaqList;
  function_child: string;
  contentInfo: ContentInfo;
  blogList: BlogList[];
  topBlogList: BlogList[];
}

export interface BlogList {
  date: string;
  isTop: boolean;
  articleId: number;
  title_id: string;
  cover_image: string;
  title: string;
}

export interface ContentInfo {
  'section-features': SectionFeatures;
  'section-why': SectionWhy;
  'section-branding': SectionBranding;
  'section-function': SectionFunction;
  'section-testimonial': SectionTestimonial;
  'section-blog': SectionBlog;
  'section-how': SectionHow;
  hero: Hero;
  'section-faq': SectionFaq;
  'section-top-blog': SectionTopBlog;
}

export interface SectionTopBlog {
  title: string;
  desc: string;
}
export interface FaqList {
  content_list: ContentList[];
  has_category: boolean;
}

export interface ContentList {
  answer: string;
  question: string;
  id: number;
}
export interface SectionFeatures {
  cards: Card[];
  imgPath: string;
  title: string;
  desc: string;
}

export interface Card {
  imgPath: string;
  title: string;
  desc: string;
  tags: string[];
}

export interface SectionWhy {
  cards: Card2[];
  imgPath: string;
  title: string;
  desc: string;
}

export interface Card2 {
  imgPath: string;
  title: string;
  desc: string;
}

export interface SectionBranding {
  cards: Card3[];
  imgPath: string;
  title: string;
  desc: string;
}

export interface Card3 {
  imgPath: string;
  title: string;
  desc: string;
}

export interface SectionFunction {
  cards: Card4[];
  imgPath: string;
  title: string;
  desc: string;
}

export interface Card4 {
  isAI: string;
  imgPath: string;
  title: string;
  desc: string;
}

export interface SectionTestimonial {
  cards: Card5[];
  title: string;
  desc: string;
}

export interface Card5 {
  date: string;
  subTitle: string;
  heading: string;
  imgPath: string;
  title: string;
  points: string;
  desc: string;
}

export interface SectionBlog {
  title: string;
  desc: string;
}

export interface SectionHow {
  cards: Card6[];
  imgPath: string;
  title: string;
  desc: string;
}

export interface Card6 {
  title: string;
  desc: string;
  imgPath: string;
}

export interface Hero {
  highlight: string;
  buttonText: string;
  imgPath: string;
  title: string;
  desc: string;
  extraInfo: string;
}

export interface SectionFaq {
  title: string;
  desc: string;
}
