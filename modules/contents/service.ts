import {
  BaseResponse,
  HomeArticleModel,
  BlogArticleQueryParamsModel,
  HomeThemeModel,
  BaseResponsePage,
  BlogDetailContentModel,
  TdkResponse,
  LandingResponse,
} from './web.interface';
import { ISitemapField } from 'next-sitemap';
import axios from 'axios';
import { env } from '@/env';
// 顶部文章
export async function getCategoryTopList(
  params?: { type: string | string[] },
  locale?: string,
) {
  const res = await axios.get<BaseResponse<[HomeArticleModel]>>(
    `/article/list/top`,
    {
      params,
      headers: {
        'Current-Language': locale,
        tenantId: `${env.NEXT_APP_TENANTID}`,
      },
      baseURL: env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}

// 文章列表
export async function getArticleListData(
  params?: BlogArticleQueryParamsModel,
  locale?: string,
) {
  const res = await axios.get<BaseResponsePage<[HomeArticleModel]>>(
    '/article/list',
    {
      params,
      headers: {
        'Current-Language': locale,
        tenantId: `${env.NEXT_APP_TENANTID}`,
      },
      baseURL: env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}

// 获取主题
export async function getThemeData(locale?: string) {
  const res = await axios.get<BaseResponse<HomeThemeModel>>('/theme/get', {
    headers: {
      'Current-Language': locale,
      tenantId: `${env.NEXT_APP_TENANTID}`,
    },
    baseURL: env.NEXT_APP_BLOG_API_HOST,
  });
  return res.data;
}

// blog详情
export async function getNewArticleDetailData(
  titleId: string,
  locale?: string,
) {
  const res = await axios.get<BaseResponse<HomeArticleModel>>(
    `/article/detail/title/${titleId}`,
    {
      headers: {
        'Current-Language': locale,
        tenantId: `${env.NEXT_APP_TENANTID}`,
      },
      baseURL: env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}
// 获取文章详情内容
export async function getArticleDetailContent(url: string) {
  const res = await axios.get<BlogDetailContentModel>(`${url}`, {
    baseURL: env.NEXT_APP_BLOG_API_HOST,
  });
  return res;
}

// article recommend
export async function getArticleRecommendTags(
  params: { currentId: string },
  locale?: string,
) {
  const res = await axios.get<BaseResponse<[HomeArticleModel]>>(
    `/article/list/recommend/tag`,
    {
      params,
      headers: {
        'Current-Language': locale,
        tenantId: `${env.NEXT_APP_TENANTID}`,
      },
      baseURL: env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function getSitemapList(params?: any, locale?: string) {
  const res = await axios.get<BaseResponse<[ISitemapField]>>(`/sitemap/list`, {
    baseURL: env.NEXT_APP_BLOG_API_HOST,
    params,
    headers: {
      'Current-Language': locale,
      tenantId: `${env.NEXT_APP_TENANTID}`,
    },
  });
  return res.data;
}

export async function getTdkApi(pageRoute: string, locale: string) {
  const res = await axios.get<BaseResponse<TdkResponse>>(
    `/tdk?pageRoute=${pageRoute}`,
    {
      headers: {
        'Current-Language': locale,
        tenantId: `${process.env.NEXT_APP_TENANTID}`,
      },
      baseURL: process.env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}
export async function getPageContentContentApi(
  pathVal: string,
  locale?: string,
  preview?: number | string,
  preview_id?: number | string,
) {
  const res = await axios.get<BaseResponse<LandingResponse>>(
    `/landing/get?pathVal=${pathVal}&preview=${preview}&lang=${locale}&preview_id=${preview_id}`,

    {
      headers: {
        'Current-Language': locale,
        tenantId: `${process.env.NEXT_APP_TENANTID}`,
      },
      baseURL: process.env.NEXT_APP_BLOG_API_HOST,
    },
  );
  return res.data;
}
