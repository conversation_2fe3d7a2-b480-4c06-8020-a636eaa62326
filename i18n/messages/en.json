{"Chat": {"ask_something_with_ai": "Ask something with AI", "start_a_new_chat": "Start a New Chat", "connect_socail_channel": "Connect Socail Channel", "connect_now": "Connect Now", "database_association": "Database Association", "data_summary": "Data Summary", "data_summary_desc": "This dataset contains xxx comments, detailing specific comment content, post content , and posting time data", "exploration": "Exploration", "all_platforms": "All Platforms", "generate_data_report": "Generate Data Report", "data_table_name": "Data Table Name", "image_preview": "Image Preview", "error_occurred_please_try_again": "An error occurred, please try again!", "please_select_a_file_to_upload": "Please select a file to upload", "server_upload_failed": "Server upload failed", "upload_failed": "Upload failed", "send_message": "Send Message", "stop": "Stop", "link_your_account_to_enable_automatic_comment_moderation": "Link your account to enable automatic comment moderation"}, "userSetting": {"update_password_failed": "update password failed", "update_password_success": "update password success", "updating": "updating", "confirm": "confirm", "enter_new_password": "Enter New Password", "change_password": "Change Password", "change_password_desc": "Change your password to keep your account secure.", "change_password_success": "Your password has been changed successfully.", "change_password_confirm_description": "This action cannot be undone.", "profile": "Profile", "email": "Email", "password": "Password", "edit": "Edit", "delete_account": "Delete Account", "account_management": "Account Management", "delete_account_desc": "Delete your account and all your data.", "If_you_want_to_log_in_with_email": "If you want to log in with email, you need to link your email first.", "delete_account_success": "Your account has been deleted successfully.", "delete_account_confirm_description": "This action cannot be undone.", "cancel": "Cancel", "confirm_delete": "Confirm Delete"}, "Moderation": {"Please_enter_at_least_one_url": "Please enter at least one URL", "Please_enter_at_least_one_keyword": "Please enter at least one keyword", "Please_enter_at_least_one_emoji": "Please enter at least one emoji", "Please_enter_at_least_one_hashtag": "Please enter at least one hashtag", "comment_processed_successfully": "Comment processed successfully", "update_successfully": "Update successfully", "Here_you_can_test_comments_to_see_if": "Here you can test comments to see if they would trigger your enabled moderation settings.", "Here_you_can_test_comments_to": "Automatically delete (instead of hide) comments that trigger your moderation settings.", "save": "Save", "moderation_settings": "Moderation Settings", "define_the_moderation_settings_that_will": "Define the moderation settings that will be used to process new comments you'll receive on your posts and ads.", "hide_profanity": "Hide profanity", "hide_negativity": "Hide negativity", "hide_emails_and_phone_numbers": "Hide emails and phone numbers", "hide_images": "Hide images", "hide_urls": "Hide URLs", "hide_mentions": "<PERSON><PERSON> mentions", "hide_hashtags": "Hide hashtags", "hide_emojis": "Hide emojis", "hide_keywords": "Hide keywords", "hide_all_comments": "Hide all comments", "type_here_press_enter_after_each_url": "Type here (separate each URL with a comma)", "test_your_moderation_settings": "Test Your Moderation Settings", "type_here_press_enter_after_each_hashtag": "Type here (separate each hashtag with a comma)", "type_here_press_enter_after_each_word": "Type here (separate each keywords with a comma)", "type_here_press_enter_after_each_emoji": "Type here (separate each emoji with a comma)", "process": "Process", "type_your_comment_here": "Type your comment here...", "advanced_settings_by_default_we_hide_all_mentions": "Advanced settings (by default we hide all mentions)", "delete_comments": "Delete comments", "configure_your_preferences_easily": "Configure your preferences easily", "advanced_settings_by_default_we_hide_all_emojis": "Advanced settings (by default we hide all emojis)", "By_default_all_URLs_will_be_hidden": "By default all URLs will be hidden, you can add domains or URLs you'd like to exclude from this filter.", "Here_you_can_add": "Here you can add hashtags you'd like to exclude from this filter."}, "bind_account": {"connect_platform": "Connect Platform", "bind_to_the_new_platform": "Bind to the new platform", "bind_platform_to_enjoy_automatic_comment_blocking_function": "Bind platform to enjoy automatic comment blocking function", "adapters": "Adapters", "bind_status_changed": "Bind status changed", "facebook_bind_failed": "Failed to bind Facebook account", "comment_management": "Comment Management", "block_settings": "Block Settings", "bind_status_failed": "Failed to change bind status", "unbind": "Pause Sync", "bind": "Start Sync", "toast": {"unlink_success": "Unlink successfully", "bind_status_changed": "Bind status changed"}}, "social_channel": {"user_comments": "User Comments", "connect_social_channels": "Connect Social Channels", "connect_your_account_to_enable_automated_bulk_management_and_comment_moderation": "Connect your account to enable automated bulk management and comment moderation.", "coming_soon": "Coming Soon", "link_new_platform": "Link New Platform", "no_agent_matching_your_needs": "No agent matching your needs? We value your", "feedback": "feedback", "LinkDesc": "Link your account safely to auto-filter negative comments & spam in bulk, with instant syncing."}, "Index": {"logout_success": "Logout successfully", "bulk_actions": "Bulk Actions", "hide_unhide_like_or_delete_comments_in_bulk_with_one_click": "Hide, unhide, like, or delete comments in bulk with one click.", "ai_data_report_generator": "AI Data Report Generator", "upload_your_files_click_and_your_insightful_visuals_will_be_ready_in_minutes": "Upload your files, click, and your insightful visuals will be ready in minutes.", "ai_social_media_moderator": "AI Social Media Comments Moderation", "ai_moderation_tool_detects_and_hides_hate_speech_scams_and_spam_stop_toxic_comments_instantly": "Our AI moderation tool detects and hides hate speech, scams, and spam. Stop toxic comments instantly!", "smart_comment_management": "Smart Comment Management", "track_and_manage_comment_across_platforms_like_facebook_and_instagram": "Track and manage comment across platforms like Facebook and Instagram.", "get_started": "Get Started", "ai_sentiment_analysis": "AI Sentiment Analysis", "automatically_detect_positive_negative_or_neutral_sentiments_in_comments": "Automatically detect positive, negative, or neutral sentiments in comments.", "try_demo": "Try Demo", "auto_delete_hide_comments": "Auto Delete & Hide Comments", "automatically_remove_or_hide_unwanted_comments": "Automatically remove or hide unwanted comments", "more_features": "More Features", "why_choose_our_ai_social_media_moderator": "Why Choose Our AI Social Media Moderator?", "real_time_moderation": "Real-Time Moderation", "customizable_settings": "Customizable Settings", "smart_custom_analytics": "Smart Custom Analytics", "seamless_integration": "Seamless Integration", "translation": "Translation", "translation_desc": "Translate comments from one language to another.", "filters": "Filters", "filters_desc": "Filter comments based on your criteria.", "knowledge_ai": "Knowledge AI", "knowledge_ai_desc": "Use AI to understand the context of the comment.", "post_viewer": "Post Viewer", "post_viewer_desc": "View posts and comments from your social media accounts.", "private_replies": "Private Replies", "private_replies_desc": "Send private replies to comments.", "block_users_mentions": "Block Users Mentions", "block_users_mentions_desc": "Block users mentions in comments.", "self_learning_algorithms": "Self-Learning Algorithms", "self_learning_algorithms_desc": "Our AI learns from your preferences over time to become even more accurate.", "instructions_ai": "Instructions AI", "instructions_ai_desc": "Use AI to generate instructions for your social media accounts.", "ai_sentiment_analysis_desc": "Automatically detect positive, negative, or neutral sentiments in comments.", "real_time_moderation_desc": "Our AI moderation tool detects and hides hate speech, scams, and spam. Stop toxic comments instantly!", "customizable_settings_desc": "Customize the moderation settings to your needs.", "smart_custom_analytics_desc": "Analyze the comments and posts to get insights.", "seamless_integration_desc": "Integrate with your social media accounts to manage comments."}, "Home": {"title": "Hello world!", "login": "<PERSON><PERSON>", "about": "Go to the about page", "menu": {"todo": "Todo", "file-upload": "File Upload", "pricing": "Pricing", "Blog": "Blog", "home": "Home", "FAQ": "FAQ", "hideComment": "Hide Comment", "sentimentAnalysis": "Sentiment Analysis"}, "auth": {"loginWithFacebook": "Login with Facebook", "getStarted": "Get Started", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed"}}, "SocialChannelConfiguration": {"next": "Next", "aiConfigurationHelper": "AI Configuration Helper", "manuallyConfigure": "Manually Configure", "aiAutoCreatesBlockingRulesForNegativeCommentsKeywordsAndSensitiveContent": "AI auto-creates blocking rules for negative comments, keywords and sensitive content", "manuallyConfigureBlockingRulesForNegativeCommentsKeywordsAndSensitiveContent": "Manually configure blocking rules for negative comments, keywords and sensitive content", "aiConfigurationHelperDescription": "AI auto-creates blocking rules for negative comments, keywords and sensitive content", "manuallyConfigureBlockingRulesForNegativeCommentsKeywordsAndSensitiveContentDescription": "Manually configure blocking rules for negative comments, keywords and sensitive content", "model": "Don't worry, you can modify these later", "bind_to_the_new_platform": "User Comments", "connect_social_channels": "Connect Social Channels", "configuration": "Configuration", "configuration_description": "Create personalized moderation rules to block.", "social_channel_configuration": "Social Channel Configuration", "skip": "<PERSON><PERSON>", "create_directly": "Create Directly"}, "faq": {"metadata": {"title": "Commentify FAQ - Your Questions Answered", "description": "Find answers to common questions about Commentify. Learn how our comment moderation works, pricing. Get help now!"}, "how-can-we-help": "How can we help?", "General Inquiries": "General Inquiries", "Features and Usage": "Features and Usage", "Q1-1": "What is Commentify Social Media Moderation Tool?", "A1-1": "Commentify is an AI-powered social media moderation tool that detects toxic comments through advanced sentiment analysis, automatically hiding hate speech, spam URLs, and negative keywords while preserving genuine engagement.", "Q1-2": "Does Commentify work with Facebook and Instagram ads?", "A1-2": "Commentify automatically moderates all Facebook & Instagram content - ads, posts, Stories & Reels. Uses AI to block spam, scams & hate speech while keeping genuine comments.", "Q1-3": "How does automatic comment hiding work?", "A1-3": "Our AI-powered system analyzes comments in real-time and automatically hides those that match your filtering criteria. You can set up filters for profanity, negative sentiment, competitor mentions, spam links, and more. The system learns from your preferences over time to become even more accurate.", "Q1-4": "How do I hide comments on Facebook?", "A1-4": "With Commentify , you can hide comments on Facebook in several ways. You can manually hide individual comments, set up automatic filters to hide comments containing specific words or phrases, or even hide all comments from specific users. Our platform makes it easy to manage your comment section without spending hours moderating.", "Q1-5": "How to snooze someone on Facebook without them knowing?", "A1-5": "Commentify allows you to effectively 'snooze' specific users by automatically hiding their comments as soon as they're posted. Unlike Facebook's native snooze feature, the person won't know their comments are being hidden from your page. This is perfect for managing problematic followers without creating conflict.", "Q1-6": "Can I hide comments on multiple Facebook pages?", "A1-6": "Yes! Commentify  supports managing comments across multiple Facebook pages and Instagram accounts from a single dashboard. This is especially useful for agencies or businesses managing several brand accounts.", "Q1-7": "Will people know their comments have been hidden?", "A1-7": "No, when you hide comments using Commentify , the person who posted the comment will still see their own comment, but it will be hidden from everyone else. This helps maintain a positive environment without creating confrontation.", "Q1-8": "What is social media sentiment analysis?​", "A1-8": "It's an AI tool that scans comments, ads, and posts to detect emotions (positive/negative/neutral), helping brands spot trends and avoid PR crises.", "Q2-1": "How to unhide a comment on Instagram?", "A2-1": "If you've hidden a comment on Instagram and want to restore it, HidComment makes it simple. In your dashboard, you can view all hidden comments and unhide them with a single click. You can also set up rules to automatically unhide certain types of comments after review.", "Q2-2": "How quickly does the tool hide toxic comments?​", "A2-2": "Instantly! Our AI scans and hides comments the second they're posted—no delay.", "Q2-3": "How does sentiment analysis work for multilingual comments?​", "A2-3": "Our NLP supports 50+ languages with dialect-specific models (e.g., US vs. UK English).", "Q2-4": "Is this tool against Facebook's policy?​", "A2-4": "No, we comply with ​​Facebook's moderation guidelines​​—you're simply filtering public visibility."}, "ErrorPage": {"title": "Sorry, something went wrong", "message": "We are working to fix this issue"}, "Blog": {"metadata": {"title": "Commentify Blog | Best Social Media Tips", "description": "Learn expert tips on comment moderation & social media management. Stay updated with the latest trends & AI-driven solutions. Read now!"}, "title": "This is blog list page", "author": "Author", "Newest": "Newest", "Article Details": "Article Details", "TABLE OF CONTENT": "TABLE OF CONTENT", "Read More": "Read More", "share": "Share"}, "Pricing": {"comment": "comment", "metadata": {"title": "Commentify Pricing - Simple, Transparent Plans", "description": "Affordable comment moderation plans for every user. No hidden fees. Start free trial today!"}, "confirm_subscription_change": "Confirm Subscription Change", "confirm_subscription_change_desc": "Click the button below to upgrade your subscription and unlock new features!", "upgrade_and_pay": "Upgrade&Pay", "title": "POINTS-BASED PRICING", "subtitle": "Choose the plan that fits your needs with our flexible points-based system", "cta": "Get Started", "free": "Free", "month": "month", "upgrade": "Upgrade Plan", "creditMonth": "credits per month", "unlimitedPages": "Unlimited pages", "unlimitedSentimentAnalysis": "Unlimited sentiment analysis", "automaticallyHideNegativeCommentsAndSpam": "Automatically hide negative comments and spam", "aiPoweredDataAnalysisJobs": "AI-powered data analysis jobs", "commentManagement": "Comment management", "customizableModerationSettings": "Customizable moderation settings", "aiDataAnalysisTask": "AI data analysis job", "commentProcessing": "Comment processing", "creditPer": "Credit per", "creditEach": "Credits each response", "billedMonthly": "billed monthly", "service": "Service", "creditCost": "Credit Cost", "pro": "Pro", "enterprise": "Enterprise", "starter": "Starter", "unlimitedUsers": "Unlimited users", "unlimitedComments": "Unlimited comments", "unlimitedModeration": "Unlimited moderation", "growth": "Growth", "Upgrade&renew": "Upgrade&renew", "currentPlan": "Current plan", "upgradeToEnjoyMoreServicesAndQuantity": "Upgrade to enjoy more services and quantity", "myInvoices": "Subscription Management", "getStarted": "Get Started today", "Free": "Free", "Starter": "Starter", "Growth": "Growth", "Pro": "Pro"}, "Payment": {"authorization_failure": "Authorization Failure", "success": {"title": "Payment Successful", "message": "Thank you for your purchase! Your subscription has been activated.", "dashboard": "Go to Dashboard", "home": "Return Home"}, "cancel": {"title": "Payment Cancelled", "message": "Your payment was not completed. You have not been charged.", "try_again": "Try Again", "home": "Return Home"}}, "Dashboard": {"unauthorized": "Unauthorized", "total": "total", "try_demo": "Try Demo", "get_started": "Get Started", "neutral_comments": "Neutral Comments", "bind_platform_management": "Social Channels", "link_your_accounts_to_get_started": "Link your accounts to get started", "bind_account": "Bind Account", "all_platforms": "All Platforms", "help_center": "Help Center", "account_name": "Account Name", "total_comments": "Total Comments", "positive_comments": "Positive Comments", "negative_comments": "Negative Comments", "hide_comments": "Hide Comments", "average_sentiment_is_good": "Average Sentiment is Good", "positive": "Positive", "negative": "Negative", "neutral": "Neutral", "emotion_analysis": "Emotion Analysis", "smart_insights": "Smart Insights", "summary": "Summary", "no_comments_data": "No comments data", "total_comments_data": "Total Comments Data", "positive_comments_data": "Positive Comments Data", "ai_sentiment_analysis": "AI Sentiment Analysis", "automatically_detect_positive_negative_or_neutral_sentiments_in_comments": "Automatically detect positive, negative, or neutral sentiments in comments", "quick_access_to_helpful_information": "Quick access to helpful information", "negative_comments_data": "Negative Comments Data", "neutral_comments_data": "Neutral Comments Data", "sentiment_distribution_over_time": "Sentiment Distribution Over Time", "daily_distribution_of_sentiments_of_all_platforms": "Daily distribution of sentiments of all platforms", "sentiment_value": "Sentiment Value (unit)", "product_introduction": "Product Introduction", "product_highlights": "Product Highlights", "pricing": "Pricing", "title": "Dashboard", "adapters": "Adapters", "refetch_facebook_accounts": "Refetch Facebook Pages", "refetch_instagram_accounts": "Refetch Instagram Accounts", "welcome": "Hello {email}", "facebook_account": "Facebook Account", "linked": "Linked", "comment_management": "Comment Management", "block_settings": "Block Settings", "unbind": "Unbind", "bind": "Bind", "connect_with_facebook": "Connect with Facebook", "bind_status_changed": "Bind status changed", "bind_status_failed": "Failed to change bind status", "facebook_bind_failed": "Failed to bind Facebook account", "facebook_bound_message": "Facebook account bound", "facebook_unbound_message": "Connect your Facebook account to manage your Facebook pages and comments.", "toast": {"refreshed_pages": "Refreshed pages", "bind_status_changed": "Bind status changed", "bind_status_failed": "Failed to change bind status", "facebook_bind_failed": "Failed to bind Facebook account", "facebook_unbind_failed": "Failed to unbind Facebook account", "platform_unbound": "Platform successfully unbound"}, "stats": {"total_comments": "Total Comments", "positive_comments": "Positive Comments", "negative_comments": "Negative Comments", "hide_comments": "Hide Comments"}, "actions": {"bind": "Bind", "unbind": "Unbind"}, "comments": "Comments", "bindAccount": "Bind Account", "history": "History", "upgradePlan": "Upgrade Plan", "usage": "Usage", "usageTooltip": "Current usage statistics, including the total number of comments and replies.", "upgradeCoupons": "Upgrade&coupons", "personalSettings": "Personal Settings", "blog": "Blog", "helpCenterFAQ": "Help Center FAQ", "introductionPage": "Introduction Page", "logOut": "Log Out", "dashboard": "Dashboard", "resources": "Resources", "sign_up_or_log_in": "Sign up or log in", "login_description": "Swift insights from knowledge and data.", "metadata": {"title": "Commentify - AI Comment Moderation for Social Media", "description": "Automate comment moderation on Facebook & Instagram. Detect, hide spam/toxic comments & get real-time analytics. Try free."}}, "Comments": {"search": "Search...", "all_platforms": "All Platforms", "export_comments": "Export Comments", "positive": "Positive", "neutral": "Neutral", "negative": "Negative", "all_sentiments": "All Sentiments", "comment_management_desc": "Manage your comments and posts", "comment": "Comment", "actively": "Actively", "all_status": "All Status", "unhidden": "Unhidden", "hidden": "Hidden", "batch_like": "<PERSON><PERSON>", "batch_unlike": "<PERSON><PERSON>", "batch_hide": "<PERSON><PERSON>", "batch_unhide": "<PERSON>ch Unhide", "go_to_chat": "Go to Chat", "commented_by": "Commented By", "platforms": "Platforms", "post_caption": "Post Caption", "sentiment": "Sentiment", "status": "Status", "actions": "Actions", "liked": "Liked", "unliked": "Unliked", "like": "Like", "unlike": "Unlike", "show": "Show", "hide": "<PERSON>de", "comment_hidden_successfully": "Comment hidden successfully", "previous": "Previous", "next": "Next", "user_comments": "User Comments", "please_bind_your_account_first": "Please bind your account first", "comments": "Comments", "moderation": "Moderation", "title": "Comments Management", "description": "Here you'll find an overview of the comments you've received on your posts and ads.", "query": {"placeholder": "Search comment content", "platformPlaceholder": "Select platform", "allPlatforms": "All platforms"}, "columns": {"commentBy": "Comment By", "comment": "Comment", "page": "Page", "post": "Post", "isHidden": "Is Hidden", "commentAt": "Comment At", "actions": "Actions"}, "action": {"label": "Actions", "fetchLatestPosts": "Fetch Latest Posts with Comments", "translate": "Translate", "translate_last_language": "Last Used", "fetching": "Fetching...", "hideUnhide": "Hide/Unhide", "reply": "reply", "replyPlaceholder": "reply to this comment", "dialog": {"replyHeader": "Reply to {author<PERSON>ame}", "replyContent": "Content", "confirm": "Confirm"}, "submitting": "Replying..."}, "toast": {"fetchCommentsSuccess": "Comments fetched successfully", "fetchCommentsFailed": "Failed to fetch comments", "hideUnhideCommentSuccess": "Comment hidden/unhidden successfully", "replyToCommentSuccess": "Success!", "generateCommentEmotionSuccess": "AI analysis successful!"}}, "Auth": {"signupSuccess": "Signup successful", "signupFailed": "Signup failed", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "signUp": "Sign Up", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "iAgree": "I agree to", "terms": "Terms and Conditions", "cookiePolicy": "<PERSON><PERSON>", "and": "and", "privacyPolicy": "Privacy Policy", "continue": "Continue", "orCreateWith": "or create with", "loginWithGoogle": "Login with Google", "alreadyHaveAccount": "Already have an account?", "login": "Log in", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 6 characters", "passwordsDontMatch": "Passwords do not match", "passwordRequired": "Password is required", "authError": "Authentication failed, please check your email and password", "googleLoginError": "Google login failed, please try again later", "sign_up": "Sign up", "log_in": "Log in", "sign_up_or_log_in": "Sign up or log in", "login_description": "Swift insights from knowledge and data.", "login_description_2": "Get started with Commentify to manage your comments and posts.", "login_description_3": "Start for free", "login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "sign_up_success": "Sign up successful", "sign_up_failed": "Sign up failed", "sign_up_or_log_in_success": "Sign up or log in successful", "sign_up_or_log_in_failed": "Sign up or log in failed"}}