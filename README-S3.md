# S3 Storage Integration for Next.js

This project includes a comprehensive AWS S3 integration for file uploads and management in a Next.js application.

## Setup

1. Ensure you have the necessary AWS credentials and bucket configured
2. Add the following environment variables to your `.env.local` file:

```
AWS_REGION=your-region
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_PUBLIC_URL=your-bucket-public-url
```

**Note**: For Supabase Storage, the `AWS_S3_PUBLIC_URL` would typically be `https://your-project-ref.supabase.co/storage/v1/s3`

## Features

The integration provides the following features:

- 📤 **File uploads** (server-side and client-side)
- 🔗 **Presigned URLs** for direct client uploads
- 📥 **File downloads** with presigned URLs
- 🗑️ **File deletion**
- 🔍 **File existence checking**
- 🏷️ **Unique filename generation**

## Components and APIs

### Core Utilities

- `lib/storage.ts` - Core S3 integration with all the low-level functions

### API Routes

- `app/api/storage/upload` - Server-side file uploads
- `app/api/storage/presigned-url` - Generate presigned URLs for client-side uploads
- `app/api/storage/download` - Generate presigned URLs for downloading files
- `app/api/storage/delete` - Delete files from S3

### Server Actions

- `app/actions/upload-actions.ts` - Server actions for file uploads

### Client Components

- `components/FileUpload.tsx` - Reusable component for file uploads
- `app/server-upload/server-upload-form.tsx` - Example of using server actions for uploads

### API Service for Server Components

- `lib/api-service.ts` - Utilities for server components to interact with the storage API

## Usage Examples

### 1. Client-Side Uploads via API

```tsx
// In a client component
const handleUpload = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/storage/upload', {
    method: 'POST',
    body: formData,
  });

  const result = await response.json();
  console.log(result.fileUrl); // URL to the uploaded file
};
```

### 2. Direct-to-S3 Uploads with Presigned URLs

```tsx
// In a client component
const handleDirectUpload = async (file: File) => {
  // Step 1: Get presigned URL
  const presignedResponse = await fetch('/api/storage/presigned-url', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      filename: file.name,
      contentType: file.type,
    }),
  });

  const { uploadUrl, publicUrl } = await presignedResponse.json();

  // Step 2: Upload directly to S3
  await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: { 'Content-Type': file.type },
  });

  return publicUrl; // Public URL of the uploaded file
};
```

### 3. Server-Side Uploads with Server Actions

```tsx
// In a client component
import { uploadFileAction } from '@/app/actions/upload-actions';

const handleServerUpload = async (formData: FormData) => {
  const result = await uploadFileAction(formData);
  if (result.success) {
    console.log(result.fileUrl); // URL to the uploaded file
  }
};
```

### 4. Server Component Uploads (e.g., in API routes)

```tsx
import { uploadFile, generateUniqueFileKey } from '@/lib/storage';

export async function POST(request: Request) {
  const formData = await request.formData();
  const file = formData.get('file') as File;
  const buffer = Buffer.from(await file.arrayBuffer());

  const fileKey = generateUniqueFileKey(file.name, 'uploads');
  const fileUrl = await uploadFile(fileKey, buffer, {
    contentType: file.type,
    isPublic: true,
  });

  return Response.json({ fileUrl });
}
```

## Demo Pages

- `/file-upload` - Client-side upload demo
- `/server-upload` - Server actions upload demo

## License

This integration is part of the overall project license.
