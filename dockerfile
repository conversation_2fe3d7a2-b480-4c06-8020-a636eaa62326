# Stage 1: Install dependencies
FROM node:18-alpine AS deps
# Install pnpm
RUN npm install -g pnpm

# echo pnpm version
RUN pnpm -v

WORKDIR /app

# Copy dependency definition files
COPY package.json pnpm-lock.yaml ./

# Install dependencies using pnpm
RUN pnpm install

# Stage 2: Build the application
FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set build-time environment variables
ARG BUILD_ENV=production
ENV NODE_ENV=${BUILD_ENV}

# Install pnpm
RUN npm install -g pnpm

# Build the Next.js application
RUN echo "Building with NODE_ENV=${NODE_ENV}"
RUN pnpm build

# Stage 3: Production image
FROM node:18-alpine AS runner
WORKDIR /app

# Set runtime environment variable based on build argument
ARG BUILD_ENV=production
ENV NODE_ENV=${BUILD_ENV}

# Copy necessary files from the builder stage
COPY --from=builder /app/public ./public
# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Copy appropriate .env file based on environment
COPY .env.${NODE_ENV} ./.env

# Expose the port the app runs on
EXPOSE 3000

# Set the default command to run the application
CMD ["node", "server.js"]