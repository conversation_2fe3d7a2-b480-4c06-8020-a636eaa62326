import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  server: {
    METADATA_TITLE: z.string().min(1),
    METADATA_DESCRIPTION: z.string().min(1),
    DATABASE_URL: z.string().min(1),
    AWS_REGION: z.string().min(1),
    AWS_ACCESS_KEY_ID: z.string().min(1),
    AWS_SECRET_ACCESS_KEY: z.string().min(1),
    AWS_S3_BUCKET_NAME: z.string().min(1),
    AWS_ENDPOINT_URL: z.string().url(),
    AWS_S3_PUBLIC_URL: z.string().url(),
    STRIPE_SECRET_KEY: z.string().min(1),
    STRIPE_WEBHOOK_SECRET: z.string().min(1),
    AWS_BEDROCK_REGION: z.string().min(1),
    AWS_BEDROCK_ACCESS_KEY_ID: z.string().min(1),
    AWS_BEDROCK_SECRET_KEY: z.string().min(1),
    FACEBOOK_APP_ID: z.string().min(1),
    FACEBOOK_APP_SECRET: z.string().min(1),
    FACEBOOK_VERIFY_TOKEN: z.string().min(1),
    OPENAI_API_KEY: z.string().min(1),
    COMMENT_MCP_SERVER_SSE: z.string().min(1),
    JOB_TOKEN: z.string().min(1),
    NEXT_APP_BLOG_API_HOST: z.string().min(1),
    NEXT_APP_TENANTID: z.string().min(1),
  },
  client: {
    NEXT_PUBLIC_APP_ENV: z.enum(['local', 'production']),
    NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: z.string().startsWith('G-'),
    NEXT_PUBLIC_CLARIFY_ID: z.string().min(1),
    NEXT_PUBLIC_SITE_URL: z.string().url(),
    NEXT_PUBLIC_SITE_NAME: z.string().min(1),
    NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  },
  runtimeEnv: {
    METADATA_TITLE: process.env.METADATA_TITLE,
    METADATA_DESCRIPTION: process.env.METADATA_DESCRIPTION,
    AWS_REGION: process.env.AWS_REGION,
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    AWS_S3_BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME,
    AWS_ENDPOINT_URL: process.env.AWS_ENDPOINT_URL,
    AWS_S3_PUBLIC_URL: process.env.AWS_S3_PUBLIC_URL,
    DATABASE_URL: process.env.DATABASE_URL,
    NEXT_PUBLIC_GOOGLE_ANALYTICS_ID:
      process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
    NEXT_PUBLIC_APP_ENV: process.env.NEXT_PUBLIC_APP_ENV,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
    NEXT_PUBLIC_SITE_NAME: process.env.NEXT_PUBLIC_SITE_NAME,
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    AWS_BEDROCK_REGION: process.env.AWS_BEDROCK_REGION,
    AWS_BEDROCK_ACCESS_KEY_ID: process.env.AWS_BEDROCK_ACCESS_KEY_ID,
    AWS_BEDROCK_SECRET_KEY: process.env.AWS_BEDROCK_SECRET_KEY,
    FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
    FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET,
    FACEBOOK_VERIFY_TOKEN: process.env.FACEBOOK_VERIFY_TOKEN,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    COMMENT_MCP_SERVER_SSE: process.env.COMMENT_MCP_SERVER_SSE,
    JOB_TOKEN: process.env.JOB_TOKEN,
    NEXT_PUBLIC_CLARIFY_ID: process.env.NEXT_PUBLIC_CLARIFY_ID,
    NEXT_APP_BLOG_API_HOST: process.env.NEXT_APP_BLOG_API_HOST,
    NEXT_APP_TENANTID: process.env.NEXT_APP_TENANTID,
  },
  skipValidation: false,
  emptyStringAsUndefined: true,
});
