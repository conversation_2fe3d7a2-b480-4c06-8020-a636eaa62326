---
description: 
globs: 
alwaysApply: true
---
# NextJS 项目技术栈指南

使用 pnpm 作为包管理，在执行命令的时候，需要使用pnpm


## 前端框t
- 项目基于 **Next.js 15.1.4** 构建 (@/app 目录结构)
- 使用 **React 19.0.0** 与 **React DOM 19.0.0**
- 使用 **TailwindCSS 3.4.1** 进行样式管理 (查看 @/tailwind.config.ts)
- 使用 shadcnui 来创建页面，如果组件没有，请帮助我使用 shadcnui 的命令个来创建 

## tRPC 和 React-query
- 使用 tRPC 对服务进行调用
- React-Query 作为数据状态管理

## 后端与数据库
- 使用 **Supabase** 作为Auth (@supabase/ssr, @supabase/supabase-js)
- 使用 **Drizzle ORM** 进行数据库操作 (@/db 目录)，如果需要在数据库中操作，在 @/service 下去创建文件
- 数据库为 **PostgreSQL** (postgres 依赖)
- 使用 AWS S3 进行文件存储 (@aws-sdk/client-s3, @aws-sdk/s3-request-presigner)

## 工具链
- 使用 **TypeScript** 进行类型检查
- 使用 **Zod** 进行数据验证
- 使用 **next-intl** 进行国际化，需要注意的是，在新增路由的时候，请在 app/[locale]/ 下去创建，且将多语言的数据存放在 i18n/mwssages 下的json文件
- 使用 **eslint** 进行代码规范检查
- 使用 **Turbopack** 进行开发环境优化

## 其他
- 使用了环境变量配置 (@t3-oss/env-nextjs)，如果需要添加环境变量，同时需要修改 @/env.ts 里的配置

