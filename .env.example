NEXT_PUBLIC_APP_ENV=local
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=Quickstart Next.js Local


NEXT_PUBLIC_SUPABASE_URL=https://xxxxxx
NEXT_PUBLIC_SUPABASE_ANON_KEY=xx.xx.-xxx
DATABASE_URL="postgresql://postgres.xxx:<EMAIL>:6543/postgres"

NEXT_PUBLIC_CLARIFY_ID=qxqlzw6xaj

NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-xxxxx

METADATA_TITLE=commentify - Smart Comment Management for Facebook & Instagram
METADATA_DESCRIPTION=Hide, snooze, and manage comments on Facebook and Instagram with AI-powered tools. Save time and improve your social media presence.

# AWS S3
AWS_REGION=us-west-1
AWS_ACCESS_KEY_ID=xxxx
AWS_SECRET_ACCESS_KEY=xxxxx
AWS_S3_BUCKET_NAME=quickstart
AWS_ENDPOINT_URL=https://xxxx.xxx.co/storage/v1/s3
AWS_S3_PUBLIC_URL=https://xxxx.xxxxx.co/storage/v1/object/public/quickstart


# Stripe
STRIPE_SECRET_KEY=xxxxx
STRIPE_WEBHOOK_SECRET=xxxx


# AWS Bedrock
AWS_BEDROCK_REGION=us-west-2
AWS_BEDROCK_ACCESS_KEY_ID=xxxx
AWS_BEDROCK_SECRET_KEY=xxxx


# OPENAI API KEY
OPENAI_API_KEY=xxxx


# JOB TOKEN
JOB_TOKEN=xxxxx