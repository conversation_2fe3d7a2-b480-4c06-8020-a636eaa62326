import {
  integer,
  boolean,
  text,
  timestamp,
  pgTable,
  serial,
  uuid,
  json,
  primaryKey,
  varchar,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { createInsertSchema } from 'drizzle-zod';

// 创建用户表
export const user = pgTable('user', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid').notNull().unique(),
  email: varchar('email', { length: 255 }).notNull(),
  avatar: text('avatar'),
  stripeCustomerId: varchar('stripe_customer_id', { length: 255 }),
  currentPlan: varchar('current_plan', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 50 }).default(
    'inactive',
  ),
  subscriptionExpiresAt: timestamp('subscription_expires_at'),
  amount: integer('amount').notNull().default(100),
  remainAmount: integer('remain_amount').notNull().default(100),
  preferedTranslateLanguage: varchar('prefered_translate_language', {
    length: 50,
  }).default(''),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type UserSchema = typeof user.$inferSelect;

export const platformConnect = pgTable('platform_connect', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  platform: varchar('platform', { length: 50 }).notNull(),
  identityId: varchar('identity_id', { length: 255 }),
  accountName: varchar('account_name', { length: 255 }),
  email: varchar('email', { length: 255 }),
  externalId: varchar('external_id', { length: 255 }),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  userLongLivedToken: text('user_long_lived_token'),
  pageLongLivedToken: text('page_long_lived_token'),
  tokenExpiry: timestamp('token_expiry'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type PlatformConnect = typeof platformConnect.$inferSelect;
export type UserWithPlatform = {
  user: UserSchema;
  platforms?: PlatformConnect[];
};

export const socialPage = pgTable('social_page', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  platform: varchar('platform', { length: 50 }).notNull(),
  link: text('link'),
  externalPageId: varchar('external_page_id', { length: 255 }),
  pageName: varchar('page_name', { length: 255 }),
  bindStatus: boolean('bind_status').notNull().default(false),
  avatarUrl: text('avatar_url'),
  accessToken: text('access_token'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const socialPost = pgTable('social_post', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  externalPageId: varchar('external_page_id', { length: 255 }),
  externalPostId: varchar('external_post_id', { length: 255 }),
  platform: varchar('platform', { length: 50 }).notNull(),
  permalink: text('permalink'),
  content: text('content'),
  postTime: timestamp('post_time'),
  metadata: json('metadata'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type SocialPost = typeof socialPost.$inferSelect;

export const socialComment = pgTable('social_comment', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  externalCommentId: varchar('external_comment_id', { length: 255 }),
  externalPostId: varchar('external_post_id', { length: 255 }),
  externalPageId: varchar('external_page_id', { length: 255 }),
  platform: varchar('platform', { length: 50 }),
  authorName: varchar('author_name', { length: 255 }),
  authorId: varchar('author_id', { length: 255 }),
  content: text('content'),
  contentTranslated: text('content_translated'),
  commentTime: timestamp('comment_time'),
  emotion: varchar('emotion', {
    enum: ['positive', 'negative', 'neutral', 'pending'],
  }).default('pending'),
  ai_reason: text('ai_reason'),
  isHidden: boolean('is_hidden').notNull().default(false),
  canHide: boolean('can_hide').notNull().default(false),
  canComment: boolean('can_comment').notNull().default(false),
  canReplyPrivately: boolean('can_reply_privately').notNull().default(false),
  replyText: text('reply_text'),
  canLike: boolean('can_like').notNull().default(false),
  likeCount: integer('like_count').notNull().default(0),
  userLikes: boolean('user_likes').notNull().default(false),
  hiddenBy: varchar('hidden_by', { length: 255 }),
  postExcerpt: text('post_excerpt'),
  attachments: json('attachments'),
  messageTags: json('message_tags'),
  permalinkUrl: text('permalink_url'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type SocialCommentsSchema = typeof socialComment.$inferSelect;
export type SocialPostsSchema = typeof socialPost.$inferSelect;
export type SocialPagesSchema = typeof socialPage.$inferSelect;
export type SocialPostWithPage = {
  post: SocialPostsSchema;
  page: SocialPagesSchema;
};
export type CommentWithPost = {
  comment: SocialCommentsSchema;
  post: SocialPostsSchema;
  page: SocialPagesSchema;
};

export const commentRule = pgTable('comment_rule', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  externalPageId: varchar('external_page_id', { length: 255 }),
  hideProfanityStatus: boolean('hide_profanity_status')
    .notNull()
    .default(false),
  hideNegativityStatus: boolean('hide_negativity_status')
    .notNull()
    .default(false),
  hideEmailAndPhoneNumberStatus: boolean('hide_email_and_phone_number_status')
    .notNull()
    .default(false),
  hideImageStatus: boolean('hide_image_status').notNull().default(false),
  hideUrlStatus: boolean('hide_url_status').notNull().default(false),
  urls: text('urls'),
  hideMentionStatus: boolean('hide_mention_status').notNull().default(false),
  hideHashTags: boolean('hide_hash_tags').notNull().default(false),
  tags: text('tags'),
  hideEmojisStatus: boolean('hide_emojis_status').notNull().default(false),
  emojis: text('emojis'),
  hideKeywordsStatus: boolean('hide_keywords_status').notNull().default(false),
  keywords: text('keywords'),
  hideAllCommentsStatus: boolean('hide_all_comments_status')
    .notNull()
    .default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type CommentRuleSchema = typeof commentRule.$inferSelect;
export type CreateCommentRule = typeof commentRule.$inferInsert;

export const commentRuleCreateSchema = createInsertSchema(commentRule).omit({
  uid: true,
  createdAt: true,
  updatedAt: true,
});

export const commentInsight = pgTable('comment_insight', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  externalPageId: varchar('external_page_id', { length: 255 }),
  fullInsight: boolean('full_insight').notNull().default(false),
  insight: text('insight'),
  extraInfo: json('extra_info'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type CommentInsight = typeof commentInsight.$inferSelect;

export const commentJob = pgTable('comment_job', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  externalPageId: varchar('external_page_id', { length: 255 }),
  externalPostId: varchar('external_post_id', { length: 255 }),
  type: varchar('type', { length: 50 }).notNull(),
  status: varchar('status', {
    enum: ['running', 'completed', 'failed'],
  }).notNull(),
  nextRunAt: timestamp('next_run_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const webhookEvent = pgTable('webhook_event', {
  id: uuid('id').primaryKey().defaultRandom(),
  event: json('event'),
  status: varchar('status', {
    enum: ['pending', 'completed', 'failed'],
  })
    .notNull()
    .default('pending'),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt')
    .notNull()
    .$onUpdate(() => new Date()),
});

export const userAsset = pgTable('user_asset', {
  id: uuid('id').primaryKey().defaultRandom(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  type: varchar('type', { length: 50 }).notNull(),
  url: text('url').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// 创建订阅历史表
export const subscriptionHistory = pgTable('subscription_history', {
  id: serial('id').primaryKey(),
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  stripeSubscriptionId: varchar('stripe_subscription_id', {
    length: 255,
  }).notNull(),
  status: varchar('status', { length: 50 }).notNull(),
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// 定义用户和订阅历史的关系
export const usersRelations = relations(user, ({ many }) => ({
  subscriptionHistory: many(subscriptionHistory),
}));

export const subscriptionHistoryRelations = relations(
  subscriptionHistory,
  ({ one }) => ({
    user: one(user, {
      fields: [subscriptionHistory.uid],
      references: [user.uid],
    }),
  }),
);

// 聊天会话表定义
export const chat = pgTable('chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(), // 聊天 ID (UUID, 主键, 非空, 默认随机生成)
  createdAt: timestamp('createdAt').notNull(), // 创建时间 (时间戳, 非空)
  title: text('title').notNull(), // 聊天标题 (文本, 非空)
  // 用户 ID (UUID, 非空, 外键关联到 user 表的 id)
  uid: uuid('uid')
    .notNull()
    .references(() => user.uid),
  // 可见性 ('public' 或 'private', 字符串枚举, 非空, 默认为 'private')
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
});

// 从 chat 表推断出的 TypeScript 聊天类型
export type Chat = typeof chat.$inferSelect;

// 新版消息表 (v2)
export const message = pgTable('message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(), // 消息 ID (UUID, 主键, 非空, 默认随机生成)
  // 聊天 ID (UUID, 非空, 外键关联到 chat 表的 id)
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(), // 角色 ('user' 或 'assistant', 字符串, 非空)
  parts: json('parts').notNull(), // 消息内容部分 (JSON, 非空, 支持多模态)
  promptToken: integer('prompt_token').notNull().default(0),
  completionToken: integer('completion_token').notNull().default(0),
  externalPageId: varchar('external_page_id', { length: 255 }).default(''),
  totalToken: integer('total_token').notNull().default(0),
  attachments: json('attachments').notNull(), // 附件信息 (JSON, 非空)
  createdAt: timestamp('createdAt').notNull(), // 创建时间 (时间戳, 非空)
});

// 从 message_v2 表推断出的 TypeScript 数据库消息类型
export type DBMessage = typeof message.$inferSelect;

// 新版投票表 (v2)
export const vote = pgTable(
  'vote',
  {
    // 聊天 ID (UUID, 非空, 外键关联到 chat 表的 id)
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    // 消息 ID (UUID, 非空, 外键关联到新版 message 表的 id)
    messageId: uuid('messageId')
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean('isUpvoted').notNull(), // 是否点赞 (布尔值, 非空)
  },
  (table) => [
    // 复合主键 (chatId, messageId)
    primaryKey({ columns: [table.chatId, table.messageId] }),
  ],
);

// 从 vote_v2 表推断出的 TypeScript 投票类型
export type Vote = typeof vote.$inferSelect;

// Artifact 文档表定义
export const document = pgTable(
  'document',
  {
    // 文档 ID (UUID, 非空, 默认随机生成) - 注意：复合主键的一部分
    id: uuid('id').notNull().defaultRandom(),
    // 创建时间 (时间戳, 非空) - 注意：复合主键的一部分，用于区分版本
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(), // 文档标题 (文本, 非空)
    content: text('content'), // 文档内容 (文本, 可空)
    // Artifact 类型 (字符串枚举, 非空, 默认为 'text')
    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })
      .notNull()
      .default('text'),
    // 用户 ID (UUID, 非空, 外键关联到 user 表的 id)
    uid: uuid('uid')
      .notNull()
      .references(() => user.uid),
  },
  (table) => [
    // 复合主键 (id, createdAt) - 同一个 id 不同 createdAt 代表不同版本
    primaryKey({ columns: [table.id, table.createdAt] }),
  ],
);

// 从 document 表推断出的 TypeScript 文档类型
export type Document = typeof document.$inferSelect;
