{"id": "*************-487f-b002-a31e20419897", "prevId": "37c72efd-3fa4-457d-83c4-b1334d9cc932", "version": "7", "dialect": "postgresql", "tables": {"public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}}, "indexes": {}, "foreignKeys": {"chat_uid_user_uid_fk": {"name": "chat_uid_user_uid_fk", "tableFrom": "chat", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comment_rule": {"name": "comment_rule", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "external_page_id": {"name": "external_page_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "hide_profanity_status": {"name": "hide_profanity_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_negativity_status": {"name": "hide_negativity_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_emoji_and_phone_number_status": {"name": "hide_emoji_and_phone_number_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_image_status": {"name": "hide_image_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_url_status": {"name": "hide_url_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "urls": {"name": "urls", "type": "text", "primaryKey": false, "notNull": false}, "hide_mention_status": {"name": "hide_mention_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_hash_tags": {"name": "hide_hash_tags", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "hide_emojis_status": {"name": "hide_emojis_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "emojis": {"name": "emojis", "type": "text", "primaryKey": false, "notNull": false}, "hide_keywords_status": {"name": "hide_keywords_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "keywords": {"name": "keywords", "type": "text", "primaryKey": false, "notNull": false}, "hide_all_comments_status": {"name": "hide_all_comments_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"comment_rule_uid_user_uid_fk": {"name": "comment_rule_uid_user_uid_fk", "tableFrom": "comment_rule", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document": {"name": "document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"document_uid_user_uid_fk": {"name": "document_uid_user_uid_fk", "tableFrom": "document", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"document_id_createdAt_pk": {"name": "document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.message": {"name": "message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": true}, "prompt_token": {"name": "prompt_token", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "completion_token": {"name": "completion_token", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_token": {"name": "total_token", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"message_chatId_chat_id_fk": {"name": "message_chatId_chat_id_fk", "tableFrom": "message", "tableTo": "chat", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.platform_connect": {"name": "platform_connect", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "identity_id": {"name": "identity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "account_name": {"name": "account_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "user_long_lived_token": {"name": "user_long_lived_token", "type": "text", "primaryKey": false, "notNull": false}, "page_long_lived_token": {"name": "page_long_lived_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expiry": {"name": "token_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"platform_connect_uid_user_uid_fk": {"name": "platform_connect_uid_user_uid_fk", "tableFrom": "platform_connect", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_comment": {"name": "social_comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "external_comment_id": {"name": "external_comment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_post_id": {"name": "external_post_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_page_id": {"name": "external_page_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "author_name": {"name": "author_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "content_translated": {"name": "content_translated", "type": "text", "primaryKey": false, "notNull": false}, "comment_time": {"name": "comment_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "emotion": {"name": "emotion", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'pending'"}, "ai_reason": {"name": "ai_reason", "type": "text", "primaryKey": false, "notNull": false}, "is_hidden": {"name": "is_hidden", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "can_hide": {"name": "can_hide", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "can_comment": {"name": "can_comment", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "can_reply_privately": {"name": "can_reply_privately", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "reply_text": {"name": "reply_text", "type": "text", "primaryKey": false, "notNull": false}, "can_like": {"name": "can_like", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "user_likes": {"name": "user_likes", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hidden_by": {"name": "hidden_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "post_excerpt": {"name": "post_excerpt", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": false}, "message_tags": {"name": "message_tags", "type": "json", "primaryKey": false, "notNull": false}, "permalink_url": {"name": "permalink_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"social_comment_uid_user_uid_fk": {"name": "social_comment_uid_user_uid_fk", "tableFrom": "social_comment", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_page": {"name": "social_page", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false}, "external_page_id": {"name": "external_page_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "page_name": {"name": "page_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "bind_status": {"name": "bind_status", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"social_page_uid_user_uid_fk": {"name": "social_page_uid_user_uid_fk", "tableFrom": "social_page", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_post": {"name": "social_post", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "external_page_id": {"name": "external_page_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_post_id": {"name": "external_post_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "permalink": {"name": "permalink", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "post_time": {"name": "post_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"social_post_uid_user_uid_fk": {"name": "social_post_uid_user_uid_fk", "tableFrom": "social_post", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_history": {"name": "subscription_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_history_uid_user_uid_fk": {"name": "subscription_history_uid_user_uid_fk", "tableFrom": "subscription_history", "tableTo": "user", "columnsFrom": ["uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "current_plan": {"name": "current_plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'inactive'"}, "subscription_expires_at": {"name": "subscription_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 300}, "prefered_translate_language": {"name": "prefered_translate_language", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_uid_unique": {"name": "user_uid_unique", "nullsNotDistinct": false, "columns": ["uid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vote": {"name": "vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"vote_chatId_chat_id_fk": {"name": "vote_chatId_chat_id_fk", "tableFrom": "vote", "tableTo": "chat", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vote_messageId_message_id_fk": {"name": "vote_messageId_message_id_fk", "tableFrom": "vote", "tableTo": "message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"vote_chatId_messageId_pk": {"name": "vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}