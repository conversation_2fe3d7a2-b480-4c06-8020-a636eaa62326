CREATE TABLE "comment_insight" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"uid" uuid NOT NULL,
	"external_page_id" varchar(255),
	"full_insight" boolean DEFAULT false NOT NULL,
	"insight" text,
	"extra_info" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "comment_insight" ADD CONSTRAINT "comment_insight_uid_user_uid_fk" FOREIGN KEY ("uid") REFERENCES "public"."user"("uid") ON DELETE no action ON UPDATE no action;