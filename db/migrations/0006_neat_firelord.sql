CREATE TABLE "comment_rule" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"uid" uuid NOT NULL,
	"external_page_id" varchar(255),
	"hide_profanity_status" boolean DEFAULT true NOT NULL,
	"hide_negativity_status" boolean DEFAULT true NOT NULL,
	"hide_emoji_and_phone_number_status" boolean DEFAULT false NOT NULL,
	"hide_image_status" boolean DEFAULT true NOT NULL,
	"hide_url_status" boolean DEFAULT true NOT NULL,
	"urls" text,
	"hide_mention_status" boolean DEFAULT true NOT NULL,
	"hide_hash_tags" boolean DEFAULT true NOT NULL,
	"tags" text,
	"hide_emojis_status" boolean DEFAULT true NOT NULL,
	"emojis" text,
	"hide_keywords_status" boolean DEFAULT true NOT NULL,
	"keywords" text,
	"hide_all_comments_status" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "comment_rule" ADD CONSTRAINT "comment_rule_uid_user_uid_fk" FOREIGN KEY ("uid") REFERENCES "public"."user"("uid") ON DELETE no action ON UPDATE no action;