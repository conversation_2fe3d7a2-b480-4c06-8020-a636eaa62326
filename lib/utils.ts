import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

interface IxParams {
  w?: string | number;
  h?: string | number;
  q?: string | number;
}

export const isS3Image = (url: string) => {
  return url?.includes('pumpsoul.com') ?? false; //) || (url?.includes('res-back-test.pumpsoul.com') ?? false);
};

export function imgUrl(v: string, type?: 'small' | 'mid' | 'big') {
  const imgType = type ?? 'mid';
  switch (imgType) {
    case 'small':
      return imgix(v, { w: 200, h: 200 });
    case 'big':
      return imgix(v, { w: 1000, h: 1000 });
    default:
      return imgix(v, { w: 500, h: 500 });
  }
}

export function imgix(v: string, params?: IxParams) {
  if (!v || !isS3Image(v)) return v || '';

  const w = params?.w ?? 100;
  const h = params?.h ?? 100;
  return v + `__op__resize,m_mfit,w_${w},h_${h}__op__format,f_webp`;
}

export const PlatImageMap = new Map<string, string>([
  ['facebook', '/images/dashboard/facebook.webp'],
  ['instagram', '/images/dashboard/ins.webp'],
  ['', '/images/dashboard/platform-all.svg'],
]);
