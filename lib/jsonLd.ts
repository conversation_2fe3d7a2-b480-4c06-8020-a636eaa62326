export type structureData = {
  title?: string;
  url?: string;
  desc?: string;
};
export type pricingStructure = {
  name: string;
  price: string;
  currency: string;
};
export type faqStructure = {
  question: string;
  answer: string;
};

export const homeJsonLd = (site: structureData) => ({
  '@context': 'https://schema.org',
  '@graph': [
    {
      '@type': 'WebSite',
      name: site.title,
      url: site.url,
      description: site.desc,
    },
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: site.url,
        },
      ],
    },
  ],
});

export const page2JsonLd = (
  currentPage: structureData,
  site: structureData,
) => ({
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  name: currentPage.title,
  description: currentPage.desc,
  url: currentPage.url,
  breadcrumb: {
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: site.url,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: currentPage.title,
        item: currentPage.url,
      },
    ],
  },
});

export const pricingJsonLd = (
  curerntPage: structureData,
  site: structureData,
  pricings?: pricingStructure[],
) => {
  const pricingItems = pricings?.map((pricing) => ({
    '@type': 'Offer',
    name: pricing.name,
    priceCurrency: pricing.currency,
    price: pricing.price,
  }));
  //根据pricings的价格排序，获取最高价格和最低价格
  const sortedPricings =
    pricingItems?.sort((a, b) => parseFloat(a.price) - parseFloat(b.price)) ||
    [];
  return {
    '@context': 'https://schema.org',
    '@type': 'Servive',
    name: curerntPage.title,
    serviceType: 'Subscription-based Erasa',
    description: curerntPage.desc,
    url: curerntPage.url,
    provide: {
      '@type': 'Organization',
      name: 'Erasa',
      url: site.url,
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: site.url,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Pricing',
          item: curerntPage.url,
        },
      ],
    },
    offers: sortedPricings.map((item) => ({
      '@type': 'Offer',
      name: item.name,
      price: item.price,
      pricecurrency: item.priceCurrency,
      availability: 'https://schema.org/Instock',
      url: site.url + '/plan',
    })),
  };
};

export const faqJsonLd = (
  currentPage: structureData,
  site: structureData,
  questions: faqStructure[],
) => {
  const faqItems = questions.map((question) => ({
    '@type': 'Question',
    name: question.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: question.answer,
    },
  }));
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    name: currentPage.title,
    url: currentPage.url,
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: site.url,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'FAQ',
          item: currentPage.url,
        },
      ],
    },
    mainEntity: faqItems,
  };
};
