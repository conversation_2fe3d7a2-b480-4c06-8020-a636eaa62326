import { useEffect, useState } from 'react';

export function useAutoScroll({
  smooth = false,
  content,
}: {
  smooth?: boolean;
  content: React.ReactNode;
}) {
  const scrollRef = document.getElementById(
    'scroll-row-chat',
  ) as HTMLDivElement;
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);

  const scrollToBottom = () => {
    if (!scrollRef) return;

    const scrollElement = scrollRef;
    const { scrollHeight, clientHeight } = scrollElement;

    scrollElement.scrollTo({
      top: scrollHeight - clientHeight,
      behavior: smooth ? 'smooth' : 'auto',
    });

    setIsAtBottom(true);
    setAutoScrollEnabled(true);
  };

  const disableAutoScroll = () => {
    if (!scrollRef) return;

    const scrollElement = scrollRef;
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceFromBottom > 10) {
      setAutoScrollEnabled(false);
      setIsAtBottom(false);
    }
  };

  useEffect(() => {
    if (autoScrollEnabled) {
      scrollToBottom();
    } else {
      // Check if we're at the bottom even if auto-scroll is disabled
      if (scrollRef) {
        const { scrollTop, scrollHeight, clientHeight } = scrollRef;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
        setIsAtBottom(distanceFromBottom < 10);
      }
    }
  }, [content, autoScrollEnabled]);

  return {
    scrollRef,
    isAtBottom,
    autoScrollEnabled,
    scrollToBottom,
    disableAutoScroll,
  };
}
