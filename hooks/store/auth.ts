import { create } from 'zustand';
import { LoginUser } from '@/modules/contents/web.interface';
interface AuthState {
  user: LoginUser | null;
  setUser: (user: LoginUser | null) => void;
  signOut: () => void;
  isLoginMode: boolean;
  setIsLoginMode: (isLoginMode: boolean) => void;
  isLoginStatus: boolean;
  setIsLoginStatus: (isLoginStatus: boolean) => void;
  hasPlatform: boolean;
  setHasPlatform: (hasPlatform: boolean) => void;
  prompt: string;
  setPrompt: (prompt: string) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  signOut: () => {
    set({ user: null });
    set({ hasPlatform: false });
    set({ isLoginStatus: false });
    localStorage.removeItem('isFirst');
  },
  isLoginMode: false,
  setIsLoginMode: (isLoginMode) => set({ isLoginMode }),
  isLoginStatus: false,
  setIsLoginStatus: (isLoginStatus) => set({ isLoginStatus }),
  hasPlatform: false,
  setHasPlatform: (hasPlatform) => set({ hasPlatform }),
  prompt: '',
  setPrompt: (prompt) => set({ prompt }),
}));
