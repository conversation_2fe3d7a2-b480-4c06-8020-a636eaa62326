const additionalUrls = [
  '/hide-comments',
  '/social-media-sentiment-analysis',
  '/pricing',
  '/blog',
  '/faq',
];
const env = process.env;
let policies = [];
if (env.NEXT_PUBLIC_APP_ENV === 'production') {
  policies.push(
    {
      userAgent: '*',
      allow: ['/'],
    },
    {
      userAgent: '*',
      disallow: [
        '/api/',
        '/account/',
        '/blog/[id]',
        '/dashboard/[ruleId]',
        '/$',
      ],
    },
  );
} else {
  policies.push({
    userAgent: '*',
    disallow: ['/'],
  });
}

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: env.NEXT_PUBLIC_SITE_URL,
  exclude: ['/server-sitemap.xml'],
  additionalPaths: async () => {
    const result = [];
    const locales = ['en', 'zh'];

    result.push({
      loc: '/',
      changefreq: 'daily',
      priority: 1,
      lastmod: new Date().toISOString(),
      alternateRefs: locales.map((locale) => {
        return {
          href:
            locale === 'en'
              ? env.NEXT_PUBLIC_SITE_URL
              : `${env.NEXT_PUBLIC_SITE_URL}/${locale}`,
          hreflang: locale,
        };
      }),
    });

    additionalUrls.forEach((url) => {
      result.push({
        loc: url,
        changefreq: 'daily',
        priority: 0.8,
        lastmod: new Date().toISOString(),
        alternateRefs: locales.map((locale) => {
          return {
            href:
              locale === 'en'
                ? env.NEXT_PUBLIC_SITE_URL
                : `${env.NEXT_PUBLIC_SITE_URL}/${locale}`,
            hreflang: locale,
          };
        }),
      });
    });

    return result;
  },
  generateRobotsTxt: true,
  robotsTxtOptions: {
    policies: policies,
    additionalSitemaps: [`${env.NEXT_PUBLIC_SITE_URL}/server-sitemap.xml`],
  },
};
