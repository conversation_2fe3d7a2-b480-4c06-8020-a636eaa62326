import { Inter } from 'next/font/google';
import './globals.css';
import { GoogleAnalytics } from '@next/third-parties/google';
import { env } from '@/env';
import { TRPCProvider } from '@/trpc/client';
import { ClarityProvider } from '@/components/biz/ClarityProvider';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased relative`}>
        <ClarityProvider clarityId={env.NEXT_PUBLIC_CLARIFY_ID} />
        <TRPCProvider>{children}</TRPCProvider>
      </body>
      <GoogleAnalytics gaId={env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID} />
    </html>
  );
}
