@import 'nprogress/nprogress.css';
#nprogress .bar {
  background: #6246ea !important;
  height: 3px !important;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 250 47% 100%;
    --foreground: 250 5% 10%;
    --card: 250 47% 100%;
    --card-foreground: 250 5% 15%;
    --popover: 250 47% 100%;
    --popover-foreground: 250 95% 10%;
    --primary: 250 79.6% 59.6%;
    --primary-foreground: 0 0% 100%;
    --secondary: 250 30% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 212 30% 95%;
    --muted-foreground: 250 5% 40%;
    --accent: 212 30% 90%;
    --accent-foreground: 250 5% 15%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 250 5% 100%;
    --border: 250 30% 82%;
    --input: 250 30% 50%;
    --ring: 250 79.6% 59.6%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 250 47% 10%;
    --foreground: 250 5% 100%;
    --card: 250 47% 10%;
    --card-foreground: 250 5% 100%;
    --popover: 250 47% 5%;
    --popover-foreground: 250 5% 100%;
    --primary: 250 79.6% 59.6%;
    --primary-foreground: 0 0% 100%;
    --secondary: 250 30% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 212 30% 25%;
    --muted-foreground: 250 5% 65%;
    --accent: 212 30% 25%;
    --accent-foreground: 250 5% 95%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 250 5% 100%;
    --border: 250 30% 50%;
    --input: 250 30% 50%;
    --ring: 250 79.6% 59.6%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
