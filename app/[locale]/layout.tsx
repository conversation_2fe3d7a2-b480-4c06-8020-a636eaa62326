import { routing } from '@/i18n/routing';
import { notFound } from 'next/navigation';
import { getMessages } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { I18nProps } from '@/i18n/locale';
import React from 'react';
import { Toaster } from 'sonner';
import AuthProvider from '@/components/biz/AuthProvider';

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<I18nProps>;
}>) {
  const { locale } = await params;
  if (!routing.locales.includes(locale as string)) {
    notFound();
  }

  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          <AuthProvider>
            <div>{children}</div>
            <Toaster position="top-center" richColors />
          </AuthProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
