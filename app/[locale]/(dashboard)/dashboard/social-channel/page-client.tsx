'use client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { linkWithFacebook } from '@/modules/auth/supabase';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
interface Platform {
  img: string;
  title: string;
  description: string;
}
function SocialChannelPage() {
  const t = useTranslations('social_channel');

  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>({
    img: '/images/social-channel/facebook.webp',
    title: 'Facebook',
    description: t('LinkDesc'),
  });
  const dataList: Platform[] = [
    {
      img: '/images/social-channel/facebook.webp',
      title: 'Facebook',
      description: t('LinkDesc'),
    },
    {
      img: '/images/social-channel/ins.webp',
      title: 'Instagram',
      description: t('LinkDesc'),
    },
    {
      img: '/images/social-channel/x.webp',
      title: 'X',
      description: t('LinkDesc'),
    },
    {
      img: '/images/social-channel/onfans.webp',
      title: 'Linkedin',
      description: t('LinkDesc'),
    },
  ];
  return (
    <div>
      <div className="flex items-center gap-2 mb-4">
        <Link href="/dashboard/bind-account">{t('user_comments')}</Link>
        <span>{'>'}</span>
        <span className="text-xl font-medium">
          {t('connect_social_channels')}
        </span>
      </div>
      <div className="bg-white rounded-lg shadow h-[calc(100vh-80px)]">
        <div className="p-6">
          <p className="text-xl font-semibold">
            {t('connect_social_channels')}
          </p>
          <p className="text-gray-500 font-medium">
            {t(
              'connect_your_account_to_enable_automated_bulk_management_and_comment_moderation',
            )}
          </p>
        </div>
        <div className="border-t border-gray-300"></div>
        <div className="px-6 py-10 bg-[#F9FAFF] h-full">
          <div className="bg-gray-50 min-h-screen flex flex-col  p-4">
            <div className="flex flex-wrap gap-4 justify-center">
              {/* Facebook Card */}
              {dataList.map((item, index) => (
                <div
                  key={index}
                  className={`bg-white relative rounded-lg overflow-hidden border-2 hover:border-primary cursor-pointer border-gray-200 w-[280px] ${
                    selectedPlatform?.title === item.title
                      ? 'border-primary'
                      : ''
                  }`}
                  onClick={() => {
                    if (index <= 1) {
                      setSelectedPlatform(item);
                    }
                  }}
                >
                  {index > 1 && (
                    <div className="absolute top-3 right-3 bg-gray-100 rounded-full px-2.5 py-0.5 text-xs font-medium flex items-center gap-1">
                      <span className="inline-block w-2 h-2 bg-primary rounded-full bg-gray-500"></span>
                      <span className="inline-flex items-center  text-gray-800">
                        {t('coming_soon')}
                      </span>
                    </div>
                  )}
                  <div
                    className="p-6 flex justify-center items-center bg-cover bg-center h-[150px]"
                    style={{ backgroundImage: `url(${item.img})` }}
                  ></div>
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {item.title}
                    </h3>
                    <p className="mt-2 text-sm text-gray-500 mb-10">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-14 flex flex-col items-center">
              <Button
                className="bg-primary hover:bg-primary/80 text-white px-6 py-2 rounded-md flex items-center gap-1"
                onClick={() => {
                  linkWithFacebook();
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-link-icon lucide-link"
                >
                  <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                  <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                </svg>
                {t('link_new_platform')}
              </Button>

              <p className="mt-8 text-base text-black font-medium">
                {t('no_agent_matching_your_needs')}{' '}
                <a href="#" className="text-[#6366F1] hover:underline">
                  {t('feedback')}
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default dynamic(() => Promise.resolve(SocialChannelPage), {
  ssr: false,
});
