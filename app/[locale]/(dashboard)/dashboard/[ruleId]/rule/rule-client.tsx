'use client';

import { useState, useEffect } from 'react';
import { trpc } from '@/trpc/client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, SmilePlus } from 'lucide-react';
import { toast } from 'sonner';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  EmojiPickerContent,
  EmojiPickerFooter,
  EmojiPickerSearch,
} from '@/components/ui/emoji-picker';
import { EmojiPicker as EmojiPickerPrimitive } from 'frimousse';

export const RuleClient = ({ ruleId }: { ruleId: string }) => {
  const utils = trpc.useUtils();
  const { data: rule } = trpc.rule.getRuleById.useQuery({
    id: ruleId,
  });

  const updateRuleMutation = trpc.rule.updateRule.useMutation({
    onSuccess: () => {
      utils.rule.getRuleById.invalidate({ id: ruleId });
      toast.success('更新成功');
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const [processComment, setProcessComment] = useState<string>('');

  const processCommentRuleMutation = trpc.rule.processCommentRule.useMutation({
    onSuccess: (data) => {
      toast.success(data.map((item) => item.hitRule).join(', '));
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const [activeTab, setActiveTab] = useState('moderation');
  const [url, setUrl] = useState<string>('');
  const [urlList, setUrlList] = useState<string[]>([]);

  useEffect(() => {
    if (rule?.urls) {
      setUrlList(rule.urls.split(',').filter((url) => url.trim() !== ''));
    }
  }, [rule?.urls]);

  const [hashtag, setHashtag] = useState<string>('');
  const [hashtagList, setHashtagList] = useState<string[]>([]);

  useEffect(() => {
    if (rule?.tags) {
      setHashtagList(rule.tags.split(',').filter((tag) => tag.trim() !== ''));
    }
  }, [rule?.tags]);

  const [emoji, setEmoji] = useState<string>('');
  const [emojiList, setEmojiList] = useState<string[]>([]);

  useEffect(() => {
    if (rule?.emojis) {
      setEmojiList(
        rule.emojis.split(',').filter((emoji) => emoji.trim() !== ''),
      );
    }
  }, [rule?.emojis]);

  const [keyword, setKeyword] = useState<string>('');
  const [keywordList, setKeywordList] = useState<string[]>([]);

  useEffect(() => {
    if (rule?.keywords) {
      setKeywordList(
        rule.keywords.split(',').filter((keyword) => keyword.trim() !== ''),
      );
    }
  }, [rule?.keywords]);

  const addUrl = () => {
    if (url && !urlList.includes(url)) {
      setUrlList([...urlList, url]);
      setUrl('');
    }
  };

  const removeUrl = (url: string) => {
    setUrlList(urlList.filter((u) => u !== url));
  };

  const addKeyword = () => {
    if (keyword && !keywordList.includes(keyword)) {
      setKeywordList([...keywordList, keyword]);
      setKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setKeywordList(keywordList.filter((k) => k !== keyword));
  };

  const addHashtag = () => {
    if (hashtag && !hashtagList.includes(hashtag)) {
      setHashtagList([...hashtagList, hashtag]);
      setHashtag('');
    }
  };

  const removeHashtag = (tag: string) => {
    setHashtagList(hashtagList.filter((t) => t !== tag));
  };

  const addEmoji = () => {
    if (emoji && !emojiList.includes(emoji)) {
      setEmojiList([...emojiList, emoji]);
      setEmoji('');
    }
  };

  const removeEmoji = (emoji: string) => {
    setEmojiList(emojiList.filter((e) => e !== emoji));
  };

  const ToggleSwitch = ({
    value,
    onChange,
  }: {
    value: boolean;
    onChange: () => void;
  }) => (
    <div
      className={`w-12 h-6 rounded-full p-1 cursor-pointer ${value ? 'bg-green-500' : 'bg-gray-300'}`}
      onClick={onChange}
    >
      <div
        className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform ${value ? 'translate-x-6' : 'translate-x-0'}`}
      />
    </div>
  );

  return (
    <div className="bg-slate-50 p-4 rounded-lg">
      <div className="flex mb-4 border-b">
        <button
          className={`px-4 py-2 ${activeTab === 'comments' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'}`}
          onClick={() => setActiveTab('comments')}
        >
          Comments
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'moderation' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'}`}
          onClick={() => setActiveTab('moderation')}
        >
          Moderation
        </button>
      </div>

      {activeTab === 'comments' && <div>{rule?.id}</div>}

      {activeTab === 'moderation' && (
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Configure your preferences easily
          </div>

          <div className="text-xl font-semibold mb-2">Moderation Settings</div>
          <div className="text-sm text-muted-foreground mb-6">
            Define the moderation settings that will be used to process new
            comments you&apos;ll receive on your posts and ads.
          </div>

          {/* Profanity and Negativity Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4 flex items-center justify-between">
              <span>Hide profanity</span>
              <ToggleSwitch
                value={rule?.hideProfanityStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideProfanityStatus: !rule?.hideProfanityStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </Card>
            <Card className="p-4 flex items-center justify-between">
              <span>Hide negativity</span>
              <ToggleSwitch
                value={rule?.hideNegativityStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideNegativityStatus: !rule?.hideNegativityStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </Card>
          </div>

          {/* Email/Phone and Images Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4 flex items-center justify-between">
              <span>Hide emails and phone numbers</span>
              <ToggleSwitch
                value={rule?.hideEmailAndPhoneNumberStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideEmailAndPhoneNumberStatus:
                        !rule?.hideEmailAndPhoneNumberStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </Card>
            <Card className="p-4 flex items-center justify-between">
              <span>Hide images</span>
              <ToggleSwitch
                value={rule?.hideImageStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideImageStatus: !rule?.hideImageStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </Card>
          </div>

          {/* URLs Section */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span>Hide URLs</span>
              <ToggleSwitch
                value={rule?.hideUrlStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideUrlStatus: !rule?.hideUrlStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </div>
            {rule?.hideUrlStatus && (
              <>
                <div className="relative mb-4">
                  <Input
                    placeholder="Type here (press enter after each URL)"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && addUrl()}
                  />
                  {urlList.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {urlList.map((url, index) => (
                        <Badge
                          key={index}
                          className="flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-100"
                        >
                          {url}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeUrl(url)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground mb-4">
                  Here you can add hashtags you&apos;d like to exclude from this
                  filter.
                </div>
                <Button
                  variant="default"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                  onClick={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        urls: urlList.join(','),
                      },
                      id: rule?.id as string,
                    })
                  }
                >
                  Save
                </Button>
              </>
            )}
          </Card>

          {/* Mentions Section */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-1">
              <span>Hide mentions</span>
              <ToggleSwitch
                value={rule?.hideMentionStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideMentionStatus: !rule?.hideMentionStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </div>
            <div className="text-xs text-muted-foreground">
              Advanced settings (by default we hide all mentions)
            </div>
          </Card>

          {/* Hashtags Section */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-4">
              <span>Hide hashtags</span>
              <ToggleSwitch
                value={rule?.hideHashTags ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideHashTags: !rule?.hideHashTags,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </div>
            {rule?.hideHashTags && (
              <>
                <div className="relative mb-4">
                  <Input
                    placeholder="Type here (press enter after each hashtag)"
                    value={hashtag}
                    onChange={(e) => setHashtag(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && addHashtag()}
                  />
                  {hashtagList.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {hashtagList.map((tag, index) => (
                        <Badge
                          key={index}
                          className="flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-100"
                        >
                          {`#${tag}`}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeHashtag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground mb-4">
                  Here you can add hashtags you&apos;d like to exclude from this
                  filter.
                </div>
                <Button
                  variant="default"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                  onClick={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        tags: hashtagList.join(','),
                      },
                      id: rule?.id as string,
                    })
                  }
                >
                  Save
                </Button>
              </>
            )}
          </Card>

          {/* Emojis Section */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-4">
              <span>Hide emojis</span>
              <ToggleSwitch
                value={rule?.hideEmojisStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideEmojisStatus: !rule?.hideEmojisStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </div>
            {rule?.hideEmojisStatus && (
              <>
                <div className="mb-4">
                  <div className="flex items-center border rounded-md">
                    <div className="relative flex-1">
                      <Input
                        placeholder="Enter your emojis here"
                        value={emoji}
                        onChange={(e) => setEmoji(e.target.value)}
                        className="pr-10"
                        onKeyDown={(e) => e.key === 'Enter' && addEmoji()}
                      />
                      <Popover>
                        <PopoverTrigger asChild>
                          <button
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            type="button"
                          >
                            <SmilePlus className="h-5 w-5" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="p-0 w-[280px] h-[280px]"
                          align="end"
                        >
                          <EmojiPickerPrimitive.Root
                            className="bg-popover text-popover-foreground isolate flex h-full w-full flex-col overflow-hidden rounded-md"
                            onEmojiSelect={(emoji) => {
                              if (emoji && emoji.emoji) {
                                setEmoji(emoji.emoji);
                                setEmojiList([...emojiList, emoji.emoji]);
                              }
                            }}
                          >
                            <EmojiPickerSearch placeholder="Search emojis..." />
                            <EmojiPickerContent />
                            <EmojiPickerFooter />
                          </EmojiPickerPrimitive.Root>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {emojiList.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {emojiList.map((emoji, index) => (
                        <Badge
                          key={index}
                          className="flex items-center gap-1 bg-gray-100 text-gray-800 hover:bg-gray-100"
                        >
                          {emoji}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeEmoji(emoji)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <Button
                  variant="default"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                  onClick={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        emojis: emojiList.join(','),
                      },
                      id: rule?.id as string,
                    })
                  }
                >
                  Save
                </Button>
              </>
            )}
          </Card>

          {/* Keywords and All Comments Section */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-4">
              <span>Hide Keywords</span>
              <ToggleSwitch
                value={rule?.hideKeywordsStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideKeywordsStatus: !rule?.hideKeywordsStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </div>
            {rule?.hideKeywordsStatus && (
              <>
                <div className="relative mb-4">
                  <Input
                    placeholder="Type here (press enter after each keyword)"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && addKeyword()}
                  />
                  {keywordList.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {keywordList.map((key, index) => (
                        <Badge
                          key={index}
                          className="flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-100"
                        >
                          {key}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeKeyword(key)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground mb-4">
                  Here you can add hashtags you&apos;d like to exclude from this
                  filter.
                </div>
                <Button
                  variant="default"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                  onClick={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        keywords: keywordList.join(','),
                      },
                      id: rule?.id as string,
                    })
                  }
                >
                  Save
                </Button>
              </>
            )}
          </Card>

          <div>
            <Card className="p-4 flex items-center justify-between">
              <span>Hide all comments</span>
              <ToggleSwitch
                value={rule?.hideAllCommentsStatus ?? false}
                onChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideAllCommentsStatus: !rule?.hideAllCommentsStatus,
                    },
                    id: rule?.id as string,
                  })
                }
              />
            </Card>
          </div>

          {/* Test Section */}
          <Card className="p-4">
            <div className="mb-4">
              <div className="font-medium">Test Your Moderation Settings</div>
              <div className="text-xs text-muted-foreground">
                Here you can test comments to see if they would trigger your
                enabled moderation settings.
              </div>
            </div>
            <div className="mb-4">
              <textarea
                value={processComment}
                onChange={(e) => setProcessComment(e.target.value)}
                placeholder="Type your comment here..."
                className="w-full p-2 border rounded-lg h-24"
              />
            </div>
            <Button
              variant="default"
              className="bg-indigo-600 hover:bg-indigo-700 text-white"
              onClick={() =>
                processCommentRuleMutation.mutate({
                  id: rule?.id as string,
                  message: processComment,
                })
              }
              disabled={processCommentRuleMutation.isPending}
            >
              Process
            </Button>
          </Card>
        </div>
      )}
    </div>
  );
};
