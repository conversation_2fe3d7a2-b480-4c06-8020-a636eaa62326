'use client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { trpc } from '@/trpc/client';
import { Check } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useAuthStore } from '@/hooks/store/auth';
import { useCallback, useState, useRef } from 'react';
import { Plan } from '@/modules/stripe/plan';
import { SubscribeButton } from '@/components/SubscribeButton';
import { cn } from '@/lib/utils';
import UpgradeModal from '@/components/UpgradeModal';
export default function SettingPricePage() {
  const [data] = trpc.stripe.getProducts.useSuspenseQuery(undefined, {
    retry: 3,
  });

  const { user, isLoginStatus, setIsLoginMode } = useAuthStore();

  const isUpgrade = useCallback(
    (plan: Plan) => {
      const curIndex = data.findIndex(
        (item) => item.name === user?.currentPlan,
      );
      const planIndex = data.findIndex((item) => item.name === plan.name);
      return (curIndex || -1) <= planIndex;
    },
    [user],
  );
  const t = useTranslations('Pricing');
  const currentPlan = data.find((item) => item.name === user?.currentPlan);

  const createCustomerPortalSession =
    trpc.stripe.createCustomerPortalSession.useMutation({
      onSuccess: (url) => {
        if (url) {
          window.location.href = url;
        }
      },
    });
  const [open, setOpen] = useState(false);
  const priceIdRef = useRef<string>('');
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-lg font-medium mb-2">{t('Upgrade&renew')}</h1>
        <div className="mt-6 rounded-lg overflow-hidden">
          <div className="relative overflow-hidden flex justify-between bg-[#1e2130] text-white p-6 rounded-t-lg">
            <div className="absolute top-[-200px] right-0 scale-150 h-[500px] w-[500px] bg-primary transform rotate-6 translate-x-1/4 translate-y-0 [clip-path:polygon(50%_0%,61%_35%,98%_35%,68%_57%,79%_91%,50%_70%,21%_91%,32%_57%,2%_35%,39%_35%)]"></div>
            <div className="relative z-10">
              <div className="flex items-center gap-2">
                <span className="font-medium">{t('currentPlan')}</span>
                <Badge className="bg-blue-600 hover:bg-blue-600 text-white">
                  {user?.currentPlan || 'free'}
                </Badge>
              </div>
              <div className="mt-1 flex items-baseline">
                <span className="font-medium">
                  ${currentPlan?.f_value || '0'}/month
                </span>
                <span className="ml-2 text-sm text-gray-400">
                  {t('billedMonthly')}
                </span>
              </div>
              <div className="mt-1 text-sm text-gray-400">
                {t('upgradeToEnjoyMoreServicesAndQuantity')}
              </div>
            </div>
            <div className="relative z-10">
              {user?.currentPlan !== 'Free' && (
                <Button
                  variant="outline"
                  onClick={() => createCustomerPortalSession.mutate()}
                  className="bg-white text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={createCustomerPortalSession.isPending}
                >
                  {t('myInvoices')}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {data.map((item) => (
            <div
              key={item.id}
              className={`border-2 rounded-lg overflow-hidden cursor-pointer ${
                user?.currentPlan === item.name
                  ? 'border-primary'
                  : 'border-gray-200'
              } px-4  hover:border-primary`}
            >
              <div className="pt-4">
                <h2 className="font-medium mb-1">{t(item.f_name)}</h2>
                <div className="flex items-baseline mb-4">
                  <span className="text-3xl font-bold">${item.f_value}</span>
                  <span className="text-gray-500 ml-1">/{t('month')}</span>
                </div>
                <div
                  className={cn('text-sm text-gray-500 mb-4', {
                    'opacity-0': item.id === 'free',
                  })}
                >
                  {t('billedMonthly')}
                </div>
                <SubscribeButton
                  priceId={item.priceId}
                  isActive={user?.currentPlan === item.name}
                  isLoginStatus={isLoginStatus}
                  setIsLoginMode={setIsLoginMode}
                  isUpgrade={isUpgrade(item)}
                  user={user}
                  openModal={() => setOpen(true)}
                  setPriceId={(priceId: string) => {
                    priceIdRef.current = priceId;
                  }}
                >
                  {!isLoginStatus ? (
                    <span>{t('getStarted')}</span>
                  ) : user?.currentPlan === item.name ? (
                    <span>{t('currentPlan')}</span>
                  ) : isUpgrade(item) ? (
                    <span>{t('upgrade')}</span>
                  ) : (
                    <span>{t('getStarted')}</span>
                  )}
                </SubscribeButton>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>
                      {' '}
                      <span className="text-primary">{item.credits}</span>{' '}
                      {t('creditMonth')}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('unlimitedPages')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('unlimitedSentimentAnalysis')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('automaticallyHideNegativeCommentsAndSpam')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('aiPoweredDataAnalysisJobs')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('commentManagement')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                    <span>{t('customizableModerationSettings')}</span>
                  </li>
                </ul>
              </div>
              <div className="flex justify-between mb-2">
                <div className="text-sm text-gray-500">{t('service')}</div>
                <div className="text-sm text-gray-500">{t('creditCost')}</div>
              </div>

              <div className="border-t-2 border-[#E7E7E7]">
                <div className="flex justify-between py-4 gap-4">
                  <div className="text-sm text-left">
                    {t('aiDataAnalysisTask')}
                  </div>
                  <div className="text-sm text-right">
                    <span className="text-primary">10</span> {t('creditEach')}
                  </div>
                </div>
              </div>

              <div className="border-t-2 border-[#E7E7E7]">
                <div className="flex justify-between py-4 gap-4">
                  <div className="text-sm text-left">
                    {t('commentProcessing')}
                  </div>
                  <div className="text-sm text-right">
                    1 {t('creditPer')} <span className="text-primary">1</span>{' '}
                    <span>{t('comment')}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <UpgradeModal
        open={open}
        onOpenChange={setOpen}
        priceId={priceIdRef.current}
      />
    </div>
  );
}
