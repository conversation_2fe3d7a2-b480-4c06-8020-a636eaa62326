'use client';
import Chat from '@/components/chat';
import LoadingSlot from '@/components/loading-slot';
import { UIMessage } from 'ai';

export default function ChatClient({
  id,
  initialMessages,
  pageIdMap,
}: {
  id: string;
  initialMessages: UIMessage[];
  pageIdMap: Map<string, string>;
}) {
  return (
    <LoadingSlot>
      <div className="h-full">
        <Chat id={id} initialMessages={initialMessages} pageIdMap={pageIdMap} />
      </div>
    </LoadingSlot>
  );
}
