import { DBMessage } from '@/db/schema';
import { getMessagesByChatId } from '@/modules/chat/service';
import { Attachment, UIMessage } from 'ai';
import ChatClient from './chat-client';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
function convertToUIMessages(messages: Array<DBMessage>): Array<UIMessage> {
  return messages.map((message) => ({
    id: message.id,
    parts: message.parts as UIMessage['parts'],
    role: message.role as UIMessage['role'],
    // Note: content will soon be deprecated in @ai-sdk/react
    content: '',
    createdAt: message.createdAt,
    experimental_attachments: (message.attachments as Array<Attachment>) ?? [],
  }));
}

export default async function ChatPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session) {
    redirect('/home?logout=true');
  }
  const { id } = await params;
  // 获取message信息
  const messages = await getMessagesByChatId({ id });

  const initialMessages = convertToUIMessages(messages);
  const pageIdMap = new Map<string, string>();

  messages.forEach((message) => {
    if (message.externalPageId) {
      pageIdMap.set(message.id, message.externalPageId);
    }
  });
  return (
    <div className="h-[calc(100vh-64px)]">
      <ChatClient
        id={id}
        initialMessages={initialMessages}
        pageIdMap={pageIdMap}
      />
    </div>
  );
}
