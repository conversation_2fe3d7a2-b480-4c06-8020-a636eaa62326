'use client';
import Link from 'next/link';
import { useState } from 'react';
import Image from 'next/image';
import { Check, Loader2 } from 'lucide-react';
import { trpc } from '@/trpc/client';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
export default function SocialChannelConfigurationPage() {
  const [selectedOption, setSelectedOption] = useState<number>(0);
  const router = useRouter();
  const t = useTranslations('SocialChannelConfiguration');
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const createRecommandRule = trpc.rule.createRecommandRule.useMutation({
    onSuccess: () => {
      toast.success(t('create_recommand_rule_success'));
      router.push('/dashboard/bind-account');
    },
  });

  const { data: pages, isLoading } = trpc.socialPage.getSoicalPages.useQuery(
    {
      externalPageId: undefined,
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  const configs = [
    {
      name: t('aiConfigurationHelper'),
      description: t('aiConfigurationHelperDescription'),
      model: t('model'),
      img: '/images/social-channel-configuration/3.webp',
    },
    {
      name: t('manuallyConfigure'),
      description: t(
        'manuallyConfigureBlockingRulesForNegativeCommentsKeywordsAndSensitiveContentDescription',
      ),
      img: '/images/social-channel-configuration/4.webp',
    },
  ];

  return (
    <div>
      <div className="flex items-center gap-2 mb-4">
        <Link href="/dashboard/bind-account">
          {t('bind_to_the_new_platform')}
        </Link>
        <span>{'>'}</span>
        <Link href="/dashboard/social-channel">
          {t('connect_social_channels')}
        </Link>
        <span>{'>'}</span>
        <span className="text-xl font-medium">{t('configuration')}</span>
      </div>
      <div className="bg-white rounded-lg shadow h-[calc(100vh-80px)] p-6 overflow-y-auto [&::-webkit-scrollbar]:hidden ">
        <div className="max-w-4xl mx-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              {t('configuration')}
            </h1>
            <p className="text-gray-600">{t('configuration_description')}</p>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-[100px] border border-gray-200 rounded-lg mb-6">
              <Loader2 className="w-8 h-8 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {pages?.map((data) => {
                const { page } = data;
                return (
                  <div
                    key={page.id}
                    className={`bg-white relative bg-left-top bg-[size:70px] hover:border-primary bg-no-repeat rounded-lg border border-gray-200 p-4 cursor-pointer ${
                      page.platform === 'facebook'
                        ? "bg-[url('/images/social-channel-configuration/facebook.webp')]"
                        : "bg-[url('/images/social-channel-configuration/ins.webp')]"
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Image
                        src={page.avatarUrl || ''}
                        alt={page.pageName || ''}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                      <div>
                        <div
                          className="text-lg font-medium cursor-pointer hover:underline"
                          onClick={() => {
                            window.open(page.link || '', '_blank');
                          }}
                        >
                          {page.pageName}
                        </div>
                        <div className="text-sm text-gray-500">
                          @{page.pageName}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {configs.map((config, index) => {
              return (
                <div
                  key={config.name}
                  onClick={() => setSelectedOption(index)}
                  className={`p-4 cursor-pointer border-2 border-gray-200 rounded-lg h-fit ${
                    index == selectedOption && 'border-primary'
                  } hover:border-primary`}
                >
                  <div
                    className="bg-no-repeat relative bg-[size:250px] h-[200px] bg-right-bottom border-dashed border border-gray-200 p-4 rounded-lg"
                    style={{
                      backgroundImage: `url(${config.img})`,
                    }}
                  >
                    <p className="text-lg font-bold mb-4">{config.name}</p>
                    <p className="text-sm text-gray-500 mb-4">
                      {config.description}
                    </p>
                    {config.model && (
                      <p className="text-sm text-gray-500 w-[150px]">
                        {config.model}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center justify-center gap-2 mt-4">
                    <div
                      className={`w-8 h-8 border-2 border-gray-200 rounded-full  text-white flex items-center justify-center ${
                        index == selectedOption && 'bg-green-500'
                      }`}
                      onClick={() => setSelectedOption(index)}
                    >
                      {index == selectedOption && <Check className="w-5 h-5" />}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="flex flex-col items-center gap-4 mt-10 mb-4">
            <button
              className="bg-primary text-white py-2 px-4 rounded-md w-full max-w-xs flex items-center justify-center"
              onClick={() => {
                if (selectedOption === 0) {
                  setIsBtnLoading(true);
                  const timer = setTimeout(() => {
                    router.push('/dashboard/bind-account');
                    setIsBtnLoading(false);
                    clearTimeout(timer);
                  }, 1000);
                } else {
                  createRecommandRule.mutate({
                    pageIds: pages?.map((data) => data.page.id) || [],
                  });
                }
              }}
            >
              {isBtnLoading || createRecommandRule.isPending ? (
                <Loader2 className="w-6 h-6 animate-spin" />
              ) : (
                t('next')
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
