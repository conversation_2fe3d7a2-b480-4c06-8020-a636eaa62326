'use client';
import Image from 'next/image';
import { ChevronDown, ArrowRight } from 'lucide-react';
import { useTranslations } from 'next-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { trpc } from '@/trpc/client';
import { cn, PlatImageMap } from '@/lib/utils';
import { useMemo, useState } from 'react';
import { PlatformPageRoot } from '@/modules/contents/web.interface';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import ChatInput from '@/components/chatInput';

export default function DashboardContent() {
  const router = useRouter();
  const t = useTranslations('Dashboard');

  // 选择的平台
  const [selectedPlatform, setSelectedPlatform] =
    useState<PlatformPageRoot | null>(null);

  const { data: pages } = trpc.data.platformData.useQuery({
    externalPageId: selectedPlatform?.page.externalPageId || undefined,
  });

  // 获取账号名称列表
  const { data: accountList } = trpc.socialPage.getSoicalPages.useQuery(
    {
      externalPageId: undefined,
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  const summaryRadio = useMemo(() => {
    const { totalPositive, totalNegative, totalNeutral } =
      pages?.emotions || {};

    const E =
      ((totalPositive as number) - (totalNegative as number)) /
      ((((totalPositive as number) + (totalNegative as number)) as number) +
        (totalNeutral as number));
    return E + 0.5;
  }, [pages]);

  const summaryMessage = useMemo(() => {
    // from-red-400 via-yellow-400 to-green-400
    if (summaryRadio >= 1) {
      return {
        desc: `Your comments' average sentiment: <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Awesome).`,
        color: 'text-green-700',
      };
    } else if (summaryRadio >= 0.8 && summaryRadio <= 1) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Pretty good)`,
        color: 'text-green-500',
      };
    } else if (summaryRadio > 0.5 && summaryRadio < 0.8) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Good).`,
        color: 'text-primary',
      };
    } else if (summaryRadio === 0.5) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Meh)`,
        color: 'text-primary',
      };
    } else if (summaryRadio > 0.2 && summaryRadio < 0.5) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span>(Slightly off)`,
        color: 'text-primary',
      };
    } else if (summaryRadio < 0.2 && summaryRadio >= 0) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Not great)`,
        color: 'text-primary',
      };
    } else if (summaryRadio < 0) {
      return {
        desc: `Score <span class="text-primary">${(summaryRadio * 100).toFixed(2)}</span> (Terrible)`,
        color: 'text-primary',
      };
    }
  }, [summaryRadio]);

  const chartData = useMemo(() => {
    if (!pages?.commentsData) return [];

    const groupedData = pages.commentsData.reduce(
      (acc, item) => {
        const date = new Date(item.date).toLocaleDateString();
        if (!acc[date]) {
          acc[date] = {
            date,
            positive: item.positiveNumber || 0,
            neutral: item.neutralNumber || 0,
            negative: item.negativeNumber || 0,
            total: item.total || 0,
          };
        }
        return acc;
      },
      {} as Record<
        string,
        {
          date: string;
          positive: number;
          neutral: number;
          negative: number;
          total: number;
        }
      >,
    );

    return Object.values(groupedData).sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    );
  }, [pages?.commentsData]);

  return (
    <div className="flex flex-col">
      <h1 className="text-2xl font-bold mb-4">{t('title')}</h1>
      <div className="grid grid-cols-12 gap-4 ">
        {/* AI Sentiment Analysis Card */}
        <div className="col-span-5 row-span-2 bg-white rounded-xl rounded-br-[50px] rounded-tl-[50px] p-6 relative bg-[url('/images/dashboard/ai-analysis-bg.webp')] bg-cover bg-center bg-no-repeat">
          <div className="z-10 relative">
            <h2 className="text-xl font-bold">{t('ai_sentiment_analysis')}</h2>
            <p className="text-gray-600 text-base mt-1">
              {t(
                'automatically_detect_positive_negative_or_neutral_sentiments_in_comments',
              )}
            </p>
            <div className="mt-6 flex gap-2">
              {/* <button className="bg-primary text-white px-4 py-2 rounded-md flex items-center">
                <span className="mr-2">{t('try_demo')}</span>
              </button> */}
              <button
                className="bg-white border border-gray-300 px-4 py-2 rounded-md hover:text-primary"
                onClick={() => {
                  router.push('/dashboard/chat');
                }}
              >
                {t('get_started')}
              </button>
            </div>
          </div>
          <div className="absolute right-0 -top-6">
            <Image
              src="/images/dashboard/ai-analysis.webp"
              width={100}
              height={100}
              alt="Analytics illustration"
            />
          </div>
        </div>
        {/* Resources Card */}
        <div className="col-span-4 row-span-2 bg-white rounded-xl p-6">
          <h2 className="text-xl font-bold">{t('resources')}</h2>
          <p className="text-gray-600 text-sm mt-1">
            {t('quick_access_to_helpful_information')}
          </p>
          <div className="mt-6 w-fit">
            <Link href="/faq">
              <div className="border border-gray-200 p-3  rounded-lg flex  gap-3 items-center justify-center cursor-pointer">
                <Image
                  src="/images/dashboard/help-center.webp"
                  width={20}
                  height={20}
                  alt="help center"
                />
                <span className="text-sm font-bold hover:text-primary">
                  {t('help_center')}
                </span>
                <ArrowRight className="w-4 h-4 hover:text-primary" />
              </div>
            </Link>
          </div>
        </div>

        {/* Account Management Card */}
        <div className="col-span-3 row-span-2 bg-white rounded-xl p-6">
          <h2 className="text-xl font-bold">{t('bind_platform_management')}</h2>
          <p className="text-gray-600 text-sm mt-1">
            {t('link_your_accounts_to_get_started')}
          </p>
          <div className=" bg-white rounded-xl border border-gray-200 mt-6">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center justify-between cursor-pointer p-2">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center mr-2">
                      <Image
                        src={
                          PlatImageMap.get(
                            (selectedPlatform?.page.platform as string) || '',
                          ) as string
                        }
                        width={16}
                        height={16}
                        alt="platform"
                      />
                    </div>
                    <span>
                      {selectedPlatform?.page.pageName || t('all_platforms')}
                    </span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedPlatform(null);
                  }}
                >
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center mr-2">
                      <Image
                        src={
                          PlatImageMap.get(
                            (selectedPlatform?.page.platform as string) || '',
                          ) as string
                        }
                        width={16}
                        height={16}
                        alt="platform"
                      />
                    </div>
                    <span>{t('all_platforms')}</span>
                  </div>
                </DropdownMenuItem>
                {accountList?.map((item) => (
                  <DropdownMenuItem
                    key={item.page.id}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedPlatform(item as unknown as PlatformPageRoot);
                    }}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center mr-2">
                        <Image
                          src={
                            PlatImageMap.get(item.page.platform as string) || ''
                          }
                          width={16}
                          height={16}
                          alt="platform"
                        />
                      </div>
                      <span>{item.page.pageName}</span>
                    </div>
                  </DropdownMenuItem>
                ))}

                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedPlatform(null);
                  }}
                >
                  <div
                    className="flex items-center"
                    onClick={() => {
                      router.push('/dashboard/bind-account');
                    }}
                  >
                    <div className="w-6 h-6 rounded-md flex items-center justify-center mr-2">
                      <Image
                        src={'/images/dashboard/add-platform.webp'}
                        width={16}
                        height={16}
                        alt="instagram"
                      />
                    </div>
                    <span>{t('account_name')}</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Total Comments Card */}
        <div className="col-span-3 row-span-2 bg-white rounded-xl p-6">
          <h2 className="text-xl font-bold">{t('total_comments')}</h2>
          <div className="flex items-end gap-2 mt-2">
            <div className="text-green-500 text-4xl font-bold">
              {pages?.summary.total}
            </div>
            <div className="mb-1">
              {pages?.summary.increaseNumber &&
              pages?.summary.increaseNumber > 0
                ? `+${pages?.summary.increaseNumber}`
                : ''}
            </div>
          </div>
          <div className="text-gray-500 text-xs w-[fit-content] bg-white text-center border rounded-xl mt-1 px-1">
            <div
              dangerouslySetInnerHTML={{
                __html: summaryMessage?.desc || '',
              }}
            ></div>
          </div>

          <div className="mt-4 mb-3 text-xs text-gray-500">
            <div className="w-full h-2 bg-gray-200 rounded-full mt-1 relative">
              <div className="absolute inset-0 flex items-center justify-between px-1">
                <span className="text-[10px] text-red-500 mt-8">
                  {t('negative')}
                </span>
                <div
                  style={{
                    left: `${summaryRadio * 100}%`,
                    transform: 'translateX(-50%)',
                  }}
                  className={cn(
                    'h-6 w-8 bg-black rounded-full text-sm flex items-center justify-center text-white z-10 absolute',
                  )}
                >
                  you
                </div>
                <span className="text-[10px] text-green-500 mt-8">
                  {t('positive')}
                </span>
              </div>
              <div className="h-full bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Sentiment Analysis Card */}
        <div className="col-span-9 row-span-2 bg-white rounded-xl p-6">
          <h2 className="text-xl font-bold">{t('emotion_analysis')}</h2>
          <div className="grid grid-cols-3 gap-4 mt-4">
            <div className="flex items-start gap-3">
              <div className="text-1xl bg-[#E9F8EC] rounded-sm p-1">😊</div>
              <div>
                <div className="flex items-end gap-1 mb-2">
                  <span className="text-3xl font-bold">
                    {pages?.emotions.totalPositive}
                  </span>
                  <span className="text-sm text-gray-500">
                    +{pages?.emotions?.increasePositive}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {t('positive_comments')}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3 border-l border-gray-200 pl-4">
              <div className="text-1xl bg-[#F8E9EC] rounded-sm p-1">😡</div>
              <div>
                <div className="flex items-end gap-1 mb-2">
                  <span className="text-3xl font-bold">
                    {pages?.emotions.totalNegative}
                  </span>
                  <span className="text-sm text-gray-500">
                    +{pages?.emotions.increaseNegative}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {t('negative_comments')}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3 border-l border-gray-200 pl-4">
              <div className="text-1xl bg-[#F3EAFC] rounded-sm p-1">😐</div>
              <div>
                <div className="flex items-end gap-1 mb-2">
                  <span className="text-3xl font-bold">
                    {pages?.emotions.totalNeutral}
                  </span>
                  <span className="text-sm text-gray-500">
                    +{pages?.emotions.increaseNeutral}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {t('neutral_comments')}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Smart Insights Card */}
        <div className="col-span-3 row-span-10 bg-white rounded-xl p-6">
          <div className="flex items-center gap-2">
            <div className="text-xl font-bold relative">
              <div>{t('smart_insights')}</div>

              <div className="absolute -right-6 top-0 text-primary font-bold">
                AI
                <svg
                  className="absolute -right-3 top-0"
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    id="Star 1"
                    d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                    fill="#6246EA"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="font-medium text-lg">{t('summary')}</h3>
            <div className="border-t mt-2 pt-4 space-y-4 text-sm text-gray-600">
              {pages?.commentsData?.length ? (
                pages?.insights?.map((item, index) => (
                  <p key={index} className="hover:text-primary">
                    {item}
                  </p>
                ))
              ) : (
                <p>{t('no_comments_data')}</p>
              )}
            </div>
          </div>
        </div>

        {/* Sentiment Distribution Card */}
        <div className="col-span-9 row-span-10 bg-white rounded-xl p-6 h-[100%]">
          <h2 className="text-xl font-bold">
            {t('sentiment_distribution_over_time')}
          </h2>
          <p className="text-gray-500 text-sm">
            {t('daily_distribution_of_sentiments_of_all_platforms')}
          </p>

          <div className="mt-6 h-[460px] relative">
            <div className="absolute left-0 -top-4 text-xs text-gray-500">
              {t('sentiment_value')}
            </div>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  }}
                  itemStyle={{ fontSize: '12px' }}
                  labelStyle={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    marginBottom: '4px',
                  }}
                  formatter={(value, name) => {
                    const nameMap = {
                      positive: t('positive'),
                      neutral: t('neutral'),
                      negative: t('negative'),
                      total: t('total'),
                    };
                    return [value, nameMap[name as keyof typeof nameMap]];
                  }}
                  labelFormatter={(label) => `${label}`}
                />
                <Line
                  type="monotone"
                  dataKey="positive"
                  stroke="#154EBA"
                  strokeWidth={2}
                  dot={false}
                  name="positive"
                />
                <Line
                  type="monotone"
                  dataKey="neutral"
                  stroke="#3B9C77"
                  strokeWidth={2}
                  dot={false}
                  name="neutral"
                />
                <Line
                  type="monotone"
                  dataKey="negative"
                  stroke="#2A97C4"
                  strokeWidth={2}
                  dot={false}
                  name="negative"
                />
                <Line
                  type="monotone"
                  dataKey="total"
                  dot={false}
                  stroke="#B95D5A"
                  strokeDasharray="5 5"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
      <div className="sticky right-0 bottom-[20px] px-[20%]">
        <ChatInput isLoginStatus={true} isChat={false} />
      </div>
    </div>
  );
}
