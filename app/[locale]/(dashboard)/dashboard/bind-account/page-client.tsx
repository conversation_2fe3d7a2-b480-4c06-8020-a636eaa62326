'use client';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { trpc } from '@/trpc/client';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Loader2, MoreHorizontal } from 'lucide-react';
import ChatInput from '@/components/chatInput';
import { linkWithFacebook } from '@/modules/auth/supabase';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenu,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
export default function BindAccountPage() {
  const router = useRouter();
  const t = useTranslations('bind_account');
  const utils = trpc.useUtils();
  const { data: pages } = trpc.socialPage.getSoicalPages.useQuery(
    {
      externalPageId: undefined,
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  const bindStatusMutation = trpc.socialPage.changeBindStatus.useMutation({
    onSuccess: () => {
      utils.socialPage.getSoicalPages.invalidate();
      toast.success(t('toast.bind_status_changed'));
    },
  });
  const unlinkMutation = trpc.user.deleteDataByExternalPageId.useMutation({
    onSuccess: () => {
      utils.socialPage.getSoicalPages.invalidate();
      toast.success(t('toast.unlink_success'));
    },
  });
  const [isLoading, setIsLoading] = useState(false);
  const handleBind = async () => {
    if (isLoading) return;
    try {
      setIsLoading(true);
      await linkWithFacebook();
      setIsLoading(false);
      utils.user.currentUserInfo.invalidate();
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('toast.facebook_bind_failed');
      toast.error(errorMessage);
    }
  };
  return (
    <div className="relative flex flex-col">
      <h1 className="text-2xl font-bold mb-4">{t('adapters')}</h1>
      <div className="bg-white relative rounded-lg shadow h-[calc(100vh-80px)] overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        {/* aciton */}
        <div className="p-6">
          <button
            className={` text-white w-[220px] py-2 rounded-lg flex items-center justify-center ${
              isLoading
                ? 'opacity-50 cursor-not-allowed bg-primary/50'
                : 'bg-primary'
            }`}
            onClick={handleBind}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-6 w-6 animate-spin" />
            ) : (
              <>+ {t('connect_platform')}</>
            )}
          </button>
          <p className="text-sm text-gray-500 mt-2">
            {t('bind_platform_to_enjoy_automatic_comment_blocking_function')}
          </p>
        </div>
        {/* list */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex flex-wrap gap-6">
            {pages?.map((page) => {
              const isLoading =
                bindStatusMutation.isPending &&
                bindStatusMutation.variables?.pageId === page.page.id;
              return (
                <Card
                  key={page.page.id}
                  className="border-gray-200 relative w-[300px]"
                >
                  <CardHeader
                    className={`flex items-center justify-center pb-2  bg-no-repeat bg-right bg-cover rounded-t-lg`}
                    style={{
                      backgroundImage: `url(/images/bind-account/${page.page.platform}.webp)`,
                    }}
                  >
                    {page.page.bindStatus && (
                      <div className="absolute top-2 left-2 bg-white rounded-full px-3 py-1 flex items-center gap-2">
                        <span className="bg-green-500 rounded-full h-2 w-2 inline-block"></span>
                        <span className="text-sm">Linked</span>
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <MoreHorizontal className="h-4 w-4 text-white cursor-pointer hover:text-primary" />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => {
                              unlinkMutation.mutate({
                                externalPageId: page.page.externalPageId || '',
                              });
                            }}
                          >
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                id="tabler:unlink"
                                clipPath="url(#clip0_5107_8758)"
                              >
                                <path
                                  id="Vector"
                                  d="M14.1667 18.3334V16.6667M7.50008 12.5001L12.5001 7.50008M9.16675 5.00008L9.55258 4.55341C10.3341 3.77202 11.394 3.33308 12.4991 3.33316C13.6043 3.33324 14.6641 3.77233 15.4455 4.55383C16.2269 5.33534 16.6658 6.39524 16.6658 7.50038C16.6657 8.60551 16.2266 9.66535 15.4451 10.4467L15.0001 10.8334M10.8334 15.0001L10.5026 15.4451C9.71174 16.2265 8.64477 16.6647 7.533 16.6647C6.42122 16.6647 5.35425 16.2265 4.56341 15.4451C4.17351 15.0599 3.86395 14.6011 3.65267 14.0953C3.44139 13.5896 3.33259 13.0469 3.33259 12.4988C3.33259 11.9507 3.44139 11.4081 3.65267 10.9023C3.86395 10.3966 4.17351 9.9378 4.56341 9.55258L5.00008 9.16675M16.6667 14.1667H18.3334M1.66675 5.83341H3.33341M5.83341 1.66675V3.33341"
                                  stroke="#1D2129"
                                  strokeWidth="1.66667"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </g>
                              <defs>
                                <clipPath id="clip0_5107_8758">
                                  <rect width="20" height="20" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>

                            <span>Unlink</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="flex flex-col items-center">
                      <div className="relative">
                        {page.page.avatarUrl ? (
                          <Image
                            src={page.page.avatarUrl || ''}
                            alt={`${page.page.pageName} avatar`}
                            width={50}
                            height={50}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="rounded-full bg-gray-200 w-10 h-10"></div>
                        )}
                      </div>
                      <CardTitle className="mt-3">
                        <Link
                          href={page.page.link || '#'}
                          target="_blank"
                          className={cn('text-base text-black')}
                        >
                          @{page.page.pageName}
                        </Link>
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="mt-6">
                      <div
                        className="border border-gray-200 py-1 flex items-center justify-center rounded-sm mb-4 cursor-pointer hover:bg-gray-100"
                        onClick={() => {
                          router.push(
                            `/dashboard/comments?externalPageId=${page.page.externalPageId}&type=comments`,
                          );
                        }}
                      >
                        {t('comment_management')}
                      </div>
                      <div
                        className="border border-gray-200 py-1 flex items-center justify-center rounded-sm mb-4 cursor-pointer hover:bg-gray-100"
                        onClick={() => {
                          router.push(
                            `/dashboard/comments?externalPageId=${page.page.externalPageId}&type=moderation`,
                          );
                        }}
                      >
                        {t('block_settings')}
                      </div>
                      <div
                        className="border border-gray-200 py-1 flex items-center justify-center rounded-sm mb-4 cursor-pointer hover:bg-gray-100"
                        onClick={() => {
                          try {
                            bindStatusMutation.mutate({
                              pageId: page.page.id,
                              bindStatus: !page.page.bindStatus,
                            });
                          } catch {
                            toast.error(t('toast.bind_status_failed'));
                          }
                        }}
                      >
                        {isLoading ? (
                          <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                        ) : (
                          <span>
                            {page.page.bindStatus ? t('unbind') : t('bind')}
                          </span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>
      {/* 输入框 */}
      <div className="sticky right-0 bottom-[20px] px-[20%]">
        <ChatInput isLoginStatus={true} />
      </div>
    </div>
  );
}
