'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/hooks/store/auth';
import { useTranslations } from 'next-intl';
import { trpc } from '@/trpc/client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { updatePassword } from '@/modules/auth/supabase';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

export default function SettingClient() {
  const t = useTranslations('userSetting');
  const { user } = useAuthStore();
  const router = useRouter();
  const [newPassword, setNewPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  // 添加状态控制弹窗
  const [open, setOpen] = useState(false);

  const handleUpdatePassword = async () => {
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('password', newPassword);
      await updatePassword(formData);
      toast.success(t('update_password_success'));
      setNewPassword('');
      setOpen(false); // 成功后关闭弹窗
    } catch {
      toast.error(t('update_password_failed'));
    } finally {
      setIsLoading(false);
    }
  };

  // 修改弹窗组件
  // <Dialog open={open} onOpenChange={setOpen}>
  //   <DialogTrigger asChild>
  //     <Button variant="outline" size="sm">
  //       {t('edit')}
  //     </Button>
  //   </DialogTrigger>
  //   <DialogContent className="w-[400px]">
  //     <DialogHeader>
  //       <DialogTitle>{t('change_password')}</DialogTitle>
  //     </DialogHeader>
  //     <div className="space-y-4 pb-4">
  //       <div className="space-y-2">
  //         <Input
  //           type="password"
  //           placeholder={t('enter_new_password')}
  //           value={newPassword}
  //           onChange={(e) => setNewPassword(e.target.value)}
  //         />
  //       </div>
  //       <Button
  //         className="w-full bg-primary hover:bg-primary/90"
  //         onClick={handleUpdatePassword}
  //         disabled={!newPassword || isLoading}
  //       >
  //         {isLoading ? t('updating') : t('confirm')}
  //       </Button>
  //     </div>
  //   </DialogContent>
  // </Dialog>;
  const deleteDataMutation = trpc.user.deleteData.useMutation({
    onSuccess: () => {
      toast.success(t('delete_account_success'));
      router.push('/');
    },
  });
  return (
    <div className="bg-white rounded-lg shadow h-full">
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-lg font-medium">{t('profile')}</h1>
      </div>
      <div className="bg-[#F9FAFE] h-full">
        <div className="p-6  h-full max-w-[900px] mx-auto">
          <Card className="shadow-none border-0 bg-[#F9FAFE]">
            <CardContent className="p-0">
              <div className="flex justify-between items-center py-4">
                <div>
                  <p className="text-sm font-medium mb-1">{t('email')}</p>
                  <p className="text-sm text-muted-foreground">{user?.email}</p>
                </div>
                {/* <Button variant="outline" size="sm">
                {t('edit')}
              </Button> */}
              </div>

              <div className="flex justify-between items-center py-4">
                <div>
                  <p className="text-sm font-medium mb-1">{t('password')}</p>
                  <p className="text-sm text-muted-foreground">
                    {t('If_you_want_to_log_in_with_email')}
                  </p>
                </div>
                <Dialog open={open} onOpenChange={setOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setOpen(true)}
                    >
                      {t('edit')}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[400px]">
                    <DialogHeader>
                      <DialogTitle>{t('change_password')}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 pb-4">
                      <div className="space-y-2">
                        <Input
                          type="password"
                          placeholder={t('enter_new_password')}
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                        />
                      </div>
                      <Button
                        className="w-full bg-primary hover:bg-primary/90"
                        onClick={handleUpdatePassword}
                        disabled={!newPassword || isLoading}
                      >
                        {isLoading ? t('updating') : t('confirm')}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              <div className="flex justify-between items-center py-4">
                <div>
                  <p className="text-sm font-medium mb-1">
                    {t('account_management')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {t('delete_account')}
                  </p>
                </div>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-destructive border-destructive hover:bg-destructive/10"
                    >
                      {t('delete_account')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{t('delete_account')}</AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('delete_account_confirm_description')}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                      <AlertDialogAction
                        className="bg-destructive hover:bg-destructive/90"
                        onClick={() => {
                          deleteDataMutation.mutate();
                        }}
                      >
                        {t('confirm_delete')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
