'use client';
import Image from 'next/image';
import ChatList from '@/components/chat-list';
import { useAuthStore } from '@/hooks/store/auth';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { FolderKanban, LayoutDashboard, UserRoundCog } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { trpc } from '@/trpc/client';
import { LoginUser } from '@/modules/contents/web.interface';
import { useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('Dashboard');
  const { hasPlatform } = useAuthStore();

  const { setUser, setHasPlatform, setIsLoginStatus } = useAuthStore();
  const { data: userInfo } = trpc.user.currentUserInfo.useQuery(undefined, {
    retry: 1,
    refetchOnMount: true,
  });
  useEffect(() => {
    if (userInfo?.user) {
      setUser(userInfo.user as unknown as LoginUser);
      setHasPlatform((userInfo.platforms?.length as number) > 0);
      setIsLoginStatus(true);
      if ((userInfo.platforms?.length as number) == 0) {
        // router.push('/dashboard/social-channel');
      }
    }
  }, [userInfo]);

  return (
    <TooltipProvider>
      <div className="flex h-screen w-full bg-BgMainColor p-4 gap-4">
        {/* Sidebar */}
        <div className="w-[240px] flex flex-col ">
          <div
            className="pb-4 flex items-center gap-2 w-full h-[70px]"
            onClick={() => {
              if (hasPlatform) {
                router.push('/dashboard');
              } else {
                router.push('/dashboard/social-channel');
              }
            }}
          >
            {/* <div className="w-8 h-8 rounded-full bg-white"></div>
            <span className="font-bold text-gray-700">LOGO</span> */}
            <Image
              src="/icon-font.webp"
              alt="logo"
              width={200}
              height={36}
              className="h-[36px] w-[170px] cursor-pointer"
            />
          </div>

          <div className="pb-4">
            <button
              onClick={() => {
                router.push('/dashboard/chat');
              }}
              className="w-full bg-primary text-white rounded-md py-2 px-4 flex items-center justify-center gap-1 hover:bg-primary/80"
            >
              <span>+</span> New chat
            </button>
          </div>

          <nav className="flex-1 mb-2 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
            <div className="">
              <div
                className={cn(
                  pathname === '/dashboard' && 'text-primary',
                  'flex mb-2 items-center gap-2 p-2 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary',
                )}
                onClick={() => {
                  if (hasPlatform) {
                    router.push('/dashboard');
                  } else {
                    router.push('/dashboard/social-channel');
                  }
                }}
              >
                <LayoutDashboard className="size-5" />
                {t('dashboard')}
              </div>
              <div
                className={cn(
                  pathname === '/dashboard/comments' && 'text-primary',
                  'flex mb-2 items-center gap-2 p-2 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary',
                )}
                onClick={() => {
                  if (hasPlatform) {
                    router.push('/dashboard/comments');
                  } else {
                    router.push('/dashboard/social-channel');
                  }
                }}
              >
                <FolderKanban className="size-5" />
                {t('comments')}
              </div>
              <div
                className={cn(
                  pathname === '/dashboard/bind-account' && 'text-primary',
                  'flex mb-2 items-center gap-2 p-2 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary',
                )}
                onClick={() => {
                  if (hasPlatform) {
                    router.push('/dashboard/bind-account');
                  } else {
                    router.push('/dashboard/social-channel');
                  }
                }}
              >
                <UserRoundCog className="size-5" />
                {t('adapters')}
              </div>
            </div>

            <Separator className="my-2 h-[1px]" />

            <div className="text-gray-500 flex items-center gap-2 px-2">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="icon/chat" clipPath="url(#clip0_4831_2027)">
                  <path
                    id="vector"
                    d="M13.8089 5.41858L13.6445 5.7957C13.5243 6.07178 13.1424 6.07178 13.0221 5.7957L12.8577 5.41858C12.5647 4.74615 12.037 4.21078 11.3785 3.91789L10.872 3.69265C10.5982 3.57086 10.5982 3.17238 10.872 3.05058L11.3501 2.83793C12.0256 2.53751 12.5628 1.98232 12.8507 1.28706L13.0195 0.879524C13.1372 0.595497 13.5295 0.595497 13.6471 0.879524L13.8159 1.28706C14.1039 1.98232 14.6411 2.53751 15.3165 2.83793L15.7946 3.05058C16.0685 3.17238 16.0685 3.57086 15.7946 3.69265L15.2882 3.91789C14.6297 4.21078 14.1019 4.74615 13.8089 5.41858ZM6.66667 1.99984H9.33333V3.33317H6.66667C4.45753 3.33317 2.66667 5.12403 2.66667 7.33317C2.66667 9.73984 4.30805 11.3102 8 12.9864V11.3332H9.33333C11.5425 11.3332 13.3333 9.5423 13.3333 7.33317H14.6667C14.6667 10.2787 12.2789 12.6665 9.33333 12.6665V14.9998C6 13.6665 1.33333 11.6665 1.33333 7.33317C1.33333 4.38765 3.72115 1.99984 6.66667 1.99984Z"
                    fill="#1D2129"
                    fillOpacity="0.5"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_4831_2027">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <span>{t('history')}</span>
            </div>

            <div className="p-3 space-y-1">
              <ChatList />
            </div>
          </nav>

          <LoginSection />
        </div>

        <div className="flex-1 overflow-auto rounded-lg h-full [&::-webkit-scrollbar]:hidden ">
          {children}
        </div>
      </div>
    </TooltipProvider>
  );
}

const LoginSection = () => {
  const { user, signOut } = useAuthStore();
  const supabase = createClient();
  const router = useRouter();
  const t = useTranslations('Dashboard');
  return (
    <div className="flex flex-col space-y-4 mb-4 ">
      {/* Upgrade Plan Button */}
      <div
        style={{
          backgroundImage: 'url(/images/dashboard/line-bg.webp)',
          backgroundSize: '100% 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
        className="w-full flex items-center relative text-gray-800 rounded-lg py-4 px-4 cursor-pointer"
        onClick={() => {
          router.push('/dashboard/setting-pricing');
        }}
      >
        <span className="font-bold text-sm">{t('upgradePlan')}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="bg-white rounded-full ml-2"
        >
          <path d="M9 18l6-6-6-6" />
        </svg>
        <Image
          src="/images/dashboard/plane.webp"
          alt="icon"
          width={92.8}
          height={76.8}
          loading="lazy"
          className="absolute right-0 top-[-20px]"
        />
      </div>

      {/* Usage Stats */}
      <div className="space-y-2 bg-[#D3D3F6] p-4 rounded-md">
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-gray-600">{t('usage')}</div>
          <div className="flex gap-2 items-center">
            <Image
              src="/images/dashboard/yellow-stack.webp"
              alt="icon"
              width={18}
              height={18}
              loading="lazy"
            />
            <span className="text-sm text-gray-600">
              {user?.remainAmount || 0}/{user?.amount || 0}
            </span>
            <Tooltip>
              <TooltipTrigger asChild>
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    id="&#229;&#184;&#174;&#229;&#138;&#169; (4) 1"
                    clipPath="url(#clip0_4885_3417)"
                  >
                    <path
                      id="Vector"
                      d="M7.028 0.742188C7.896 0.742188 8.71034 0.907854 9.471 1.23919C10.2317 1.57052 10.8967 2.01852 11.466 2.58319C12.0353 3.14785 12.4833 3.81052 12.81 4.57119C13.1367 5.33185 13.3 6.14619 13.3 7.01419C13.3 7.88219 13.1367 8.69419 12.81 9.45019C12.4833 10.2062 12.0353 10.8689 11.466 11.4382C10.8967 12.0075 10.2317 12.4555 9.471 12.7822C8.71034 13.1089 7.896 13.2722 7.028 13.2722C6.16 13.2722 5.348 13.1089 4.592 12.7822C3.836 12.4555 3.17334 12.0075 2.604 11.4382C2.03467 10.8689 1.58667 10.2062 1.26 9.45019C0.933338 8.69419 0.770004 7.88219 0.770004 7.01419C0.770004 6.14619 0.933338 5.33185 1.26 4.57119C1.58667 3.81052 2.03467 3.14785 2.604 2.58319C3.17334 2.01852 3.836 1.57052 4.592 1.23919C5.348 0.907854 6.16 0.742187 7.028 0.742188ZM7.042 11.2982C7.28467 11.2982 7.48767 11.2165 7.651 11.0532C7.81434 10.8899 7.896 10.6915 7.896 10.4582C7.896 10.2155 7.81434 10.0125 7.651 9.84919C7.48767 9.68585 7.28467 9.60419 7.042 9.60419C6.79934 9.60419 6.59634 9.68585 6.433 9.84919C6.26967 10.0125 6.188 10.2155 6.188 10.4582C6.188 10.6915 6.26967 10.8899 6.433 11.0532C6.59634 11.2165 6.79934 11.2982 7.042 11.2982ZM7.756 7.85419C7.74667 7.70485 7.84 7.54619 8.036 7.37819C8.232 7.21019 8.45134 7.02352 8.694 6.81819C8.93667 6.61285 9.16067 6.38185 9.366 6.12519C9.57134 5.86852 9.68334 5.57219 9.702 5.23619C9.72067 4.87219 9.68334 4.53152 9.59 4.21419C9.49667 3.89685 9.34267 3.62385 9.128 3.39519C8.91334 3.16652 8.63567 2.98452 8.295 2.84919C7.95434 2.71385 7.55534 2.64619 7.098 2.64619C6.52867 2.64619 6.055 2.74652 5.677 2.94719C5.299 3.14785 4.99334 3.39052 4.76 3.67519C4.52667 3.95985 4.36334 4.25385 4.27 4.55719C4.17667 4.86052 4.13467 5.11485 4.144 5.32019C4.15334 5.56285 4.23034 5.74019 4.375 5.85219C4.51967 5.96419 4.676 6.02252 4.844 6.02719C5.012 6.03185 5.166 5.98519 5.306 5.88719C5.446 5.78919 5.516 5.64685 5.516 5.46019C5.516 5.34819 5.551 5.21052 5.621 5.04719C5.691 4.88385 5.78667 4.72752 5.908 4.57819C6.02934 4.42885 6.17634 4.30285 6.349 4.20019C6.52167 4.09752 6.71534 4.04619 6.93 4.04619C7.35 4.04619 7.686 4.15119 7.938 4.36119C8.19 4.57119 8.30667 4.83485 8.288 5.15219C8.288 5.31085 8.24134 5.45785 8.148 5.59319C8.05467 5.72852 7.93567 5.85919 7.791 5.98519C7.64634 6.11119 7.49234 6.23719 7.329 6.36319C7.16567 6.48919 7.01167 6.61985 6.867 6.75519C6.72234 6.89052 6.601 7.03752 6.503 7.19619C6.405 7.35485 6.35134 7.52752 6.342 7.71419L6.356 8.24619C6.356 8.38619 6.42134 8.51919 6.552 8.64519C6.68267 8.77119 6.85534 8.83886 7.07 8.84819C7.28467 8.83886 7.455 8.76886 7.581 8.63819C7.707 8.50752 7.76534 8.35819 7.756 8.19019V7.85419Z"
                      fill="#6246EA"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_4885_3417">
                      <rect width="14" height="14" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">{t('usageTooltip')}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        <div className="w-full rounded-full bg-white h-2">
          <div
            className="bg-purple-600 h-2 rounded-full"
            style={{
              width: `${((user?.remainAmount as number) / (user?.amount as number)) * 100}%`,
            }}
          />
        </div>
      </div>

      {/* User Info */}
      <div className="flex items-center justify-between bg-[#ECECFF] p-2 rounded-md">
        <div className="flex items-center space-x-2">
          {user?.avatar ? (
            <Image
              src={user.avatar || ''}
              alt="User avatar"
              className="w-8 h-8 rounded-full"
              width={32}
              height={32}
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
              <span className="text-white text-lg ">
                {user?.email.slice(0, 1).toLocaleUpperCase()}
              </span>
            </div>
          )}
          <span className="text-sm text-gray-700 w-[150px] truncate">
            {user?.email || ''}
          </span>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="text-gray-400 hover:text-gray-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="1" />
                <circle cx="19" cy="12" r="1" />
                <circle cx="5" cy="12" r="1" />
              </svg>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[180px]">
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => router.push('/dashboard/setting-pricing')}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
              {t('upgradeCoupons')}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => {
                router.push('/dashboard/setting');
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
              {t('personalSettings')}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => {
                router.push('/blog');
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20" />
              </svg>
              {t('blog')}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => {
                router.push('/faq');
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                <path d="M12 17h.01" />
              </svg>
              {t('helpCenterFAQ')}
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
              {t('introductionPage')}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="cursor-pointer text-red-600"
              onClick={async () => {
                await supabase.auth.signOut();
                signOut();
                router.push('/home?logout=true');
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                <polyline points="16 17 21 12 16 7" />
                <line x1="21" y1="12" x2="9" y2="12" />
              </svg>
              {t('logOut')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default DashboardLayout;
