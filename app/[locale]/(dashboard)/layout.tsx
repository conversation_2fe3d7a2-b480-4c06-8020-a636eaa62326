import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import DashboardLayout from './layout-client';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Dashboard');

  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
  };
}

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session) {
    redirect('/home?logout=true');
  }
  return (
    <div>
      <DashboardLayout>{children}</DashboardLayout>
    </div>
  );
}
