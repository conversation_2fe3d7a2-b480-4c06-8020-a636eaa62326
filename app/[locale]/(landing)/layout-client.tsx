'use client';
import { useRouter, usePathname } from 'next/navigation';

import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/hooks/store/auth';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import {
  BookOpen,
  CreditCard,
  FileQuestion,
  FolderKanban,
  LayoutDashboard,
} from 'lucide-react';
import { LoginUser } from '@/modules/contents/web.interface';
import { trpc } from '@/trpc/client';
import dynamic from 'next/dynamic';

const CookieContent = dynamic(() => import('@/components/biz/CookieContent'), {
  ssr: false,
});

export default function Index({
  children,
  session,
}: {
  children: React.ReactNode;
  session: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('Dashboard');
  const {
    isLoginStatus,
    setIsLoginMode,
    setUser,
    setHasPlatform,
    setIsLoginStatus,
  } = useAuthStore();

  const { data: userInfo } = trpc.user.currentUserInfo.useQuery(undefined, {
    retry: 1,
    staleTime: 0,
  });

  useEffect(() => {
    if (userInfo?.user) {
      setUser(userInfo.user as unknown as LoginUser);
      setHasPlatform((userInfo.platforms?.length as number) > 0);
      setIsLoginStatus(true);
    }
  }, [userInfo]);
  return (
    <div className="flex h-screen w-full bg-BgMainColor p-4 gap-4">
      <div className="w-[240px] flex flex-col ">
        <div
          className="pb-4 flex items-center gap-2 w-full h-[70px]"
          onClick={() => {
            router.push('/');
          }}
        >
          <Image
            src="/icon-font.webp"
            alt="logo"
            width={200}
            height={36}
            className="h-[36px] w-[170px] cursor-pointer"
          />
        </div>

        <div className="pb-4">
          <button
            className="w-full bg-indigo-600 text-white rounded-md py-2 px-4 flex items-center justify-center gap-1"
            onClick={() => {
              if (isLoginStatus) {
                router.push('/dashboard/chat');
              } else {
                setIsLoginMode(true);
              }
            }}
          >
            <span>+</span> New chat
          </button>
        </div>

        <nav className="flex-1  overflow-y-auto">
          <div className="">
            <div
              className={cn(
                pathname === '/dashboard' && 'text-primary',
                'flex mb-2 items-center gap-2 p-2 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary',
              )}
              onClick={() => {
                if (isLoginStatus) {
                  router.push('/dashboard');
                } else {
                  setIsLoginMode(true);
                }
              }}
            >
              <LayoutDashboard className="size-5" />
              {t('dashboard')}
            </div>
            <div
              className={cn(
                pathname === '/dashboard/comments' && 'text-primary',
                'flex mb-2 items-center gap-2 p-2 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary',
              )}
              onClick={() => {
                if (isLoginStatus) {
                  router.push('/dashboard/comments');
                } else {
                  setIsLoginMode(true);
                }
              }}
            >
              <FolderKanban className="size-5" />
              {t('comments')}
            </div>
          </div>

          <div className="px-3 py-2 text-sm text-gray-500">
            {t('resources')}
          </div>

          <div className="p-3 space-y-1">
            <div className="flex items-center gap-2 p-2 text-gray-600 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" />
              </svg>
              {t('product_introduction')}
            </div>
            <div className="flex items-center gap-2 p-2 text-gray-600 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" />
              </svg>
              {t('product_highlights')}
            </div>
            <div
              className="flex items-center gap-2 p-2 text-gray-600 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary"
              onClick={() => router.push('/blog')}
            >
              <BookOpen className="size-5" />
              Blog
            </div>
            <div
              className="flex items-center gap-2 p-2 text-gray-600 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary"
              onClick={() => router.push('/faq')}
            >
              <FileQuestion className="size-5" />
              FAQ
            </div>
            <div
              className="flex items-center gap-2 p-2 text-gray-600 cursor-pointer rounded-md hover:bg-[#D3D3F6] hover:text-primary"
              onClick={() => router.push('/pricing')}
            >
              <CreditCard className="size-5" />
              {t('pricing')}
            </div>
          </div>
        </nav>

        {!session ? <LoginSection /> : null}
      </div>

      <div className="flex-1 p-6 bg-white rounded-lg relative">{children}</div>
      <CookieContent />
    </div>
  );
}

const LoginSection = () => {
  const { setIsLoginMode } = useAuthStore();
  const t = useTranslations('Dashboard');

  return (
    <div className="mt-auto bg-white flex flex-col items-center text-center p-4 rounded-lg cursor-pointer">
      <div className="flex flex-col items-center text-center mb-4">
        <div className="w-16 h-16  flex items-center justify-center rounded-md mb-2">
          <Image
            src="/images/dashboard/un-login.webp"
            alt="login"
            width={64}
            height={64}
          />
        </div>
        <p className="text-base text-gray-500">{t('login_description')}</p>
      </div>
      <button
        className="w-full bg-primary text-white rounded-md py-2 px-4 hover:bg-primary/80 transition-colors"
        onClick={() => setIsLoginMode(true)}
      >
        {t('sign_up_or_log_in')}
      </button>
    </div>
  );
};
