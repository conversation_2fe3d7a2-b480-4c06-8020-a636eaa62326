import { env } from '@/env';
import HomePage from './page-client';
import { page2JsonLd } from '@/lib/jsonLd';
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import Script from 'next/script';
let jsonLd: ReturnType<typeof page2JsonLd> | string = '';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Dashboard');
  jsonLd = page2JsonLd(
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}/home`,
      desc: t('metadata.description'),
    },
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      desc: t('metadata.description'),
    },
  );
  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    metadataBase: new URL(env.NEXT_PUBLIC_SITE_URL),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}`,
    },
    openGraph: {
      title: t('metadata.title'),
      description: t('metadata.description'),
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      images: [
        {
          url: `/openGraphImgs/index.webp`,
          width: 1200,
          height: 630,
          alt: t('metadata.title'),
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.title'),
      site: `@commentifyai`,
      description: t('metadata.description'),
      images: [
        {
          url: `/openGraphImgs/index.webp`,
        },
      ],
    },
  };
}
export default function Home() {
  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <HomePage />
    </>
  );
}
