'use client';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import { useTranslations } from 'next-intl';
import { useAuthStore } from '@/hooks/store/auth';
import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { toast } from 'sonner';

const ChatInput = dynamic(() => import('@/components/chatInput'), {
  ssr: false,
});

export default function Index() {
  const router = useRouter();
  const t = useTranslations('Index');
  const { isLoginStatus, setIsLoginMode } = useAuthStore();
  const hasShownToast = useRef(false);
  useEffect(() => {
    if (hasShownToast.current) return;
    if (window.location.search.includes('logout=true')) {
      toast.error(t('logout_success'));
      hasShownToast.current = true;
    }
  }, [t]);

  const handleGetStarted = () => {
    if (isLoginStatus) {
      router.push('/dashboard');
    } else {
      setIsLoginMode(true);
    }
  };
  const Features = {
    h2: t('more_features'),
    list: [
      {
        title: t('instructions_ai'),
        desc: t('instructions_ai_desc'),
        image: '/images/index/n-1.webp',
      },
      {
        title: t('translation'),
        desc: t('translation_desc'),
        image: '/images/index/n-2.webp',
      },
      {
        title: t('filters'),
        desc: t('filters_desc'),
        image: '/images/index/n-3.webp',
      },
      {
        title: t('knowledge_ai'),
        desc: t('knowledge_ai_desc'),
        image: '/images/index/n-4.webp',
      },
      {
        title: t('post_viewer'),
        desc: t('post_viewer_desc'),
        image: '/images/index/n-5.webp',
      },
      {
        title: t('private_replies'),
        desc: t('private_replies_desc'),
        image: '/images/index/n-6.webp',
      },
      {
        title: t('block_users_mentions'),
        desc: t('block_users_mentions_desc'),
        image: '/images/index/n-7.webp',
      },
      {
        title: t('self_learning_algorithms'),
        desc: t('self_learning_algorithms_desc'),
        image: '/images/index/n-8.webp',
      },
    ],
  };
  const moderationFeatures = {
    h2: t('why_choose_our_ai_social_media_moderator'),
    list: [
      {
        title: t('real_time_moderation'),
        desc: t('real_time_moderation_desc'),
      },
      {
        title: t('customizable_settings'),
        desc: t('customizable_settings_desc'),
      },
      {
        title: t('smart_custom_analytics'),
        desc: t('smart_custom_analytics_desc'),
      },
      {
        title: t('seamless_integration'),
        desc: t('seamless_integration_desc'),
      },
    ],
  };
  return (
    <div className="max-w-6xl mx-auto pr-10 h-full overflow-auto [&::-webkit-scrollbar]:hidden">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2 mt-10">
            {t('ai_social_media_moderator')}
          </h1>
          <p className="text-gray-600">
            {t(
              'ai_moderation_tool_detects_and_hides_hate_speech_scams_and_spam_stop_toxic_comments_instantly',
            )}
          </p>
        </div>
      </div>
      <div className="flex gap-4">
        <div className="w-1/4" onClick={handleGetStarted}>
          <div className="w-full h-full rounded-tl-[50px] rounded-br-[50px] rounded-bl-xl rounded-tr-xl overflow-hidden p-7 bg-[url('/images/index/1.webp')] bg-cover bg-center">
            <div className="h-full flex flex-col">
              <div className="mb-auto">
                <h2 className="text-lg font-semibold mb-2">
                  {t('smart_comment_management')}
                </h2>
                <p className="text-sm text-gray-500">
                  {t(
                    'track_and_manage_comment_across_platforms_like_facebook_and_instagram',
                  )}
                </p>
              </div>
              <div className="mt-4">
                <button className="text-white flex items-center text-sm hover:text-primary transition-all">
                  {t('get_started')} <ArrowRight className="ml-1 h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="w-3/4 flex flex-col gap-4" onClick={handleGetStarted}>
          <div className="h-[250px] rounded-xl bg-[url('/images/index/2.webp')] bg-cover bg-center p-6 relative">
            <div className="flex justify-between">
              <div className="flex-1">
                <h2 className="text-lg font-medium mb-2 text-black">
                  {t('ai_sentiment_analysis')}
                </h2>
                <p className="text-base text-gray-500 mb-8">
                  {t(
                    'automatically_detect_positive_negative_or_neutral_sentiments_in_comments',
                  )}
                </p>
                <div className="flex gap-2">
                  {/* <button className="bg-indigo-600 text-white rounded-md px-10 py-2 text-sm flex items-center gap-1 hover:bg-indigo-700 transition-all">
                    <span>👍</span> {t('try_demo')}
                  </button> */}
                  <button className="bg-white text-gray-800 rounded-md px-10 py-2 text-sm">
                    {t('get_started')}
                  </button>
                </div>
              </div>
              <Image
                src="/images/index/3.webp"
                alt="AI Sentiment Analysis"
                width={300}
                height={300}
                className="object-contain absolute bottom-0 right-[-50px]"
              />
            </div>
          </div>
          <div className="flex gap-4 h-[250px] ">
            <div className="border rounded-xl p-5 flex-1 bg-[#FBFBFB] cursor-pointer">
              <div className="h-full flex flex-col">
                <div className="mb-auto">
                  <h2 className="text-lg font-semibold mb-2 text-gray-800">
                    {t('auto_delete_hide_comments')}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {t('automatically_remove_or_hide_unwanted_comments')}
                  </p>
                </div>
                <div className="mt-4">
                  <button className="text-gray-700 flex items-center text-sm hover:text-primary transition-all">
                    {t('get_started')} <ArrowRight className="ml-1 h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
            <div className="border rounded-xl p-5 flex-1 bg-[#FBFBFB] cursor-pointer ">
              <div className="h-full flex flex-col">
                <div className="mb-auto">
                  <h2 className="text-lg font-semibold mb-2 text-gray-800">
                    {t('bulk_actions')}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {t(
                      'hide_unhide_like_or_delete_comments_in_bulk_with_one_click',
                    )}
                  </p>
                </div>
                <div className="mt-4">
                  <button className="text-gray-700 flex items-center text-sm hover:text-primary transition-all">
                    {t('get_started')} <ArrowRight className="ml-1 h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
            <div className="border rounded-xl p-5 flex-1 bg-[#FBFBFB] cursor-pointer">
              <div className="h-full flex flex-col">
                <div className="mb-auto">
                  <h2 className="text-lg font-semibold mb-2 text-gray-800">
                    {t('ai_data_report_generator')}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {t(
                      'upload_your_files_click_and_your_insightful_visuals_will_be_ready_in_minutes',
                    )}
                  </p>
                </div>
                <div className="mt-4">
                  <button className="text-gray-700 flex items-center text-sm hover:text-primary transition-all">
                    {t('get_started')} <ArrowRight className="ml-1 h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <h2 className="text-2xl font-bold mb-4 mt-8">{Features.h2}</h2>
      <ul className="grid grid-cols-4 gap-4" onClick={handleGetStarted}>
        {Features.list.map((feature) => (
          <li
            key={feature.title}
            className="border rounded-xl p-4 bg-[#FBFBFB] h-[280px] cursor-pointer relative"
          >
            <h3 className="text-lg font-semibold mb-2 text-gray-800">
              {feature.title}
            </h3>
            <p className="text-sm text-gray-600">{feature.desc}</p>
            {feature.image && (
              <div className="flex justify-center items-center h-fit absolute bottom-[0px] left-0 right-0">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  width={240}
                  height={240}
                  className="object-contain"
                />
              </div>
            )}
          </li>
        ))}
      </ul>

      <h2 className="text-2xl font-bold mb-4 mt-8">{moderationFeatures.h2}</h2>
      <ul
        className="grid grid-cols-2 gap-4 mb-[250px]"
        onClick={handleGetStarted}
      >
        {moderationFeatures.list.map((feature) => (
          <li
            key={feature.title}
            className="border rounded-xl p-4 bg-[#FBFBFB] cursor-pointer"
          >
            <h3 className="text-lg font-semibold mb-2 text-gray-800">
              {feature.title}
            </h3>
            <p className="text-sm text-gray-600">{feature.desc}</p>
          </li>
        ))}
      </ul>
      <div className="absolute bottom-10 left-1/2 -translate-x-1/2 right-0  w-[70%]">
        <ChatInput
          isLoginStatus={isLoginStatus}
          openModal={() => setIsLoginMode(true)}
        />
      </div>
    </div>
  );
}
