'use client';

import { useLocale } from 'next-intl';
import { Link, routing, usePathname } from '@/i18n/routing';
import { localeNames } from '@/i18n/locale';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Image from 'next/image';

export default function LanguageSwitcher() {
  const locale = useLocale();
  const pathname = usePathname();
  const currentLocaleName = localeNames[locale as keyof typeof localeNames];

  return (
    <div className="flex items-center gap-1">
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center gap-2 text-sm px-3 py-2 hover:bg-accent/50 rounded-md transition-colors">
          <Image
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
            className="w-4 h-4 opacity-70"
          />
          <span>{currentLocaleName}</span>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="opacity-70"
          >
            <path
              d="M2.5 4.5L6 8L9.5 4.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[8rem]">
          {routing.locales.map((l) => (
            <DropdownMenuItem key={l} asChild>
              <Link
                href={pathname}
                locale={l}
                className={`w-full px-3 py-2 text-sm ${locale === l ? 'bg-accent/50 font-medium' : 'hover:bg-accent/30'}`}
              >
                {localeNames[l as keyof typeof localeNames]}
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
