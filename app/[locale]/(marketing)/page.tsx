import PageClient from './page-client';
import { env } from '@/env';
import { I18nProps } from '@/i18n/locale';
import { Metadata } from 'next';
import {
  getTdkApi,
  getPageContentContentApi,
} from '@/modules/contents/service';
import { redirect } from 'next/navigation';
import Script from 'next/script';
import { homeJsonLd } from '@/lib/jsonLd';

let jsonLd: ReturnType<typeof homeJsonLd> | string = '';

export async function generateMetadata({
  params,
}: {
  params: Promise<I18nProps>;
}): Promise<Metadata> {
  const { locale } = await params;
  const tdk = await getTdkApi('/', locale);
  const baseURL = env.NEXT_PUBLIC_SITE_URL;
  jsonLd = homeJsonLd({
    title: tdk.data?.title,
    url: `${env.NEXT_PUBLIC_SITE_URL}`,
    desc: tdk.data?.description,
  });
  return {
    title: tdk.data?.title,
    description: tdk.data?.description,
    metadataBase: new URL(baseURL),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}`,
    },
    openGraph: {
      title: tdk.data?.title,
      description: tdk.data?.description,
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      images: [
        {
          url: `/openGraphImgs/index.webp`,
          width: 1200,
          height: 630,
          alt: tdk.data?.title,
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: tdk.data?.title,
      site: `@commentifyai`,
      description: tdk.data?.description,
      images: [
        {
          url: `/openGraphImgs/index.webp`,
        },
      ],
    },
  };
}
export default async function Index({
  params,
  searchParams,
}: {
  params: Promise<I18nProps>;
  searchParams: Promise<{ preview?: string; preview_id?: string }>;
}) {
  const { locale } = await params;
  const { preview, preview_id } = await searchParams;
  const pageRes = await getPageContentContentApi(
    '/',
    locale,
    Number(preview) || '',
    Number(preview_id) || '',
  );
  if (pageRes.code != 0) {
    redirect('/404');
  }

  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <PageClient landingData={pageRes.data} />
    </>
  );
}
