'use client';
import { Check } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useTranslations } from 'next-intl';
import { trpc } from '@/trpc/client';
import { cn } from '@/lib/utils';
import { SubscribeButton } from '@/components/SubscribeButton';
import { useAuthStore } from '@/hooks/store/auth';
import { useCallback, useState, useRef } from 'react';
import { Plan } from '@/modules/stripe/plan';
import dynamic from 'next/dynamic';
const UpgradeModal = dynamic(() => import('@/components/UpgradeModal'), {
  ssr: false,
});
export default function PricingPage() {
  const t = useTranslations('Pricing');
  const [data] = trpc.stripe.getProducts.useSuspenseQuery(undefined, {
    retry: 3,
  });

  const { user, isLoginStatus, setIsLoginMode } = useAuthStore();

  const isUpgrade = useCallback(
    (plan: Plan) => {
      const curIndex = data.findIndex(
        (item) => item.name === user?.currentPlan,
      );
      const planIndex = data.findIndex((item) => item.name === plan.name);
      return (curIndex || -1) <= planIndex;
    },
    [user],
  );

  const [open, setOpen] = useState(false);
  const priceIdRef = useRef<string>('');

  return (
    <div className="container mx-auto px-4 py-16 max-w-6xl">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
        {data.map((item) => (
          <div
            key={item.id}
            className={`border-2 rounded-lg overflow-hidden cursor-pointer ${
              user?.currentPlan === item.name
                ? 'border-primary'
                : 'border-gray-200'
            } px-4  hover:border-primary`}
          >
            <div className="pt-4">
              <h2 className="font-medium mb-1">{t(item.f_name)}</h2>
              <div className="flex items-baseline mb-4">
                <span className="text-3xl font-bold">${item.f_value}</span>
                <span className="text-gray-500 ml-1">/{t('month')}</span>
              </div>
              <div
                className={cn('text-sm text-gray-500 mb-4', {
                  'opacity-0': item.id === 'free',
                })}
              >
                {t('billedMonthly')}
              </div>
              <SubscribeButton
                priceId={item.priceId}
                isActive={user?.currentPlan === item.name}
                isLoginStatus={isLoginStatus}
                setIsLoginMode={setIsLoginMode}
                isUpgrade={isUpgrade(item)}
                user={user}
                setPriceId={(priceId: string) => {
                  priceIdRef.current = priceId;
                }}
                openModal={() => setOpen(true)}
              >
                {!isLoginStatus ? (
                  <span>{t('getStarted')}</span>
                ) : user?.currentPlan === item.name ? (
                  <span>{t('currentPlan')}</span>
                ) : isUpgrade(item) ? (
                  <span>{t('upgrade')}</span>
                ) : (
                  <span>{t('getStarted')}</span>
                )}
              </SubscribeButton>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>
                    {' '}
                    <span className="text-primary">{item.credits}</span>{' '}
                    {t('creditMonth')}
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('unlimitedPages')}</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('unlimitedSentimentAnalysis')}</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('automaticallyHideNegativeCommentsAndSpam')}</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('aiPoweredDataAnalysisJobs')}</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('commentManagement')}</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{t('customizableModerationSettings')}</span>
                </li>
              </ul>
            </div>
            <div className="flex justify-between mb-2">
              <div className="text-sm text-gray-500">{t('service')}</div>
              <div className="text-sm text-gray-500">{t('creditCost')}</div>
            </div>

            <div className="border-t-2 border-[#E7E7E7]">
              <div className="flex justify-between py-4 gap-4">
                <div className="text-sm text-left">
                  {t('aiDataAnalysisTask')}
                </div>
                <div className="text-sm text-right">
                  <span className="text-primary">100</span> {t('creditEach')}
                </div>
              </div>
            </div>

            <div className="border-t-2 border-[#E7E7E7]">
              <div className="flex justify-between py-4 gap-4">
                <div className="text-sm text-left">
                  {t('commentProcessing')}
                </div>
                <div className="text-sm text-right">
                  1 {t('creditPer')} <span className="text-primary">1</span>{' '}
                  <span>{t('comment')}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* FAQ Section */}
      <div className="max-w-3xl mx-auto">
        <h2 className="text-2xl font-bold text-center mb-8">
          FREQUENTLY ASKED QUESTIONS
        </h2>

        <Accordion type="single" collapsible className="space-y-4">
          <AccordionItem value="item-1" className="overflow-hidden">
            <AccordionTrigger className="px-4 py-6 hover:no-underline">
              How many pages and users can I add?
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-8">
              Unlimited pages and users, our pricing is only based on your
              comment volume.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2" className="overflow-hidden !mt-0">
            <AccordionTrigger className="px-4 py-6 hover:no-underline">
              What happens if I exceed my comments/mo?
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-8">
              We&apos;ll warn you when you&apos;re at 90% so you can upgrade to
              a higher plan.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3" className="overflow-hidden !mt-0">
            <AccordionTrigger className="px-4 py-6 hover:no-underline">
              How does the free trial work with these plans?
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-8">
              All plans include a full-feature 7-day trial - no credit card
              needed. When your trial ends, we&apos;ll automatically apply the
              plan you&apos;re viewing today.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-4" className="overflow-hidden !mt-0">
            <AccordionTrigger className="px-4 py-6 hover:no-underline">
              What counts as a &apos;comment&apos; in my monthly limit?
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-8">
              Every comment processed by our AI (whether hidden, approved, or
              flagged) counts toward your limit.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            value="item-5"
            className="overflow-hidden border-none !mt-0"
          >
            <AccordionTrigger className="px-4 py-6 hover:no-underline">
              Does Commentify analyze existing comment history?
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-8">
              Commentify only moderates new comments received after connecting
              your page. We don&apos;t access or process historical comments
              from before setup.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      {/* 升级确认弹窗 */}
      <UpgradeModal
        open={open}
        onOpenChange={setOpen}
        priceId={priceIdRef.current}
      />
    </div>
  );
}
