import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import PricingPage from './page-client';
import { pricingJsonLd } from '@/lib/jsonLd';
import Script from 'next/script';
import { env } from '@/env';
import { plans } from '@/modules/stripe/plan';

let jsonLd: ReturnType<typeof pricingJsonLd> | string = '';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Pricing');
  jsonLd = pricingJsonLd(
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}/pricing`,
      desc: t('metadata.description'),
    },
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      desc: t('metadata.description'),
    },
    plans.map((plan) => ({
      name: plan.name,
      price: plan.f_value.toString(),
      currency: plan.currency,
    })),
  );
  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    metadataBase: new URL(env.NEXT_PUBLIC_SITE_URL),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}/pricing`,
    },
    openGraph: {
      title: t('metadata.title'),
      description: t('metadata.description'),
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}/pricing`,
      images: [
        {
          url: `/openGraphImgs/pricing.webp`,
          width: 1200,
          height: 630,
          alt: t('metadata.title'),
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.title'),
      site: `@commentifyai`,
      description: t('metadata.description'),
      images: [
        {
          url: `/openGraphImgs/pricing.webp`,
        },
      ],
    },
  };
}

export default function Layout() {
  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <PricingPage />
    </>
  );
}
