'use client';
import { ChevronUp, ChevronDown, ArrowR<PERSON> } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { LandingResponse } from '@/modules/contents/web.interface';
import LandingBlog from '@/components/LandingBlog';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/hooks/store/auth';

export default function HideCommentsPage({
  landingData,
}: {
  landingData: LandingResponse;
}) {
  const [open, setOpen] = useState<number | null>(null);
  const router = useRouter();
  const { isLoginStatus, setIsLoginMode } = useAuthStore();
  const handleClick = () => {
    if (isLoginStatus) {
      router.push('/home');
    } else {
      setIsLoginMode(true);
    }
  };
  return (
    <div className="max-w-[1200px] mx-auto">
      {/* 顶部内容 */}
      <div className="max-w-[700px] mx-auto pt-16 text-center">
        <h1 className="hidden">{landingData.contentInfo['hero'].title}</h1>
        <div className="text-4xl md:text-5xl font-bold mb-4 w-[650px]">
          <span className="text-primary">Hide Comment</span> Tool For Facebook &
          Instagram
        </div>
        <p className="text-lg text-gray-600 mb-6">
          {landingData.contentInfo['hero'].desc}
        </p>
        <button
          className="bg-primary text-white px-12 py-3 rounded-lg font-semibold shadow-md hover:bg-primary/90 transition mb-4"
          onClick={handleClick}
        >
          {landingData.contentInfo['hero'].buttonText}
        </button>
        <div className="text-xs text-gray-400 mb-6">
          {landingData.contentInfo['hero'].extraInfo}
        </div>
      </div>
      <div className="flex justify-center my-8">
        <div className="flex items-center px-6 py-2 rounded-full bg-green-100">
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="&#229;&#183;&#178;&#232;&#174;&#164;&#232;&#175;&#129; 1">
              <path
                id="Vector"
                d="M9.32419 19.3048C9.76229 19.4553 10.2381 19.4553 10.6762 19.3048C13.6602 18.2814 14.7382 17.4454 14.7382 17.4454C17.8402 15.3301 17.8675 12.5528 17.8675 12.5528V3.78743C17.8675 3.35743 17.5175 2.9881 17.0875 2.97276C14.8875 2.89476 11.6429 1.27743 10.4555 0.644764C10.3152 0.569602 10.1584 0.530273 9.99919 0.530273C9.83997 0.530273 9.68322 0.569602 9.54286 0.644764C8.35619 1.27543 5.11353 2.89476 2.91019 2.97276C2.70093 2.98363 2.50371 3.07402 2.35886 3.22544C2.214 3.37686 2.13245 3.57789 2.13086 3.78743V12.5528C2.13086 12.5528 2.15819 15.3301 5.26019 17.4454C5.26219 17.4454 6.34019 18.2814 9.32419 19.3048Z"
                fill="#F24827"
              />
              <path
                id="Vector_2"
                d="M9.43511 17.7168C9.80116 17.8419 10.1984 17.8419 10.5644 17.7168C13.0564 16.8635 13.9571 16.1641 13.9571 16.1641C16.5464 14.3988 16.5704 12.0781 16.5704 12.0781V4.85547C16.5704 4.49614 16.2771 4.18947 15.9198 4.17547C14.0818 4.11147 11.3731 2.76014 10.3824 2.23281C10.2653 2.17036 10.1345 2.1377 10.0018 2.1377C9.869 2.1377 9.73827 2.17036 9.62111 2.23281C8.63044 2.75947 5.92111 4.11147 4.08377 4.17614C3.72644 4.18747 3.43311 4.49614 3.43311 4.85614V12.0781C3.43311 12.0781 3.45711 14.3981 6.04644 16.1648C6.04244 16.1648 6.94311 16.8635 9.43511 17.7168Z"
                fill="#FF8D4C"
              />
              <path
                id="Vector_3"
                opacity="0.501"
                d="M9.99982 2.13672C9.86915 2.13672 9.73782 2.16805 9.61915 2.23272C8.62849 2.75939 5.91915 4.11139 4.08182 4.17605C3.72449 4.18739 3.43115 4.49605 3.43115 4.85605V12.0781C3.43115 12.0781 3.45515 14.3981 6.04449 16.1647C6.04449 16.1647 6.94515 16.8634 9.43782 17.7167C9.61915 17.7794 9.81049 17.8101 10.0018 17.8101V2.13739H9.99982V2.13672Z"
                fill="#FAC570"
              />
              <path
                id="Vector_4"
                d="M16.5698 11.0059V12.0779C16.5698 12.0779 16.5464 14.3979 13.9564 16.1645C13.9564 16.1645 13.5251 16.4999 12.4331 16.9879L13.3598 18.2265C14.4371 17.6912 15.3944 17.0739 16.1031 16.2912C17.2698 14.9779 17.7458 13.8145 17.8571 12.7265L16.5704 11.0059H16.5698Z"
                fill="#DC3514"
              />
              <path
                id="Vector_5"
                opacity="0.557"
                d="M16.57 12.0779V11.0066L13.654 7.10791L10 10.5079V13.7332L12.4333 16.9866C13.5253 16.4966 13.9573 16.1626 13.9573 16.1626C16.5467 14.3992 16.5707 12.0786 16.5707 12.0786L16.57 12.0779Z"
                fill="#F34E27"
              />
              <path
                id="Vector_6"
                d="M9.51999 10.9554L6.82666 9.48877L9.99999 13.7321V10.5074L9.51999 10.9554Z"
                fill="#F86027"
              />
              <path
                id="Vector_7"
                d="M9.4221 11.8267C9.26757 11.8267 9.11937 11.7653 9.0101 11.6561L6.95343 9.59739C6.89917 9.54337 6.85611 9.47917 6.82673 9.40847C6.79735 9.33777 6.78223 9.26196 6.78223 9.18539C6.78223 9.10883 6.79735 9.03302 6.82673 8.96231C6.85611 8.89161 6.89917 8.82741 6.95343 8.77339C7.00745 8.71913 7.07165 8.67607 7.14235 8.64669C7.21306 8.61731 7.28887 8.60219 7.36543 8.60219C7.442 8.60219 7.51781 8.61731 7.58851 8.64669C7.65921 8.67607 7.72342 8.71913 7.77743 8.77339L9.4221 10.4181L12.7461 7.09406C12.8001 7.0398 12.8643 6.99674 12.935 6.96736C13.0057 6.93798 13.0815 6.92285 13.1581 6.92285C13.2347 6.92285 13.3105 6.93798 13.3812 6.96736C13.4519 6.99674 13.5161 7.0398 13.5701 7.09406C13.6244 7.14807 13.6674 7.21228 13.6968 7.28298C13.7262 7.35368 13.7413 7.42949 13.7413 7.50606C13.7413 7.58262 13.7262 7.65843 13.6968 7.72914C13.6674 7.79984 13.6244 7.86404 13.5701 7.91806L9.8341 11.6541C9.78042 11.7088 9.7163 11.7523 9.64553 11.7818C9.57476 11.8114 9.49879 11.8264 9.4221 11.8261V11.8267Z"
                fill="white"
              />
            </g>
          </svg>
          <span className="text-green-700 font-semibold ml-2">
            Facebook &amp; Instagram Approved
          </span>
        </div>
      </div>

      {/* 产品截图 */}
      <div className="flex justify-center mb-16 mt-12">
        <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-100 bg-white max-w-3xl w-full">
          <Image
            src={landingData.contentInfo['hero'].imgPath}
            alt={landingData.contentInfo['hero'].title}
            width={900}
            height={500}
            className="w-full h-auto"
            priority
            fetchPriority="high"
          />
        </div>
      </div>

      <div className="max-w-4xl mx-auto my-20">
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10 max-w-[500px] mx-auto">
          {landingData.contentInfo['section-branding'].title}
        </h2>
        {/* 卡片区 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* 第一行3个卡片 */}
          {landingData.contentInfo['section-branding'].cards
            .slice(0, 3)
            .map((item, index) => (
              <div
                className="bg-white rounded-xl border shadow p-6 flex flex-col items-center"
                key={index}
              >
                <Image
                  src={item.imgPath}
                  alt={item.title}
                  width={48}
                  height={48}
                  className="mb-4"
                />
                <h3 className="font-bold text-lg mb-1 text-center">
                  {item.title}
                </h3>
                <div className="text-gray-500 text-sm text-center">
                  {item.desc}
                </div>
              </div>
            ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 w-2/3 mx-auto">
          {landingData.contentInfo['section-branding'].cards
            .slice(3, 5)
            .map((item, index) => (
              <div
                className="bg-white rounded-xl border shadow p-6 flex flex-col items-center"
                key={index}
              >
                <Image
                  src={item.imgPath}
                  alt={item.title}
                  width={48}
                  height={48}
                  className="mb-4"
                />
                <h3 className="font-bold text-lg mb-1 text-center">
                  {item.title}
                </h3>
                <div className="text-gray-500 text-sm text-center">
                  {item.desc}
                </div>
              </div>
            ))}
        </div>
        {/* 统计和按钮 */}
        <div className="text-center text-gray-500 mb-6 max-w-[500px] mx-auto">
          {landingData.contentInfo['section-branding'].desc}
        </div>
        <div className="flex justify-center mb-2">
          <button
            className="bg-[#6C47FF] text-white px-8 py-3 rounded-lg font-semibold shadow-md hover:bg-[#5a38d6] transition flex items-center gap-2 mb-4"
            onClick={handleClick}
          >
            <span>🚀</span> Start Free Trial
          </button>
        </div>
        <div className="text-center text-xs text-gray-400">
          No credit card required, cancel anytime
        </div>
      </div>

      {landingData.contentInfo['section-features'].title && (
        <>
          {landingData.contentInfo['section-features'].cards.map(
            (item, index) => (
              <div
                className="max-w-5xl mx-auto grid md:grid-cols-2 gap-10 mb-20 items-center"
                key={index}
              >
                <div
                  className={`order-2 ${
                    index % 2 === 0 ? 'order-1' : 'order-2'
                  }`}
                >
                  <h2 className="text-2xl font-bold mb-4">{item.title}</h2>
                  <ul className="text-gray-700 space-y-2 mb-6">
                    {item.tags.map((tag, index) => (
                      <h3 key={index}>
                        <svg
                          className="inline-block align-middle mr-1"
                          width="16"
                          height="16"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle cx="10" cy="10" r="10" fill="#22C55E" />
                          <path
                            d="M6 10.5L9 13.5L14 8.5"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <span className="align-text-top">{tag}</span>
                      </h3>
                    ))}
                  </ul>
                  <button
                    className="bg-primary text-white px-6 py-2 rounded font-semibold hover:bg-primary/90 transition  flex items-center gap-2"
                    onClick={handleClick}
                  >
                    Get started <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
                <div
                  className={`order-1 ${
                    index % 2 === 0 ? 'order-2' : 'order-1'
                  }`}
                >
                  <Image
                    src={item.imgPath}
                    alt="Comments"
                    width={500}
                    loading="lazy"
                    height={350}
                  />
                </div>
              </div>
            ),
          )}
        </>
      )}

      {/* 精选文章 */}
      {landingData.contentInfo['section-top-blog'] &&
        landingData.contentInfo['section-top-blog'].title && (
          <LandingBlog
            title={landingData.contentInfo['section-top-blog'].title}
            desc={landingData.contentInfo['section-top-blog'].desc}
            blogList={landingData.topBlogList}
          />
        )}
      {/* 博客 */}
      {landingData.contentInfo['section-blog'] &&
        landingData.contentInfo['section-blog'].title && (
          <LandingBlog
            title={landingData.contentInfo['section-blog'].title}
            desc={landingData.contentInfo['section-blog'].desc}
            blogList={landingData.blogList}
          />
        )}
      {landingData.contentInfo['section-faq'].title && (
        <div className="max-w-5xl mx-auto my-20 min-h-[600px]">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10">
            {landingData.contentInfo['section-faq'].title}
          </h2>
          <div className="divide-y">
            {landingData.faqList['content_list'].map((item, idx) => (
              <div key={item.question}>
                <button
                  className="w-full flex justify-between items-center px-6 py-5 text-left text-base font-mediumtransition"
                  onClick={() => setOpen(open === idx ? null : idx)}
                >
                  <h3>{item.question}</h3>
                  <span className="ml-2 text-xl">
                    {open === idx ? <ChevronUp /> : <ChevronDown />}
                  </span>
                </button>
                <div
                  className={`px-6 pb-4 text-gray-600 text-sm transition-all duration-300 ${
                    open === idx
                      ? 'max-h-40 opacity-100'
                      : 'max-h-0 opacity-0 overflow-hidden'
                  }`}
                >
                  <div dangerouslySetInnerHTML={{ __html: item.answer }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
