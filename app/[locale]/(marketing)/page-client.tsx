'use client';
import { <PERSON>R<PERSON>, ChevronUp, ChevronDown, UserRound } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import LandingBlog from '@/components/LandingBlog';
import { LandingResponse } from '@/modules/contents/web.interface';
import { useAuthStore } from '@/hooks/store/auth';
import { useRouter } from 'next/navigation';

export default function MarketingPage({
  landingData,
}: {
  landingData: LandingResponse;
}) {
  const [open, setOpen] = useState<number | null>(null);
  const router = useRouter();
  const { isLoginStatus, setIsLoginMode } = useAuthStore();
  const handleClick = () => {
    if (isLoginStatus) {
      router.push('/home');
    } else {
      setIsLoginMode(true);
    }
  };
  return (
    <div className="max-w-[1200px] mx-auto">
      {/* 顶部内容 */}
      <div className="max-w-[700px] mx-auto pt-16 text-center">
        <h1 className="hidden">{landingData.contentInfo['hero'].title}</h1>
        <div className="text-4xl md:text-5xl font-bold mb-4">
          AI SOCIAL MEDIA{' '}
          <span className="text-primary">COMMENT MODERATOR</span>
        </div>
        <p className="text-lg text-gray-600 mb-6">
          {landingData.contentInfo['hero'].desc}
        </p>
        <button
          className="bg-primary text-white px-12 py-3 rounded-lg font-semibold shadow-md hover:bg-primary/90 transition mb-4"
          onClick={handleClick}
        >
          {landingData.contentInfo['hero'].buttonText}
        </button>
        <div className="text-xs text-gray-400 mb-6">
          {landingData.contentInfo['hero'].extraInfo}
        </div>
        <div className="flex justify-center gap-4 mb-10">
          <div className="bg-white rounded-lg shadow px-4 py-2 flex items-center gap-2 w-[250px]">
            <Image
              src="/images/home/<USER>"
              alt="fb"
              width={44}
              height={24}
              priority
              fetchPriority="high"
            />
            <span className="text-sm font-medium">
              Facebook & Instagram <span className="font-bold">Approved</span>
            </span>
          </div>
          <div className="bg-white rounded-lg shadow px-4 py-2 flex items-center gap-2 w-[250px]">
            <Image
              src="/images/home/<USER>"
              alt="lang"
              width={24}
              height={24}
              priority
            />
            <span className="text-sm font-medium">
              Supports <span className="font-bold">20+</span> Languages
            </span>
          </div>
        </div>
      </div>

      {/* 产品截图 */}
      <div className="flex justify-center mb-16">
        <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-100 bg-white max-w-3xl w-full">
          <Image
            src={landingData.contentInfo['hero'].imgPath}
            alt="Dashboard"
            width={900}
            height={500}
            className="w-full h-auto"
            priority
          />
        </div>
      </div>
      {landingData.contentInfo['section-features'].title && (
        <>
          {landingData.contentInfo['section-features'].cards.map(
            (item, index) => (
              <div
                className="max-w-5xl mx-auto grid md:grid-cols-2 gap-10 mb-20 items-center"
                key={index}
              >
                <div
                  className={`order-2 ${
                    index % 2 === 0 ? 'order-1' : 'order-2'
                  }`}
                >
                  <h2 className="text-2xl font-bold mb-4">{item.title}</h2>
                  <ul className="text-gray-700 space-y-2 mb-6">
                    {item.tags.map((tag, index) => (
                      <h3 key={index}>
                        <svg
                          className="inline-block align-middle mr-1"
                          width="16"
                          height="16"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle cx="10" cy="10" r="10" fill="#22C55E" />
                          <path
                            d="M6 10.5L9 13.5L14 8.5"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <span className="align-text-top">{tag}</span>
                      </h3>
                    ))}
                  </ul>
                  <button
                    className="bg-primary text-white px-6 py-2 rounded font-semibold hover:bg-primary/90 transition  flex items-center gap-2"
                    onClick={handleClick}
                  >
                    Get started <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
                <div
                  className={`order-1 ${
                    index % 2 === 0 ? 'order-2' : 'order-1'
                  }`}
                >
                  <Image
                    src={item.imgPath}
                    alt="Comments"
                    width={500}
                    loading="lazy"
                    height={350}
                  />
                </div>
              </div>
            ),
          )}
        </>
      )}

      <div className="max-w-5xl mx-auto mt-16 mb-20">
        <h2 className="text-3xl font-bold text-center mb-10 max-w-[500px] mx-auto">
          {landingData.contentInfo['section-how'].title}
        </h2>
        <div className="grid md:grid-cols-3 gap-4">
          {landingData.contentInfo['section-how'].cards.map((item, index) => (
            <div
              className="bg-[#F1F3F7] rounded-xl shadow p-6 flex flex-col relative overflow-hidden"
              key={index}
            >
              <div className="text-2xl font-bold text-primary mb-2">
                {index + 1}
              </div>
              <h3 className="font-semibold mb-1">{item.title}</h3>
              <div className="text-gray-500 text-sm">{item.desc}</div>
              <Image
                src={item.imgPath}
                alt={item.title}
                width={100}
                height={100}
                loading="lazy"
                className="absolute top-0 right-0"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="max-w-5xl mx-auto mb-20">
        <h2 className="text-3xl font-bold text-center mb-4 max-w-[530px] mx-auto">
          {landingData.contentInfo['section-function'].title}
        </h2>
        <p className="text-center text-gray-500 mb-10 max-w-[750px] mx-auto">
          {landingData.contentInfo['section-function'].desc}
        </p>
        <div className="grid md:grid-cols-3 gap-6">
          {landingData.contentInfo['section-function'].cards.map(
            (item, index) => (
              <div
                className="bg-white rounded-xl shadow p-5 flex flex-col"
                key={index}
              >
                <div className="flex items-center mb-2">
                  <span className="inline-block w-7 h-4 bg-green-500 rounded-full relative transform rotate-180">
                    <span className="absolute left-0.5 top-0.5 w-3 h-3 bg-white rounded-full shadow"></span>
                  </span>
                  {item.isAI == '1' && (
                    <span className="text-sm font-bold text-primary relative ml-4">
                      <span>AI</span>
                      <svg
                        className="absolute top-0 -right-3"
                        width="10"
                        height="10"
                        viewBox="0 0 10 10"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          id="Star 1"
                          d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                          fill="#6246EA"
                        />
                      </svg>
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-bold">{item.title}</h3>
                </div>
                <div className="text-gray-500 text-sm">{item.desc}</div>
                <Image
                  src={item.imgPath}
                  alt={item.title}
                  width={290}
                  loading="lazy"
                  height={190}
                  className="w-full object-contain rounded-sm mt-4"
                />
              </div>
            ),
          )}
        </div>
      </div>
      <section className="max-w-5xl mx-auto my-20">
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10 max-w-[500px] mx-auto">
          {landingData.contentInfo['section-why'].title}
        </h2>
        <div className="grid grid-cols-4 gap-4">
          {landingData.contentInfo['section-why'].cards.map((item, index) => (
            <div
              className="bg-white rounded-xl shadow px-4 py-8 flex flex-col items-center text-center border border-gray-200"
              key={index}
            >
              <Image
                src={item.imgPath}
                alt={item.title}
                width={48}
                height={48}
                className="mb-4"
                loading="lazy"
              />
              <h3 className="font-bold mb-2">{item.title}</h3>
              <div className="text-gray-500 text-sm">{item.desc}</div>
            </div>
          ))}
        </div>
      </section>
      <div className="max-w-5xl mx-auto mb-20">
        <h2 className="text-3xl font-bold text-center mb-8">
          {landingData.contentInfo['section-testimonial'].title}
        </h2>
        <div className="grid grid-cols-3 gap-6">
          {landingData.contentInfo['section-testimonial'].cards.map(
            (item, index) => (
              <div
                className="bg-white rounded-xl shadow p-6 flex flex-col items-start"
                key={index}
              >
                <div className="flex items-center bg-[#F1F3F7] rounded-full p-2 mb-4">
                  <UserRound className="size-6" />
                </div>
                <h3 className="font-semibold mb-2">{item.heading}</h3>
                <div className="text-gray-500 text-sm mb-4">{item.desc}</div>
                <div className="text-xs font-bold">{item.title}</div>
                <div className="text-xs text-gray-400">{item.subTitle}</div>
              </div>
            ),
          )}
        </div>
      </div>

      {/* 精选文章 */}
      {landingData.contentInfo['section-top-blog'].title && (
        <LandingBlog
          title={landingData.contentInfo['section-top-blog'].title}
          desc={landingData.contentInfo['section-top-blog'].desc}
          blogList={landingData.topBlogList}
        />
      )}
      {/* 博客 */}
      {landingData.contentInfo['section-blog'].title && (
        <LandingBlog
          title={landingData.contentInfo['section-blog'].title}
          desc={landingData.contentInfo['section-blog'].desc}
          blogList={landingData.blogList}
        />
      )}

      {landingData.contentInfo['section-faq'].title && (
        <div className="max-w-5xl mx-auto my-20 min-h-[600px]">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10">
            {landingData.contentInfo['section-faq'].title}
          </h2>
          <div className="divide-y">
            {landingData.faqList['content_list'].map((item, idx) => (
              <div key={item.question}>
                <button
                  className="w-full flex justify-between items-center px-6 py-5 text-left text-base font-mediumtransition"
                  onClick={() => setOpen(open === idx ? null : idx)}
                >
                  <h3>{item.question}</h3>
                  <span className="ml-2 text-xl">
                    {open === idx ? <ChevronUp /> : <ChevronDown />}
                  </span>
                </button>
                <div
                  className={`px-6 pb-4 text-gray-600 text-sm transition-all duration-300 ${
                    open === idx
                      ? 'max-h-40 opacity-100'
                      : 'max-h-0 opacity-0 overflow-hidden'
                  }`}
                >
                  <div dangerouslySetInnerHTML={{ __html: item.answer }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
