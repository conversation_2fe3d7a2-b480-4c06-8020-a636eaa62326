import PageClient from './page-client';
import { I18nProps } from '@/i18n/locale';
import { Metadata } from 'next';
import {
  getPageContentContentApi,
  getTdkApi,
} from '@/modules/contents/service';
import { env } from '@/env';
import { page2JsonLd } from '@/lib/jsonLd';
import Script from 'next/script';

let jsonLd: ReturnType<typeof page2JsonLd> | string = '';

export async function generateMetadata({
  params,
}: {
  params: Promise<I18nProps>;
}): Promise<Metadata> {
  const { locale } = await params;
  const tdk = await getTdkApi('/social-media-sentiment-analysis', locale);
  jsonLd = page2JsonLd(
    {
      title: tdk.data?.title,
      url: `${env.NEXT_PUBLIC_SITE_URL}/social-media-sentiment-analysis`,
      desc: tdk.data?.description,
    },
    {
      title: tdk.data?.title,
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      desc: tdk.data?.description,
    },
  );

  const baseURL = env.NEXT_PUBLIC_SITE_URL;
  return {
    title: tdk.data?.title,
    description: tdk.data?.description,
    metadataBase: new URL(baseURL),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}/social-media-sentiment-analysis`,
    },
    openGraph: {
      title: tdk.data?.title,
      description: tdk.data?.description,
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}/social-media-sentiment-analysis`,
      images: [
        {
          url: `/openGraphImgs/socialMediaSentimentAnalysis.webp`,
          width: 1200,
          height: 630,
          alt: tdk.data?.title,
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: tdk.data?.title,
      site: `@commentifyai`,
      description: tdk.data?.description,
      images: [
        {
          url: `/openGraphImgs/socialMediaSentimentAnalysis.webp`,
        },
      ],
    },
  };
}
export default async function SocialMediaSentimentAnalysis({
  params,
  searchParams,
}: {
  params: Promise<I18nProps>;
  searchParams: Promise<{ preview?: string; preview_id?: string }>;
}) {
  const { locale } = await params;
  const { preview, preview_id } = await searchParams;
  const pageRes = await getPageContentContentApi(
    '/social-media-sentiment-analysis',
    locale,
    Number(preview) || '',
    Number(preview_id) || '',
  );
  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <PageClient landingData={pageRes.data} />
    </>
  );
}
