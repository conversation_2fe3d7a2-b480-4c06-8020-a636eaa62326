'use client';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useState } from 'react';
import { cn } from '@/lib/utils';
interface faqCategory {
  id: string;
  name: string;
  questions: {
    id: string;
    question: string;
    answer: string;
  }[];
}

export default function FAQ({ categories }: { categories: faqCategory[] }) {
  const [selectedCategory, setSelectedCategory] = useState('general');

  const currentCategory = categories.find((cat) => cat.id === selectedCategory);

  return (
    <div className="max-w-[1200px] mx-auto py-20 px-4 min-h-[800px]">
      <h1 className="text-4xl font-bold text-center mb-4">How can we help?</h1>

      <div className="flex gap-2 mt-12 mb-20">
        {/* 左侧分类 */}
        <div className="w-[300px] flex-shrink-0">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'w-full text-left px-6 py-4 mb-2 relative font-bold',
                selectedCategory === category.id
                  ? 'bg-gray-100 before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-primary before:transition-all before:duration-300 before:ease-out before:scale-y-100 before:origin-top'
                  : 'hover:bg-gray-50 before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-primary before:scale-y-0 before:transition-transform before:duration-300 before:ease-out before:origin-bottom',
              )}
            >
              {category.name}
            </button>
          ))}
        </div>

        <div className="flex-1">
          <Accordion type="single" collapsible className="w-full">
            {currentCategory?.questions.map((item) => (
              <AccordionItem key={item.id} value={item.id} className="">
                <AccordionTrigger className="py-6 hover:no-underline px-4 text-lg">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="px-4 text-lg">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
