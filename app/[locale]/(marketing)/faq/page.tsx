import { <PERSON>adata } from 'next';
import { getTranslations } from 'next-intl/server';
import FAQPage from './page-client';
import { faqJsonLd } from '@/lib/jsonLd';
import Script from 'next/script';
import { env } from '@/env';

interface FaqCategory {
  id: string;
  name: string;
  questions: {
    id: string;
    question: string;
    answer: string;
  }[];
}

const categories: FaqCategory[] = [
  {
    id: 'general',
    name: 'General Inquiries',
    questions: [
      {
        id: '1',
        question: 'What is Commentify Social Media Moderation Tool?',
        answer:
          'Commentify is an AI-powered social media moderation tool that detects toxic comments through advanced sentiment analysis, automatically hiding hate speech, spam URLs, and negative keywords while preserving genuine engagement.',
      },
      {
        id: '2',
        question: 'Does Commentify work with Facebook and Instagram ads?',
        answer:
          'Commentify automatically moderates all Facebook & Instagram content - ads, posts, Stories & Reels. Uses AI to block spam, scams & hate speech while keeping genuine comments.',
      },
      {
        id: '3',
        question: 'How does automatic comment hiding work?',
        answer:
          'Our AI-powered system analyzes comments in real-time and automatically hides those that match your filtering criteria. You can set up filters for profanity, negative sentiment, competitor mentions, spam links, and more. The system learns from your preferences over time to become even more accurate.',
      },
      {
        id: '4',
        question: 'How do I hide comments on Facebook?',
        answer:
          'With Commentify, you can hide comments on Facebook through various methods. You can manually hide a single comment, set up automatic filters to hide comments containing specific words or phrases, or even hide all comments from a specific user. Our platform allows you to easily manage comment moderation without spending a lot of time on it.',
      },
      {
        id: '5',
        question: 'How to snooze someone on Facebook without them knowing?',
        answer:
          "Commentify allows you to effectively snooze specific users by automatically hiding their comments as soon as they're posted. Unlike Facebook's native snooze feature, the person won't know their comments are being hidden from your page. This is perfect for managing problematic followers without creating conflict.",
      },
      {
        id: '6',
        question: 'Can I hide comments on multiple Facebook pages?',
        answer:
          'Yes! Commentify  supports managing comments across multiple Facebook pages and Instagram accounts from a single dashboard. This is especially useful for agencies or businesses managing several brand accounts.',
      },
      {
        id: '7',
        question: 'Will people know their comments have been hidden?',
        answer:
          'No, when you hide comments using Commentify , the person who posted the comment will still see their own comment, but it will be hidden from everyone else. This helps maintain a positive environment without creating confrontation.',
      },
      {
        id: '8',
        question: 'What is social media sentiment analysis?​',
        answer:
          "It's an AI tool that scans comments, ads, and posts to detect emotions (positive/negative/neutral), helping brands spot trends and avoid PR crises.",
      },
    ],
  },
  {
    id: 'features',
    name: 'Features and Usage',
    questions: [
      {
        id: '1',
        question: 'What is Commentify Social Media Moderation Tool?',
        answer:
          "If you've hidden a comment on Instagram and want to restore it, HidComment makes it simple. In your dashboard, you can view all hidden comments and unhide them with a single click. You can also set up rules to automatically unhide certain types of comments after review.",
      },
      {
        id: '2',
        question: 'How quickly does the tool hide toxic comments?​',
        answer:
          "Instantly! Our AI scans and hides comments the second they're posted—no delay.",
      },
      {
        id: '3',
        question:
          'How does sentiment analysis work for multilingual comments?​',
        answer:
          'Our NLP supports 50+ languages with dialect-specific models (e.g., US vs. UK English).',
      },
      {
        id: '4',
        question: 'Is this tool against Facebook’s policy?',
        answer:
          "No, we comply with ​​Facebook's moderation guidelines​​—you're simply filtering public visibility.",
      },
    ],
  },
];

const faqs = categories.flatMap((category) => category.questions);

let jsonLd: ReturnType<typeof faqJsonLd> | string = '';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('faq');
  jsonLd = faqJsonLd(
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}/faq`,
      desc: t('metadata.description'),
    },
    {
      title: t('metadata.title'),
      url: `${env.NEXT_PUBLIC_SITE_URL}`,
      desc: t('metadata.description'),
    },
    faqs.map((faq) => ({
      question: faq.question,
      answer: faq.answer,
    })),
  );
  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    metadataBase: new URL(env.NEXT_PUBLIC_SITE_URL),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}/faq`,
    },
    openGraph: {
      title: t('metadata.title'),
      description: t('metadata.description'),
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}/faq`,
      images: [
        {
          url: `/openGraphImgs/faq.webp`,
          width: 1200,
          height: 630,
          alt: t('metadata.title'),
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.title'),
      site: `@commentifyai`,
      description: t('metadata.description'),
      images: [
        {
          url: `/openGraphImgs/faq.webp`,
        },
      ],
    },
  };
}

export default function Layout() {
  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <FAQPage categories={categories} />
    </>
  );
}
