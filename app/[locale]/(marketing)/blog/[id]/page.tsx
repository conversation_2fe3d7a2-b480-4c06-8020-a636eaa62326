import {
  getNewArticleDetailData,
  getArticleDetailContent,
  getArticleRecommendTags,
} from '@/modules/contents/service';
import { JSDOM } from 'jsdom';
import BlogDetailUi from '@/components/biz/BlogDetail';
import { Metadata } from 'next';
import { env } from '@/env';
export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const articleDetail = await getNewArticleDetailData(id);

  return {
    title: articleDetail?.data?.title,
    description: articleDetail?.data?.description,
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}/blog/${articleDetail?.data?.title_id}`,
    },
    openGraph: {
      type: 'article',
      title: articleDetail?.data?.title,
      description: articleDetail?.data?.description,
      url: `${env.NEXT_PUBLIC_SITE_URL}/blog/${articleDetail?.data?.title_id}`,
      images: [
        {
          url: articleDetail?.data?.cover_image,
          width: 1200,
          height: 630,
          alt: articleDetail?.data?.title,
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: articleDetail?.data?.title,
      site: `@commentifyai`,
      description: articleDetail?.data?.description,
      images: [
        {
          url: articleDetail?.data?.cover_image,
        },
      ],
    },
  };
}

export default async function ArticleDetail({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const articleDetail = await getNewArticleDetailData(id);
  const [recommendTagsRes, detailContent] = await Promise.all([
    getArticleRecommendTags({
      currentId: `${articleDetail?.data?.article_id}`,
    }),
    getArticleDetailContent(articleDetail?.data?.content_url ?? ''),
  ]);

  const dom = new JSDOM(detailContent.data.body);
  const doc = dom.window.document;
  doc
    .querySelectorAll('style')
    .forEach((style: HTMLStyleElement) => style.remove());
  const h2Texts = Array.from(
    doc.querySelectorAll('h2') as NodeListOf<HTMLHeadingElement>,
  ).map((h2: HTMLHeadingElement) => h2.textContent?.trim());
  doc.querySelectorAll('img').forEach((img: HTMLImageElement) => {
    if (!img.hasAttribute('loading')) {
      img.setAttribute('loading', 'lazy');
    }
  });
  detailContent.data.body = doc.body.innerHTML;

  return (
    <BlogDetailUi
      articleDetail={articleDetail?.data}
      detailContent={detailContent.data}
      h2Texts={h2Texts}
      recommendTagsRes={recommendTagsRes?.data}
    ></BlogDetailUi>
  );
}
