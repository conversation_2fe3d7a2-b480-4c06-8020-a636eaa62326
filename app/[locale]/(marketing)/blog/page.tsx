import {
  getCategoryTopList,
  getArticleListData,
  getThemeData,
} from '@/modules/contents/service';
import BlogUI from '@/components/biz/Blog/index';
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import { env } from '@/env';
export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Blog');

  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    alternates: {
      canonical: `${env.NEXT_PUBLIC_SITE_URL}/blog`,
    },
    openGraph: {
      title: t('metadata.title'),
      description: t('metadata.description'),
      type: 'website',
      url: `${env.NEXT_PUBLIC_SITE_URL}/blog`,
      images: [
        {
          url: `/openGraphImgs/index.webp`,
          width: 1200,
          height: 630,
          alt: t('metadata.title'),
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.title'),
      site: `@commentifyai`,
      images: [
        {
          url: `/openGraphImgs/index.webp`,
        },
      ],
      description: t('metadata.description'),
    },
  };
}
export default async function blog({
  searchParams,
}: {
  searchParams: Promise<{ classifies: string }>;
}) {
  const classifies = (await searchParams).classifies;
  const [articleListRes, bookListRes, themeRes] = await Promise.all([
    getArticleListData({ page_no: 1, page_size: 10, type: classifies }),
    getCategoryTopList({ type: classifies }),
    getThemeData(),
  ]);

  return (
    <BlogUI
      articleListRes={articleListRes}
      bookListRes={bookListRes}
      themeRes={themeRes}
      classifiesProps={classifies}
    />
  );
}
