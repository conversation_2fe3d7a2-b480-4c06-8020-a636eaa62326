'use client';

import { Button } from '@/components/ui/button';
import { CheckCircle2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import confetti from 'canvas-confetti';
import { useTranslations } from 'next-intl';

interface ConfettiOpts {
  spread?: number;
  startVelocity?: number;
  decay?: number;
  scalar?: number;
}

export default function PaymentSuccess() {
  const router = useRouter();
  const t = useTranslations('Payment');
  const fire = (particleRatio: number, opts: ConfettiOpts) => {
    const count = 200;
    const defaults = {
      origin: { y: 0.7 },
    };
    confetti({
      ...defaults,
      ...opts,
      particleCount: Math.floor(count * particleRatio),
    });
  };

  useEffect(() => {
    fire(0.25, {
      spread: 26,
      startVelocity: 55,
    });
    fire(0.2, {
      spread: 60,
    });
    fire(0.35, {
      spread: 100,
      decay: 0.91,
      scalar: 0.8,
    });
    fire(0.1, {
      spread: 120,
      startVelocity: 25,
      decay: 0.92,
      scalar: 1.2,
    });
    fire(0.1, {
      spread: 120,
      startVelocity: 45,
    });
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="w-full max-w-md p-8 space-y-6 text-center">
        <div className="flex justify-center">
          <CheckCircle2 className="w-20 h-20 text-green-500" />
        </div>

        <h1 className="text-2xl font-bold text-foreground">
          {t('success.title')}
        </h1>

        <p className="text-muted-foreground">{t('success.message')}</p>

        <div className="pt-6 space-y-4">
          <Button
            className="w-full bg-primary"
            onClick={() => router.push('/dashboard')}
          >
            {t('success.dashboard')}
          </Button>
        </div>
      </div>
    </div>
  );
}
