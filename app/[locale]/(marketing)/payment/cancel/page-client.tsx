'use client';

import { XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';

export default function PaymentCancel() {
  const t = useTranslations('Payment');
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="w-full max-w-md p-8 space-y-6 text-center">
        <div className="flex justify-center">
          <XCircle className="w-20 h-20 text-destructive" />
        </div>
        <h1 className="text-2xl font-bold text-foreground">
          {t('cancel.title')}
        </h1>
      </div>
    </div>
  );
}
