import Header from '@/components/biz/Header';
import { Metadata } from 'next';
import { env } from '@/env';
import React from 'react';
import Footer from '@/components/biz/Footer';
import Layout from './layout-client';

export async function generateMetadata(): Promise<Metadata> {
  // 获取环境变量中的 URL，如果未设置则使用默认值
  const baseURL = env.NEXT_PUBLIC_SITE_URL;
  return {
    title: env.METADATA_TITLE,
    description: env.METADATA_DESCRIPTION,
    metadataBase: new URL(baseURL),
    openGraph: {
      title: 'commentify - Smart Comment Management for Facebook & Instagram',
      description:
        'Hide, snooze, and manage comments on Facebook and Instagram with AI-powered tools. Save time and improve your social media presence.',
      url: 'https://commentify.com',
      siteName: 'commentify',
      images: [
        {
          url: 'https://commentify.com/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'commentify - Smart Comment Management',
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
  };
}

export default async function MarketingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="relative">
      <Header />
      <Layout>
        <div className="sm:px-2 md:px-0 px-2">{children}</div>
      </Layout>
      <Footer />
    </div>
  );
}
