'use client';
import { LoginUser } from '@/modules/contents/web.interface';
import { trpc } from '@/trpc/client';
import React, { useEffect } from 'react';
import { useAuthStore } from '@/hooks/store/auth';
import { AuroraBackground } from '@/components/ui/aurora-background';
export default function MarketingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { setUser, setHasPlatform, setIsLoginStatus } = useAuthStore();
  const { data: userInfo } = trpc.user.currentUserInfo.useQuery(undefined, {
    retry: 1,
    refetchOnMount: true,
  });

  useEffect(() => {
    if (userInfo?.user) {
      setUser(userInfo.user as unknown as LoginUser);
      setHasPlatform((userInfo.platforms?.length as number) > 0);
      setIsLoginStatus(true);
    }
  }, [userInfo]);
  return (
    <div>
      <AuroraBackground className="h-[400px] absolute top-0 left-0 w-full z-[-1]">
        <div className="w-full h-full bg-white" />
      </AuroraBackground>
      {children}
    </div>
  );
}
