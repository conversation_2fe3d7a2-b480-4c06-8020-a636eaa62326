import { getSitemapList } from '@/modules/contents/service';
import { getServerSideSitemap } from 'next-sitemap';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const sitemapList = await getSitemapList();
    return getServerSideSitemap(sitemapList.data);
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return NextResponse.json(
      { error: 'Error generating sitemap' },
      { status: 500 },
    );
  }
}
