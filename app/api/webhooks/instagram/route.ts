import { NextRequest, NextResponse } from 'next/server';
import { handleInstagramCommentEvents } from '@/modules/core/comments/service';
import { env } from '@/env';
import { logger } from '@/utils/logger';
import { db } from '@/db';
import { webhookEvent } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { InstagramWebhookBody } from '@/modules/core/comments/types';
// 验证令牌，应该存储在环境变量中
const VERIFY_TOKEN = env.FACEBOOK_VERIFY_TOKEN || 'your_verify_token';
// 页面访问令牌，应该根据关联的页面动态获取或存储在环境变量中

// 处理Facebook发送的验证请求
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const mode = searchParams.get('hub.mode');
  const token = searchParams.get('hub.verify_token');
  const challenge = searchParams.get('hub.challenge');

  // 验证模式和令牌
  if (mode === 'subscribe' && token === VERIFY_TOKEN) {
    logger.info('WEBHOOK_VERIFIED');
    return new NextResponse(challenge);
  } else {
    return new NextResponse('Verification failed', { status: 403 });
  }
}

// 处理Facebook发送的Webhook通知
export async function POST(request: NextRequest) {
  const body = await request.json();
  logger.info(body, 'Received webhook event:');

  const [event] = await db
    .insert(webhookEvent)
    .values({
      event: body,
      status: 'pending',
    })
    .returning();
  const { id } = event;
  logger.info({ id, event }, 'Webhook event inserted');
  try {
    const eventBody = event.event as InstagramWebhookBody;
    await handleInstagramCommentEvents(eventBody, id);
    return NextResponse.json({ status: 'ok' });
  } catch (error) {
    logger.error({ id, error }, 'Error processing webhook:');
    await db
      .update(webhookEvent)
      .set({ status: 'failed' })
      .where(eq(webhookEvent.id, id));
    return NextResponse.json(
      { status: 'error', message: error },
      { status: 500 },
    );
  }
}
