import { NextRequest } from 'next/server';
import { StripeService } from '@/modules/stripe/service';

export async function POST(request: NextRequest) {
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature');

  if (!signature) {
    return new Response('No signature', { status: 400 });
  }

  try {
    const event = StripeService.constructEvent(payload, signature);
    await StripeService.handleWebhook(event);
    return new Response('Webhook handled', { status: 200 });
  } catch (err) {
    console.error('Webhook Error:', err);
    return new Response('Webhook Error', { status: 400 });
  }
}
