import { NextResponse } from 'next/server';
// The client you created from the Server-Side Auth instructions
import { createClient } from '@/utils/supabase/server';
import { linkPlatform } from '@/modules/user/service';
import { logger } from '@/utils/logger';
import { refreshSocialPages } from '@/modules/core/social-page/service';

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  logger.info({ searchParams, origin, code }, 'link-callback');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/';
  const redirectPath = '/dashboard/social-channel-configuration';

  if (code) {
    const supabase = await createClient();
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (!error) {
      const user = await supabase.auth.getUser();
      if (user.data.user) {
        // 这里只更新 platformConnect
        const {
          data: { session },
        } = await supabase.auth.getSession();
        await linkPlatform(user.data.user.id, session);
        // 开始刷新主页数据
        await refreshSocialPages(user.data.user.id, 'facebook');
        await refreshSocialPages(user.data.user.id, 'instagram');
      }

      const forwardedHost = request.headers.get('x-forwarded-host'); // original origin before load balancer
      const isLocalEnv = process.env.NODE_ENV === 'development';
      logger.info({ origin, next, forwardedHost }, 'link-callback');
      if (isLocalEnv) {
        // we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
        return NextResponse.redirect(`${origin}${next}${redirectPath}`);
      } else if (forwardedHost) {
        return NextResponse.redirect(
          `https://${forwardedHost}${next}${redirectPath}`,
        );
      } else {
        return NextResponse.redirect(`${origin}${next}${redirectPath}`);
      }
    }
  }

  // return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}
