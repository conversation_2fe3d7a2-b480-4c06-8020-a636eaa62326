import { amazonBedrock } from '@/modules/aisdk/model-providers';
import { appendResponseMessages, smoothStream, streamText } from 'ai';
import { createClient } from '@/utils/supabase/server';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
} from '@/modules/chat/utils';
import {
  generateTitleFromUserMessage,
  getChatById,
  saveChat,
  saveMessages,
} from '@/modules/chat/service';
import { getUserByUid } from '@/modules/user/service';
import { mcpTools } from '@/modules/aisdk/tools/mcp';
import { logger } from '@/utils/logger';
import { chatCredits, deductAmount } from '@/modules/user/service';

// Allow streaming responses up to 60 seconds
export const maxDuration = 60;

export async function POST(req: Request) {
  const { messages, id, externalPageId } = await req.json();
  const supabase = await createClient();
  const {
    data: { user: supabaseUser },
  } = await supabase.auth.getUser();
  if (!supabaseUser) {
    return new Response('Unauthorized', { status: 401 });
  }

  const uid = supabaseUser.id;
  const currentUser = await getUserByUid(uid);
  const remainAmount = currentUser?.remainAmount;
  if (remainAmount === undefined || remainAmount <= 0) {
    logger.warn({ uid, remainAmount }, 'Chat - No credits');
    return new Response('No credits', { status: 400 });
  }

  const pageIdMessage = externalPageId ? ` pageId is ${externalPageId}.` : '';
  const systemPrompt = `
   Your uid is ${uid}, ${pageIdMessage}
   You can pass this uid and pageId to the tools. 
   If you want to call tool, you don't need to tell user what tool you called, just call the tool directly.
  You are tasked with conducting an in-depth analysis of the provided "comment" dataset and generating a detailed report. 
Follow these structured requirements:
1. Sentiment Analysis
● Distribution: Classify comments (pos/neg/neu) via NLP or keywords, show % in charts.
● Intensity: Grade strong emotions (mild/moderate/intense), find triggers. Analyze the distribution and find triggers for extreme emotions.
2. Topic Analysis
● Popular: Use clustering/modeling to find hot topics, summarize & assess impact.
● Emerging: Track new trends by comparing data over time.
3. Temporal Trend Analysis
● Plot comment volume (daily/weekly/monthly), highlight peaks and link to events.
    `;
  logger.info({ systemPrompt }, 'chat');

  const userMessage = getMostRecentUserMessage(messages);

  // 如果没有找到用户消息，返回 400 错误
  if (!userMessage) {
    return new Response('No user message found', { status: 400 });
  }

  const chat = await getChatById({ id });

  if (!chat) {
    // 根据用户第一条消息生成聊天标题
    logger.info({ message: '开始创建chat', userMessage }, 'chat');
    const title = await generateTitleFromUserMessage({
      message: userMessage,
    });

    // 保存新的聊天记录到数据库
    await saveChat({ id, uid, title });
  } else {
    if (chat.uid !== uid) {
      return new Response('Unauthorized', { status: 401 });
    }
  }

  // 保存当前用户的消息到数据库
  await saveMessages({
    messages: [
      {
        chatId: id,
        id: userMessage.id,
        role: 'user',
        parts: userMessage.parts, // 消息内容部分 (多模态)
        attachments: userMessage.experimental_attachments ?? [], // 附件
        externalPageId: externalPageId,
        promptToken: 0,
        completionToken: 0,
        totalToken: 0,
        createdAt: new Date(),
      },
    ],
  });

  const result = streamText({
    model: amazonBedrock('us.anthropic.claude-sonnet-4-20250514-v1:0'),
    providerOptions: {
      bedrock: {
        reasoning_config: { type: 'enabled', budgetTokens: 2048 },
      },
    },
    messages,
    system: systemPrompt,
    maxSteps: 10,
    tools: mcpTools,
    experimental_transform: smoothStream({
      chunking: /[\u4E00-\u9FFF]|\S+\s+/,
    }),
    abortSignal: req.signal,
    experimental_generateMessageId: generateUUID,
    onFinish: async ({ usage, response }) => {
      const { promptTokens, completionTokens, totalTokens } = usage;
      logger.info({ promptTokens, completionTokens, totalTokens }, 'chat');
      if (uid) {
        try {
          // 获取最后一条助手消息的 ID
          const assistantId = getTrailingMessageId({
            messages: response.messages.filter(
              (message) => message.role === 'assistant',
            ),
          });

          if (!assistantId) {
            throw new Error('No assistant message found!');
          }

          // 将用户消息和 AI 响应合并，获取完整的助手消息对象
          const [, assistantMessage] = appendResponseMessages({
            messages: [userMessage],
            responseMessages: response.messages,
          });

          // 保存助手消息到数据库
          await saveMessages({
            messages: [
              {
                id: assistantId,
                chatId: id,
                role: assistantMessage.role,
                parts: assistantMessage.parts,
                attachments: assistantMessage.experimental_attachments ?? [],
                createdAt: new Date(),
                externalPageId: externalPageId,
                promptToken: promptTokens,
                completionToken: completionTokens,
                totalToken: totalTokens,
              },
            ],
          });
          if (totalTokens > 5000) {
            const credits = Math.floor(totalTokens / 5000);
            await deductAmount(uid, credits * chatCredits);
          }
        } catch (error) {
          logger.error({ error }, 'Failed to save chat');
        }
      }
    },
    onError: (error) => {
      logger.error({ error }, '/api/chat throw error');
    },
  });

  return result.toDataStreamResponse({
    sendReasoning: true,
  });
}
