import { NextRequest, NextResponse } from 'next/server';
import { uploadFile, generateUniqueFileKey } from '@/modules/storage/service';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File | null;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Convert File to Buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Get prefix from request or use default
    const prefix = (formData.get('prefix') as string) || 'uploads';

    // Generate a unique key for the file
    const fileKey = generateUniqueFileKey(file.name, prefix);

    // Upload to S3
    const fileUrl = await uploadFile(fileKey, buffer, {
      contentType: file.type,
      isPublic: true,
      metadata: {
        originalName: file.name,
        size: file.size.toString(),
      },
    });

    return NextResponse.json({
      success: true,
      fileUrl,
      fileKey,
      fileName: file.name,
      contentType: file.type,
    });
  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json({ error: 'File upload failed' }, { status: 500 });
  }
}
