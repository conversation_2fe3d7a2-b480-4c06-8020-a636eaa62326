import { NextRequest, NextResponse } from 'next/server';
import { uploadFromURL } from '@/modules/storage/service';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { url, prefix } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'No url provided' }, { status: 400 });
    }
    const fileUrl = await uploadFromURL(url, prefix);
    return NextResponse.json({
      fileUrl,
    });
  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json({ error: 'File upload failed' }, { status: 500 });
  }
}
