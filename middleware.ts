import { type NextRequest, NextResponse } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

// 创建国际化中间件
const intlMiddleware = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const response1 = intlMiddleware(request);
  if (response1 !== NextResponse.next()) {
    return response1;
  }

  const response2 = await updateSession(request);
  if (response2 !== NextResponse.next()) {
    return response2;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|api/|.*\\.(?:svg|png|jpg|jpeg|gif|webp|txt|xml)$).*)',
  ],
};
