{"name": "commentify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "format": "prettier --write .", "db:migrate-dev": "drizzle-kit migrate --config drizzle-dev.config.ts", "db:migrate-prod": "drizzle-kit migrate --config drizzle-prod.config.ts", "db:generate-dev": "drizzle-kit generate --config drizzle-dev.config.ts", "db:generate-prod": "drizzle-kit generate --config drizzle-prod.config.ts"}, "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.5", "@ai-sdk/openai": "^1.3.12", "@ai-sdk/react": "^1.2.6", "@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.1.6", "@nivo/bar": "^0.93.0", "@nivo/core": "^0.93.0", "@nivo/line": "^0.93.0", "@nivo/pie": "^0.93.0", "@nivo/scatterplot": "^0.93.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.1.2", "@stripe/stripe-js": "^7.0.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.12", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.71.10", "@tanstack/react-table": "^8.21.2", "@trpc/client": "^11.0.2", "@trpc/react-query": "^11.0.2", "@trpc/server": "^11.0.2", "ai": "^4.3.2", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.7.1", "export-to-csv": "^1.4.0", "framer-motion": "^12.6.3", "frimousse": "^0.2.0", "jsdom": "^26.1.0", "lucide-react": "^0.474.0", "motion": "^12.10.5", "next": "15.1.4", "next-intl": "^3.26.3", "next-mdx-remote": "^5.0.0", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nprogress": "^0.2.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.5", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-share": "^5.2.2", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "stripe": "18.1.0", "superjson": "^2.2.2", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "usehooks-ts": "^3.1.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@microsoft/clarity": "^1.0.0", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.6", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "prettier": "3.5.3", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}