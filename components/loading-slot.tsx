'use client';

import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingSlotProps {
  children: React.ReactNode;
}

export default function LoadingSlot({ children }: LoadingSlotProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="h-full w-full flex flex-col items-center rounded justify-center bg-white/80">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <div className="text-lg font-medium text-muted-foreground">
            Loading...
          </div>
          <div className="h-1 w-48 overflow-hidden rounded-full bg-secondary">
            <div className="animate-progress h-full w-full bg-primary" />
          </div>
        </div>
      </div>
    );
  }

  return children;
}
