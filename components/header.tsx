import Image from 'next/image';
// import HeaderAvatar from '@/components/header-avatar';
import { createClient } from '@/utils/supabase/server';
import { Link } from '@/i18n/routing';
import LanguageSwitcher from '@/app/[locale]/language-switcher';
import { getTranslations } from 'next-intl/server';
import { env } from '@/env';
import { Button } from './ui/button';

export default async function Header() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  const t = await getTranslations('Home');

  return (
    <header className="fixed top-0 right-0 left-0 h-16 px-4 flex items-center justify-between z-50">
      <Link href="/" className="flex items-center gap-2">
        <Image
          src="/next.svg"
          alt="Logo"
          width={80}
          height={20}
          className="dark:invert"
        />
        <div>{env.NEXT_PUBLIC_SITE_NAME}</div>
      </Link>

      <div className="flex items-center gap-4">
        <Link href="/todo" className="hover:text-gray-600">
          {t('menu.todo')}
        </Link>
        <Link href="/file-upload" className="hover:text-gray-600">
          {t('menu.file-upload')}
        </Link>
        <Link href="/pricing" className="hover:text-gray-600">
          {t('menu.pricing')}
        </Link>
        <Link href="/blog" className="hover:text-gray-600">
          {t('menu.blog')}
        </Link>
        <LanguageSwitcher />
        {user ? (
          <div className="px-4 py-2">{/* <HeaderAvatar user={user} /> */}</div>
        ) : (
          <Button
            onClick={async () => {}}
            className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            {t('auth.loginWithFacebook')}
          </Button>
        )}
      </div>
    </header>
  );
}
