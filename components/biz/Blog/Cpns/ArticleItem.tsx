'use client';
import { imgUrl } from '@/lib/utils';
import { HomeArticleModel } from '@/modules/contents/web.interface';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function ArticleItem({ item }: { item: HomeArticleModel }) {
  const router = useRouter();
  return (
    <div
      className="flex gap-2 justify-between cursor-pointer"
      onClick={() => router.push(`/blog/${item.title_id}`)}
    >
      <div className="flex-[2]">
        <h2 className="font-bold text-lg mb-2">{item.title}</h2>
        <h3 className="mb-2 line-clamp-3">{item.description}</h3>
        <div>{item.create_time}</div>
      </div>
      <div className="flex-[1] text-right">
        {item.cover_image && (
          <Image
            src={imgUrl(item.cover_image, 'mid')}
            width={296}
            height={166}
            alt={item.title}
          ></Image>
        )}
      </div>
    </div>
  );
}
