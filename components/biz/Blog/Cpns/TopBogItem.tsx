'use client';
import { imgUrl } from '@/lib/utils';
import { HomeArticleModel } from '@/modules/contents/web.interface';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function TopBogItem({
  blogItem,
  type,
}: {
  blogItem: HomeArticleModel;
  type: number;
}) {
  const router = useRouter();
  return (
    <div>
      <Image
        className="rounded-md mb-2 cursor-pointer"
        src={imgUrl(blogItem?.cover_image, type == 0 ? 'big' : 'mid')}
        alt={blogItem?.description}
        width={296}
        height={166}
        layout="responsive"
        priority
        onClick={() => {
          router.push(`/blog/${blogItem.title_id}`);
        }}
      ></Image>
      <h2 className={`mb-2 font-bold text-1xl lg:text-3xl leading-5`}>
        {blogItem?.title}
      </h2>
      {!!!type && <h3>{blogItem?.description}</h3>}
    </div>
  );
}
