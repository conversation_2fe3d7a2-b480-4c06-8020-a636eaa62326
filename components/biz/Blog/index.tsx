'use client';

import { getArticleListData } from '@/modules/contents/service';

import {
  HomeArticleModel,
  BaseResponse,
  BaseResponsePage,
  HomeThemeModel,
} from '@/modules/contents/web.interface';
import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import TopBogItem from './Cpns/TopBogItem';
import ArticleItem from './Cpns/ArticleItem';

interface BlogUIProps {
  articleListRes: BaseResponsePage<HomeArticleModel[]>;
  bookListRes: BaseResponse<HomeArticleModel[]>;
  themeRes: BaseResponse<HomeThemeModel>;
  classifiesProps: string;
}
export default function BlogUI({
  articleListRes,
  bookListRes,
  themeRes,
  classifiesProps,
}: BlogUIProps) {
  const { data: bookList } = bookListRes;
  const classifies = themeRes.data?.classifies;
  const t = useTranslations('Blog');
  const [articleRes, setArticleRes] = useState(articleListRes);
  const searchParams = {
    page_no: 2,
    page_size: 10,
    type: classifiesProps,
  };
  const [loading, setLoading] = useState(false);

  const loadMore = async () => {
    setLoading(true);
    const data = await getArticleListData(searchParams);
    searchParams.page_no++;
    setLoading(false);
    setArticleRes({
      ...articleRes,
      has_next: data.has_next,
      data: [...articleRes.data, ...data.data],
    });
  };
  return (
    <div className="max-w-[1200px] m-auto">
      <ul className="flex my-[25px] gap-3">
        <li
          className={`px-8 py-1 bg-white rounded-md cursor-pointer border hover:border-BorderMainColor ${!classifiesProps && 'border-BorderMainColor'}`}
        >
          <Link href={'/blog'}>{t('Newest')}</Link>
        </li>
        {classifies.map((item) => (
          <li
            className={`px-8 py-1 bg-white rounded-md cursor-pointer border hover:border-BorderMainColor ${classifiesProps == item.v && 'border-BorderMainColor'}`}
            key={item.k}
          >
            <Link href={`/blog?classifies=${item.v}`}>{item.v}</Link>
          </li>
        ))}
      </ul>
      <div className="grid grid-cols-10 gap-2 lg:gap-8 lg:grid-cols-12">
        <div className="col-span-6 lg:col-span-8">
          <TopBogItem blogItem={bookList[0]} type={0} />
        </div>
        <div className="col-span-4 border-l border-black pl-2 flex flex-col gap-2 lg:gap-8 lg:pl-8 ">
          <div>
            <TopBogItem blogItem={bookList[1]} type={1} />
          </div>
          <div>
            <TopBogItem blogItem={bookList[2]} type={1} />
          </div>
        </div>
      </div>
      <ul className="mt-10">
        {articleRes.data.map((item, index: number) => (
          <li
            key={item.article_id}
            className="border-t border-black px-2 py-4 relative lg:py-10"
          >
            {index == 0 && (
              <div className="absolute top-[-25px] left-0 bg-[#444444] flex items-center px-4 gap-2">
                <div className="text-white font-bold ">Lastest</div>
                <div className="h-2 w-2 bg-[#F84AA9]"></div>
              </div>
            )}
            <ArticleItem item={item}></ArticleItem>
          </li>
        ))}
      </ul>
      {articleListRes.has_next && (
        <div
          className="text-center py-2 bg-[#F2F2F2] cursor-pointer my-4 mb-8 disabled:opacity-50"
          onClick={loadMore}
        >
          {loading ? (
            <div className="flex items-center justify-center gap-2">
              <div className="animate-spin h-4 w-4 border-2 border-BorderMainColor border-t-transparent rounded-full"></div>
              <span>加载中...</span>
            </div>
          ) : (
            '加载更多'
          )}
        </div>
      )}
    </div>
  );
}
