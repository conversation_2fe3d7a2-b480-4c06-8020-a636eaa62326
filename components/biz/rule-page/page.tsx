import { Ch<PERSON><PERSON><PERSON>own, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2 } from 'lucide-react';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { EmojiPicker as EmojiPickerPrimitive } from 'frimousse';
import { Textarea } from '@/components/ui/textarea';

import {
  EmojiPickerFooter,
  EmojiPickerContent,
} from '@/components/ui/emoji-picker';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useEffect, useMemo, useState } from 'react';
import { Account } from '@/modules/contents/web.interface';
import { trpc } from '@/trpc/client';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface ProcessCommentResult {
  hitRule: string;
  reason: string;
}

export default function RulePage({
  AccountList,
  activeTab,
  selectedAccountOut,
  setSelectedAccountOut,
}: {
  AccountList: Account[] | undefined;
  activeTab: string;
  selectedAccountOut: Account | null;
  setSelectedAccountOut: (account: Account | null) => void;
}) {
  const t = useTranslations('Moderation');
  const utils = trpc.useUtils();
  const [emoji, setEmoji] = useState('');
  const [emojiList, setEmojiList] = useState<string[]>([]);

  const addEmoji = () => {
    const uniqueEmojis = new Set([...emojiList, ...emoji.split(',')]);
    setEmojiList(Array.from(uniqueEmojis));
    setEmoji('');
  };

  const removeEmoji = (index: number) => {
    setEmojiList(emojiList.filter((_, i) => i !== index));
  };

  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);

  useEffect(() => {
    if (activeTab === 'moderation' && AccountList?.[0]) {
      setSelectedAccount(AccountList?.[0]);
    }
  }, [activeTab, AccountList]);

  const updateRuleMutation = trpc.rule.updateRule.useMutation({
    onSuccess: () => {
      utils.rule.getRuleByPageId.invalidate({
        pageId: selectedAccount?.externalPageId ?? '',
      });
      toast.success(t('update_successfully'));
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { data: rule, isLoading: isLoadingRule } =
    trpc.rule.getRuleByPageId.useQuery({
      pageId: selectedAccount?.externalPageId ?? '',
    });

  const [url, setUrl] = useState<string>('');
  const [hashtag, setHashtag] = useState<string>('');
  const [keywords, setKeywords] = useState<string>('');
  useEffect(() => {
    if (rule?.urls) {
      setUrl(rule.urls.split(',').join('\n'));
    }
    if (rule?.tags) {
      setHashtag(rule.tags);
    }
    if (rule?.emojis) {
      setEmojiList(rule.emojis.split(','));
      setEmoji(rule.emojis);
    }
    if (rule?.keywords) {
      setKeywords(rule.keywords);
    }
  }, [rule?.urls, rule?.tags, rule?.emojis, rule?.keywords]);

  const addHashtag = () => {
    if (hashtag) {
      updateRuleMutation.mutate({
        data: {
          ...rule,
          tags: hashtag,
        },
        id: rule?.id as string,
      });
    }
  };
  const removeHashtag = (index: number) => {
    setHashtag(
      hashtag
        .split(',')
        .filter((_, i) => i !== index)
        .join(','),
    );
  };
  const showTags = useMemo(() => {
    return hashtag.split(',').map((tag) => tag.trim());
  }, [hashtag]);
  const showKeywords = useMemo(() => {
    return keywords
      .trim()
      .split(',')
      .map((key) => {
        if (key.trim() === '') return null;
        return key.trim();
      })
      .filter((key) => key !== null);
  }, [keywords]);
  const removeKeyword = (index: number) => {
    setKeywords(
      keywords
        .split(',')
        .filter((_, i) => i !== index)
        .join(','),
    );
  };

  const [processComment, setProcessComment] = useState<string>('');

  const [processCommentResult, setProcessCommentResult] = useState<
    ProcessCommentResult[]
  >([]);

  const processCommentRuleMutation = trpc.rule.processCommentRule.useMutation({
    onSuccess: (data) => {
      setProcessCommentResult(data as ProcessCommentResult[]);
      toast.success(t('comment_processed_successfully'));
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  useEffect(() => {
    if (selectedAccountOut) {
      setSelectedAccount(selectedAccountOut);
    }
  }, [selectedAccountOut]);

  return (
    <div className="px-6 py-6 bg-[#F9FAFE] h-[calc(100vh-140px)] overflow-y-auto">
      <div className="max-w-[900px] mx-auto">
        {!isLoadingRule ? (
          <div>
            <div className="flex items-center justify-between">
              <div className="bg-white rounded-sm border border-gray-200 w-[200px]">
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex w-full items-center justify-between cursor-pointer p-2">
                    <div className="flex items-center gap-2">
                      <Image
                        src={
                          selectedAccount?.avatarUrl ??
                          '/images/dashboard/platform-all.svg'
                        }
                        width={16}
                        height={16}
                        alt="platform"
                      />
                      <span className="text-sm">
                        {selectedAccount?.pageName ?? 'All Platforms'}
                      </span>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    {AccountList?.map((item) => (
                      <DropdownMenuItem
                        key={item.id}
                        onClick={() => {
                          setSelectedAccount(item);
                          setSelectedAccountOut(item);
                        }}
                      >
                        <Image
                          src={item.avatarUrl ?? ''}
                          width={16}
                          height={16}
                          alt="platform"
                        />
                        <span>{item.pageName}</span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="text-sm text-black my-4">
              {t('configure_your_preferences_easily')}
            </div>
            <div className="text-base font-bold text-gray-900">
              {t('moderation_settings')}
            </div>
            <div className="text-sm text-black mt-1 mb-4">
              {t('define_the_moderation_settings_that_will')}
            </div>
            <ul className="grid grid-cols-2 gap-4">
              <li className="flex items-center justify-between shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
                <div className="relative">
                  {t('hide_profanity')}
                  <div className="absolute -right-6 top-0 text-primary font-bold">
                    AI
                    <svg
                      className="absolute -right-3 top-0"
                      width="10"
                      height="10"
                      viewBox="0 0 10 10"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Star 1"
                        d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                        fill="#6246EA"
                      />
                    </svg>
                  </div>
                </div>
                <Switch
                  checked={rule?.hideProfanityStatus}
                  onCheckedChange={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        hideProfanityStatus: !rule?.hideProfanityStatus,
                      },
                      id: rule?.id as string,
                    })
                  }
                  className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                />
              </li>
              <li className="flex items-center justify-between shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
                <div className="relative">
                  {t('hide_negativity')}
                  <div className="absolute -right-6 top-0 text-primary font-bold">
                    AI
                    <svg
                      className="absolute -right-3 top-0"
                      width="10"
                      height="10"
                      viewBox="0 0 10 10"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Star 1"
                        d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                        fill="#6246EA"
                      />
                    </svg>
                  </div>
                </div>
                <Switch
                  checked={rule?.hideNegativityStatus}
                  onCheckedChange={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        hideNegativityStatus: !rule?.hideNegativityStatus,
                      },
                      id: rule?.id as string,
                    })
                  }
                  className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                />
              </li>
              <li className="flex items-center justify-between shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
                <div className="relative">
                  {t('hide_emails_and_phone_numbers')}
                  <div className="absolute -right-6 top-0 text-primary font-bold">
                    AI
                    <svg
                      className="absolute -right-3 top-0"
                      width="10"
                      height="10"
                      viewBox="0 0 10 10"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Star 1"
                        d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                        fill="#6246EA"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <Switch
                    checked={rule?.hideEmailAndPhoneNumberStatus}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideEmailAndPhoneNumberStatus:
                            !rule?.hideEmailAndPhoneNumberStatus,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </li>
              <li className="flex items-center justify-between shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
                <div className="relative">
                  {t('hide_images')}
                  <div className="absolute -right-6 top-0 text-primary font-bold">
                    AI
                    <svg
                      className="absolute -right-3 top-0"
                      width="10"
                      height="10"
                      viewBox="0 0 10 10"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Star 1"
                        d="M5 0L5.87173 2.35582C6.17559 3.17698 6.82302 3.82441 7.64418 4.12827L10 5L7.64418 5.87173C6.82302 6.17559 6.17559 6.82302 5.87173 7.64418L5 10L4.12827 7.64418C3.82441 6.82302 3.17698 6.17559 2.35582 5.87173L0 5L2.35582 4.12827C3.17698 3.82441 3.82441 3.17698 4.12827 2.35582L5 0Z"
                        fill="#6246EA"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <Switch
                    checked={rule?.hideImageStatus}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideImageStatus: !rule?.hideImageStatus,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </li>
            </ul>
            <div className="mt-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
              <div className="flex items-center justify-between">
                <div>{t('hide_urls')}</div>
                <div>
                  <Switch
                    checked={rule?.hideUrlStatus}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideUrlStatus: !rule?.hideUrlStatus,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </div>
              {rule?.hideUrlStatus && (
                <>
                  <div className="my-2">
                    <Textarea
                      placeholder={t('type_here_press_enter_after_each_url')}
                      className="bg-gray-50 resize-none"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      {t(`By_default_all_URLs_will_be_hidden`)}
                    </p>
                    <div
                      className="text-sm bg-[#6246EA] text-white  px-3 py-1 cursor-pointer rounded-sm"
                      onClick={() => {
                        if (url.length === 0) {
                          toast.error(t('Please_enter_at_least_one_url'));
                          return;
                        }
                        updateRuleMutation.mutate({
                          data: {
                            ...selectedAccount,
                            urls: url.split('\n').join(','),
                          },
                          id: rule?.id as string,
                        });
                      }}
                    >
                      {t('save')}
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="mt-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4 flex items-center justify-between">
              <div>
                <div className="text-base">{t('hide_mentions')}</div>
                <div className="text-sm text-gray-500">
                  {t('advanced_settings_by_default_we_hide_all_mentions')}
                </div>
              </div>
              <div>
                <Switch
                  checked={rule?.hideMentionStatus}
                  onCheckedChange={() =>
                    updateRuleMutation.mutate({
                      data: {
                        ...rule,
                        hideMentionStatus: !rule?.hideMentionStatus,
                      },
                      id: rule?.id as string,
                    })
                  }
                  className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                />
              </div>
            </div>
            <div className="mt-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
              <div className="flex items-center justify-between">
                <div>{t('hide_hashtags')}</div>
                <div>
                  <Switch
                    checked={rule?.hideHashTags}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideHashTags: !rule?.hideHashTags,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </div>
              {rule?.hideHashTags && (
                <>
                  <div className="my-2">
                    <div className="relative">
                      <Input
                        placeholder={t(
                          'type_here_press_enter_after_each_hashtag',
                        )}
                        className="bg-gray-50"
                        value={hashtag}
                        onChange={(e) => setHashtag(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && addHashtag()}
                      />
                      <div className="mt-2 cursor-pointer flex flex-wrap gap-2">
                        {showTags?.map((tag, index) => (
                          <div
                            key={index}
                            className="bg-[#EEF2FF]  w-fit rounded px-2 py-0.5 flex items-center gap-1 text-sm  hover:bg-primary hover:text-white"
                          >
                            #<span>{tag}</span>
                            <X
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => removeHashtag(index)}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      {t('Here_you_can_add')}
                    </p>
                    <Button
                      variant="default"
                      className="bg-[#6246EA] hover:bg-[#6246EA]/90 text-white text-sm px-6"
                      onClick={() => {
                        if (hashtag.length === 0) {
                          toast.error(t('Please_enter_at_least_one_hashtag'));
                          return;
                        }
                        updateRuleMutation.mutate({
                          data: {
                            ...rule,
                            tags: hashtag,
                          },
                          id: rule?.id as string,
                        });
                      }}
                    >
                      {t('save')}
                    </Button>
                  </div>
                </>
              )}
            </div>
            <div className="mt-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
              <div className="flex items-center justify-between">
                <div>{t('hide_emojis')}</div>
                <div>
                  <Switch
                    checked={rule?.hideEmojisStatus}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideEmojisStatus: !rule?.hideEmojisStatus,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </div>
              {rule?.hideEmojisStatus && (
                <>
                  <div className="my-4 text-sm text-gray-500">
                    {t('advanced_settings_by_default_we_hide_all_emojis')}
                  </div>

                  <div className="my-2">
                    <div className="relative">
                      <div className="flex items-center border rounded-md">
                        <div className="relative flex-1">
                          <Input
                            placeholder={t(
                              'type_here_press_enter_after_each_emoji',
                            )}
                            value={emoji}
                            onChange={(e) => setEmoji(e.target.value)}
                            className="pr-10"
                            onKeyDown={(e) => e.key === 'Enter' && addEmoji()}
                          />
                          <Popover>
                            <PopoverTrigger asChild>
                              <button
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                type="button"
                              >
                                <SmilePlus className="h-5 w-5 text-primary" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              className="p-0 w-[280px] h-[280px]"
                              align="end"
                            >
                              <EmojiPickerPrimitive.Root
                                className="bg-popover text-popover-foreground isolate flex h-full w-full flex-col overflow-hidden rounded-md"
                                onEmojiSelect={(iemoji) => {
                                  if (iemoji && iemoji.emoji) {
                                    setEmoji(emoji + iemoji.emoji);
                                    const uniqueEmojis = new Set([
                                      ...emojiList,
                                      iemoji.emoji,
                                    ]);
                                    setEmojiList(Array.from(uniqueEmojis));
                                  }
                                }}
                              >
                                {/* <EmojiPickerSearch placeholder="Search emojis..." /> */}
                                <EmojiPickerContent />
                                <EmojiPickerFooter />
                              </EmojiPickerPrimitive.Root>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      <div className="mt-2 cursor-pointer flex flex-wrap gap-2">
                        {emojiList.map((emoji, index) => (
                          <div
                            onClick={() => removeEmoji(index)}
                            key={index}
                            className="bg-[#EEF2FF] hover:bg-[#EEF2FF]/80 w-fit rounded px-2 py-0.5 flex items-center gap-1 text-sm"
                          >
                            <span>{emoji}</span>
                            <X className="h-3 w-3 cursor-pointer hover:text-gray-600" />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-row-reverse">
                    <Button
                      variant="default"
                      className="bg-[#6246EA] hover:bg-[#6246EA]/90 text-white text-sm px-6"
                      onClick={() => {
                        if (emojiList.length === 0) {
                          toast.error(t('Please_enter_at_least_one_emoji'));
                          return;
                        }
                        updateRuleMutation.mutate({
                          data: {
                            ...rule,
                            emojis: emojiList.join(','),
                          },
                          id: rule?.id as string,
                        });
                      }}
                    >
                      {t('save')}
                    </Button>
                  </div>
                </>
              )}
            </div>
            <div className="mt-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
              <div className="flex items-center justify-between">
                <div>{t('hide_keywords')}</div>
                <div>
                  <Switch
                    checked={rule?.hideKeywordsStatus}
                    onCheckedChange={() =>
                      updateRuleMutation.mutate({
                        data: {
                          ...rule,
                          hideKeywordsStatus: !rule?.hideKeywordsStatus,
                        },
                        id: rule?.id as string,
                      })
                    }
                    className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
                  />
                </div>
              </div>
              {rule?.hideKeywordsStatus && (
                <>
                  <div className="my-2">
                    <div className="relative">
                      <Input
                        placeholder={t('type_here_press_enter_after_each_word')}
                        className="bg-gray-50"
                        value={keywords}
                        onChange={(e) => setKeywords(e.target.value)}
                      />
                      <div className="mt-2 cursor-pointer flex flex-wrap gap-2">
                        {showKeywords?.map((keyword, index) => (
                          <div
                            key={index}
                            className="bg-[#EEF2FF] hover:bg-[#EEF2FF]/80 w-fit rounded px-2 py-0.5 flex items-center gap-1 text-sm"
                          >
                            <span>{keyword}</span>
                            <X
                              className="h-3 w-3 cursor-pointer hover:text-gray-600"
                              onClick={() => removeKeyword(index)}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      {t('Here_you_can_add')}
                    </p>
                    <Button
                      variant="default"
                      className="bg-[#6246EA] hover:bg-[#6246EA]/90 text-white text-sm px-6"
                      onClick={() => {
                        if (keywords.length === 0) {
                          toast.error(t('Please_enter_at_least_one_keyword'));
                          return;
                        }
                        updateRuleMutation.mutate({
                          data: {
                            ...rule,
                            keywords: keywords,
                          },
                          id: rule?.id as string,
                        });
                      }}
                    >
                      {t('save')}
                    </Button>
                  </div>
                </>
              )}
            </div>
            <div className="mt-4 flex items-center justify-between shadow border bg-white border-gray-200 rounded-sm p-2 px-4">
              <div>{t('hide_all_comments')}</div>
              <Switch
                checked={rule?.hideAllCommentsStatus}
                onCheckedChange={() =>
                  updateRuleMutation.mutate({
                    data: {
                      ...rule,
                      hideAllCommentsStatus: !rule?.hideAllCommentsStatus,
                    },
                    id: rule?.id as string,
                  })
                }
                className="data-[state=checked]:bg-[#36C71C] data-[state=unchecked]:bg-gray-400"
              />
            </div>

            <div className="mt-4 mb-4 shadow border bg-white border-gray-200 rounded-sm p-2 px-4 pb-4">
              <div>{t('test_your_moderation_settings')}</div>
              <div className="text-sm text-gray-500">
                {t('Here_you_can_test_comments_to_see_if')}
              </div>
              <div className="mt-4">
                <div className="border rounded-md h-[140px] p-2">
                  <Textarea
                    placeholder={t('type_your_comment_here')}
                    className="resize-none h-[40px] border-none shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    value={processComment}
                    onChange={(e) => setProcessComment(e.target.value)}
                  />
                  {processCommentResult.length > 0 && (
                    <div className="mt-2 border rounded-md p-2 bg-gray-50 text-sm line-clamp-2">
                      Result: this comment would be moderated (
                      {processCommentResult.map((result, index) => {
                        const match = RegExp(/^hide(.*?)(Status)?$/).exec(
                          result.hitRule,
                        );
                        return (
                          <span key={result.hitRule}>
                            {match ? match[1] : ''}
                            {index === processCommentResult.length - 1
                              ? ''
                              : ', '}
                          </span>
                        );
                      })}
                      )
                    </div>
                  )}
                </div>
                <div className="mt-2 text-right">
                  <Button
                    variant="default"
                    className={`bg-indigo-600 hover:bg-indigo-700 text-white ${
                      processCommentRuleMutation.isPending
                        ? 'opacity-50 cursor-not-allowed'
                        : ''
                    }`}
                    onClick={() => {
                      if (
                        processCommentRuleMutation.isPending &&
                        processComment.length === 0
                      )
                        return;
                      processCommentRuleMutation.mutate({
                        id: rule?.id as string,
                        message: processComment,
                      });
                    }}
                    disabled={processCommentRuleMutation.isPending}
                  >
                    {processCommentRuleMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      t('process')
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {[1, 2, 3, 4, 5, 6, 7].map((item) => (
              <div
                key={item}
                className="flex gap-2  py-3 px-4 text-sm items-center mb-4 bg-white rounded-md"
              >
                <div className="w-8 flex items-center">
                  <Skeleton className="h-4 w-4" />
                </div>
                <div className="w-28">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <div className="w-28">
                  <Skeleton className="h-4 w-24 mb-2" />
                  <Skeleton className="h-3 w-12" />
                </div>
                <div className="w-28">
                  <Skeleton className="h-6 w-24" />
                </div>
                <div className="flex-1 pr-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3 mt-2" />
                </div>
                <div className="w-20">
                  <Skeleton className="h-4 w-4 rounded-full" />
                </div>
                <div className="w-16 text-center">
                  <Skeleton className="h-4 w-12 mx-auto" />
                </div>
                <div className="w-20 flex justify-end">
                  <Skeleton className="h-6 w-16" />
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
}
