'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import Image from 'next/image';

const CookieBanner: React.FC = () => {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    if (localStorage.getItem('cookie') === 'true') {
      setIsOpen(false);
    }
  }, []);

  return (
    <>
      {isOpen && (
        <div className="fixed bottom-[10px] left-[10px] bg-white shadow-xl shadow-[rgba(0,0,0,0.1)]  rounded-xl flex items-center px-6 py-4 gap-4  z-50 min-w-[200px] max-w-[90vw]">
          {/* 图标 */}
          <div className="text-3xl bg-violet-100 rounded-full p-2">
            <Image src="/icon.webp" alt="cookie" width={30} height={30} />
          </div>
          {/* 文本和链接 */}
          <div className="flex-1 text-gray-400 text-sm w-[320px]">
            We use cookies to improve your experience and for marketing. Read
            our{' '}
            <Link
              href="/privacy-policy"
              className="underline text-gray-400"
              target="_blank"
            >
              Cookie Policy
            </Link>
            .
          </div>
          {/* 按钮 */}
          <Button
            className="bg-violet-600 hover:bg-violet-700 text-white font-semibold"
            onClick={() => {
              localStorage.setItem('cookie', 'true');
              setIsOpen(false);
            }}
          >
            Accept cookies
          </Button>
          {/* 关闭按钮 */}
          <button
            className="ml-2 text-xl text-gray-400 hover:text-gray-700"
            onClick={() => {
              localStorage.setItem('cookie', 'true');
              setIsOpen(false);
            }}
            aria-label="关闭"
          >
            ×
          </button>
        </div>
      )}
    </>
  );
};

export default CookieBanner;
