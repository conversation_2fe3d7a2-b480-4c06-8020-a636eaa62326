'use client';
import React from 'react';
import {
  HomeArticleModel,
  BlogDetailContentModel,
} from '@/modules/contents/web.interface';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { imgUrl } from '@/lib/utils';
import styles from './index.module.css';
// import { FacebookShareButton, TwitterShareButton } from 'react-share';

interface Iprops {
  articleDetail: HomeArticleModel;
  detailContent: BlogDetailContentModel;
  recommendTagsRes: HomeArticleModel[];
  h2Texts: (string | undefined)[];
}

const BlogDetailUi: React.FC<Iprops> = ({
  articleDetail,
  detailContent,
  recommendTagsRes,
  h2Texts,
}) => {
  const t = useTranslations('Blog');
  return (
    <div className="max-w-[1200px] mx-auto flex gap-7 mt-2 lg:mt-12 items-start justify-center">
      {/* <div className="float-left bg-white rounded-lg  flex-col gap-2 p-2 hidden lg:flex ">
        <span>{t('share')}</span>
        <FacebookShareButton url={''} className="w-5 h-5 m-auto">
          <div>
            <Image
              src="/images/share/facebook.webp"
              alt="Facebook Share"
              loading="lazy"
              width={20}
              height={20}
              layout="responsive"
            />
          </div>
        </FacebookShareButton>

        <TwitterShareButton
          title={'erasa'}
          url={'https://www.erasa.com'}
          className="w-5 h-5 m-auto"
        >
          <div>
            <Image
              src={'/images/share/share_fb.webp'}
              alt="Twitter Share"
              loading="lazy"
              width={20}
              height={20}
              layout="responsive"
            />
          </div>
        </TwitterShareButton>

        <div className="w-5 h-5 m-auto">
          <Image
            src={'/images/share/link.webp'}
            alt="Copy"
            loading="lazy"
            width={20}
            height={20}
            layout="responsive"
          />
        </div>
      </div> */}
      <div className="w-full lg:w-[780px] bg-white rounded-lg p-6 mb-8">
        <ul className="flex gap-2">
          <li>
            <Link href={`/`}>Home</Link>
          </li>
          <li>{'>'}</li>
          <li>
            <Link href={`/blog`}>Blog</Link>
          </li>
          <li>{'>'}</li>
          <li>
            <Link href={`/blog`}>{articleDetail?.type || 'Test'}</Link>
          </li>
          <li>{'>'}</li>
          <li className="text-blue-500">{t('Article Details')}</li>
        </ul>
        <h1 className="text-2xl font-bold mt-3">{articleDetail.title}</h1>
        {detailContent?.image_url && (
          <Image
            src={imgUrl(detailContent?.image_url, 'big')}
            priority
            alt={detailContent?.title ?? 'erasa'}
            layout="responsive"
            width={500}
            height={500}
            className="my-6 rounded-lg"
          />
        )}
        {h2Texts?.length > 0 && (
          <div className="bg-[#F7FAFF] rounded-lg p-3 mb-8">
            <div className="text-lg font-bold mb-2">
              {t('TABLE OF CONTENT')}
            </div>
            <ul className="pl-4">
              {h2Texts?.map((item: string | undefined, index: number) => (
                <li key={index} className="list-disc mb-2 underline">
                  <Link href={`#${index + 1}`}>{item}</Link>
                </li>
              ))}
            </ul>
          </div>
        )}

        {detailContent?.body && (
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html: detailContent.body,
            }}
          />
        )}
        <h2 className="text-2xl font-bold mb-3">{t('Read More')}</h2>
        {(recommendTagsRes ?? []).map(
          (item: HomeArticleModel, index: number) => {
            return (
              <div className="mb-2" key={item?.article_id}>
                <span className="mr-2">{index + 1}.</span>
                <Link
                  href={`/blog/${item?.title_id}`}
                  className="text-blue-500"
                >
                  {item.title}
                </Link>
              </div>
            );
          },
        )}
      </div>
      {/* <div className="w-[250px] bg-white rounded-lg p-3 hidden lg:block">2</div> */}
    </div>
  );
};
export default BlogDetailUi;
