import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const Index = () => {
  const t = useTranslations('Home');
  const t1 = useTranslations('Auth');
  return (
    <footer className="border-t border-gray-200 bg-white">
      <div className="max-w-[1200px] mx-auto py-8 px-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="text-xl font-bold">
              <Image src="/icon.webp" alt="logo" width={50} height={50} />
            </Link>
          </div>
          <nav className="flex gap-8 flex-1 justify-center">
            <Link
              href="/hide-comments"
              className="text-gray-600 hover:text-primary"
            >
              {t('menu.hideComment')}
            </Link>
            <Link
              href="/social-media-sentiment-analysis"
              className="text-gray-600 hover:text-primary"
            >
              {t('menu.sentimentAnalysis')}
            </Link>
            <Link href="/pricing" className="text-gray-600 hover:text-primary">
              {t('menu.pricing')}
            </Link>

            <Link href="/faq" className="text-gray-600 hover:text-primary">
              {t('menu.FAQ')}
            </Link>
            <Link href="/blog" className="text-gray-600 hover:text-primary">
              {t('menu.Blog')}
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            {/* <button className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="12" fill="#0F172A" />
              </svg>
            </button>
            <button className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="12" fill="#0F172A" />
              </svg>
            </button>
            <button className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="12" fill="#0F172A" />
              </svg>
            </button>
            <button className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="12" fill="#0F172A" />
              </svg>
            </button> */}
          </div>
        </div>

        <div className="h-[1px] bg-gray-200 my-8" />

        <div className="flex items-center justify-between text-sm text-gray-600">
          <div>All rights reserved. © 2025 Commentify</div>
          <div className="flex items-center gap-4">
            <Link href="/privacy-policy" className="hover:text-gray-900">
              {t1('privacyPolicy')}
            </Link>
            <span className="text-gray-300">|</span>
            <Link href="/terms-use" className="hover:text-gray-900">
              {t1('terms')}
            </Link>
          </div>
        </div>
        <div className="text-gray-600 mt-4">
          This website is not a part Meta Platforms, Inc. and is not endorsed by
          Meta in any way. Facebook™ and Instagram™ are trademarks of Meta
          Platforms, Inc.
        </div>
      </div>
    </footer>
  );
};

export default Index;
