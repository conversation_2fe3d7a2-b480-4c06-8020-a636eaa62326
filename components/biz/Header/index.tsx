'use client';
import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON>, buttonVariants } from '@/components/ui/button';
// import LanguageSwitcher from '@/app/[locale]/language-switcher';
// import HeaderAvatar from '@/components/header-avatar';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/hooks/store/auth';
import { useRouter, usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';

const Index = () => {
  const router = useRouter();
  const t = useTranslations('Home');
  const { user } = useAuthStore();
  const pathname = usePathname();
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const onScroll = () => {
      setScrolled(window.scrollY > 1);
    };
    // 组件挂载时立即判断一次
    onScroll();
    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  }, []);

  const featuresArr = [
    {
      title: t('menu.hideComment'),
      href: '/hide-comments',
    },
    {
      title: t('menu.sentimentAnalysis'),
      href: '/social-media-sentiment-analysis',
    },
  ];

  return (
    <header
      className={`w-full z-50 sticky top-0  ${
        scrolled ? 'bg-white shadow' : 'bg-transparent'
      }`}
    >
      <div className="flex h-16 items-center justify-between px-4 ">
        <div className="flex items-center gap-2">
          <Image
            src="/icon-font.webp"
            alt="commentify Logo"
            width={200}
            height={50}
            className="cursor-pointer"
            onClick={() => {
              router.push('/');
            }}
          />
        </div>
        <nav className="hidden md:flex items-center gap-12">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div
                className={
                  'flex items-center gap-1 text-base font-bold hover:text-primary transition-colors cursor-pointer' +
                  (featuresArr.some((item) => pathname.startsWith(item.href))
                    ? ' text-primary'
                    : '')
                }
              >
                Features <ChevronDown className="ml-1 h-4 w-4" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {featuresArr.map((item) => (
                <DropdownMenuItem
                  key={item.title}
                  onClick={() => {
                    router.push(item.href);
                  }}
                  className={
                    'cursor-pointer' +
                    (pathname === item.href ? 'text-primary' : 'text-gray-600')
                  }
                >
                  {item.title}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <Link
            href="/pricing"
            className={`text-base font-bold focus:outline-none hover:text-primary ${
              pathname === '/pricing' ? 'text-primary' : ''
            }`}
          >
            {t('menu.pricing')}
          </Link>
          <Link
            href="/faq"
            className={`text-base focus:outline-none font-bold hover:text-primary ${
              pathname === '/faq' ? 'text-primary' : ''
            }`}
          >
            {t('menu.FAQ')}
          </Link>
          <Link
            href="/blog"
            className={`text-base focus:outline-none font-bold hover:text-primary ${
              pathname === '/blog' ? 'text-primary' : ''
            }`}
          >
            {t('menu.Blog')}
          </Link>
        </nav>
        <div className="flex items-center gap-4 w-36 justify-end">
          {/* <LanguageSwitcher /> */}
          {user ? (
            <div className="px-4 py-2">
              {/* <HeaderAvatar user={user} /> */}
              <div
                className="cursor-pointer hover:bg-primary/80 rounded-full px-6 py-2 text-sm bg-primary text-white"
                onClick={() => {
                  router.push('/home');
                }}
              >
                Dashboard
              </div>
            </div>
          ) : (
            <Button
              onClick={() => {
                router.push('/home');
              }}
              className={cn(
                buttonVariants({ variant: 'default' }),
                'rounded-full px-10',
              )}
            >
              {t('auth.getStarted')}
            </Button>
          )}
        </div>
      </div>
    </header>
  );
};
export default Index;
