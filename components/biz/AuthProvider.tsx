'use client';
import { useAuthStore } from '@/hooks/store/auth';
import dynamic from 'next/dynamic';
import NProgressProvider from '@/components/NProgressProvider';
const LoginModal = dynamic(() => import('@/components/loginModal'), {
  ssr: false,
});
export default function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isLoginMode, setIsLoginMode } = useAuthStore();

  return (
    <>
      <NProgressProvider />
      {children}
      <LoginModal open={isLoginMode} onOpenChange={setIsLoginMode} />
    </>
  );
}
