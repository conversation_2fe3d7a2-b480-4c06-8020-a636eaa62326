import { ResponsiveScatterPlot } from '@nivo/scatterplot';
import { useMemo } from 'react';

interface Result {
  content: {
    text: string;
    type: string;
  }[];
  isError: boolean;
}

interface PointData {
  x: number;
  y: number;
  size: number;
  color: string;
  painPoint: string;
  solution: string;
}

const BubbleChart = ({ result }: { result: Result }) => {
  const data = useMemo(() => {
    return [
      {
        id: 'PainPoints',
        data: result.content.map((item) => {
          const obj = JSON.parse(item.text);
          return {
            x: obj.frequency,
            y: obj.intensity,
            size: Math.max(4, Math.sqrt(obj.frequency * obj.intensity) / 2),
            color: obj.color,
            painPoint: obj.painPoint,
            solution: obj.solution,
          } as PointData;
        }),
      },
    ];
  }, [result]);

  return (
    <div className="w-full h-[300px] bg-white pr-4 py-4 rounded-sm border border-gray-200 p-2 my-4">
      <div className="text-sm font-bold pl-3">Emotional Drivers Analysis</div>
      <ResponsiveScatterPlot
        data={data}
        margin={{ top: 20, right: 20, bottom: 40, left: 40 }}
        xScale={{ type: 'linear', min: 'auto', max: 'auto' }}
        yScale={{ type: 'linear', min: 'auto', max: 'auto' }}
        nodeSize={(node) => node.data.size || 8}
        colors={{
          scheme: 'category10',
        }}
        blendMode="multiply"
        useMesh={true}
        tooltip={({ node }) => (
          <div
            style={{
              background: 'white',
              padding: 8,
              border: '1px solid #eee',
              borderRadius: 4,
              color: node.data.color,
            }}
          >
            <div>
              <strong>{node.data.painPoint}</strong>
            </div>
            <div>frequency: {node.data.x}</div>
            <div>intensity: {node.data.y}</div>
            <div>solution: {node.data.solution}</div>
          </div>
        )}
        animate={true}
      />
    </div>
  );
};

export default BubbleChart;
