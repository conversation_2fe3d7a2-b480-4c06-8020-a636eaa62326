import React, { useMemo } from 'react';
import { ResponsiveLine } from '@nivo/line';

const colorMap = new Map<string, string>([
  ['commentCount', '#1E5DFF'],
  ['positiveCount', '#F7BA1D'],
  ['negativeCount', '#39C9C9'],
  ['neutralCount', '#F47560'],
  ['pendingCount', '#3498DB'],
]);

const nameMap = new Map<string, string>([
  ['commentCount', 'comment count'],
  ['positiveCount', 'positive count'],
  ['negativeCount', 'negative count'],
  ['neutralCount', 'neutral count'],
  ['pendingCount', 'pending count'],
]);

export default function Analyze_comment_emotions({
  result,
}: {
  result: { content: { text: string }[] };
}) {
  const data = useMemo(() => {
    const dataArr: Record<string, number | string>[] = [];
    const keysSet = new Set<string>();
    const dateArr: string[] = [];

    for (const val of result.content) {
      const obj = JSON.parse(val.text);
      dateArr.push(obj.name); // 假设 name 是日期

      const row: Record<string, number | string> = { name: obj.name };
      for (const v of obj.values) {
        row[v.name] = v.value;
        keysSet.add(v.name);
      }
      dataArr.push(row);
    }

    // 组装 Nivo Line 所需格式
    const keys = Array.from(keysSet);
    const lineData = keys.map((key) => ({
      id: key,
      color: colorMap.get(key) || '#8884d8',
      data: dataArr.map((row) => ({
        x: row.name,
        y: typeof row[key] === 'number' ? row[key] : Number(row[key] || 0),
      })),
    }));

    return lineData;
  }, [result]);

  return (
    <div className="w-full h-[300px]  bg-white pr-4 py-4 rounded-sm border border-gray-200 p-2 my-4">
      <div className="text-sm font-bold pl-3">Topic Analysis</div>
      <ResponsiveLine
        data={data}
        margin={{ top: 20, right: 30, bottom: 40, left: 40 }}
        xScale={{ type: 'point' }}
        yScale={{ type: 'linear', min: 'auto', max: 'auto', stacked: true }}
        colors={({ id }) => colorMap.get(String(id)) || '#8884d8'}
        pointSize={8}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={2}
        pointBorderColor={{ from: 'serieColor' }}
        enableArea={true}
        areaOpacity={0.04}
        useMesh={true}
        tooltip={({ point }) => (
          <div
            style={{
              padding: 8,
              background: 'white',
              border: '1px solid #eee',
              borderRadius: 4,
              color: point.seriesColor,
            }}
          >
            <strong>{nameMap.get(point.seriesId)}</strong>:{' '}
            {point.data.yFormatted}
            <br />
            <span style={{ color: '#888' }}>{point.data.xFormatted}</span>
          </div>
        )}
        role="application"
      />
    </div>
  );
}
