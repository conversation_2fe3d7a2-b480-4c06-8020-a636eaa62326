import React, { useMemo } from 'react';
// import { <PERSON><PERSON><PERSON>, <PERSON>, Tooltip, ResponsiveContainer, Cell } from 'recharts';

import { ResponsivePie } from '@nivo/pie';
interface Iresult {
  content: {
    text: string;
  }[];
  result: {
    color: string;
    name: string;
    value: number;
  }[];
}
const colorMap = new Map<string, string>([
  ['positive', '#1E5DFF'],
  ['negative', '#F7BA1D'],
  ['neutral', '#39C9C9'],
  ['pending', '#F47560'],
]);

export default function Analyze_comment_emotions({
  result,
}: {
  result: Iresult;
}) {
  const data = useMemo(() => {
    return result.content.map((item: Iresult['content'][0]) => {
      const obj = JSON.parse(item.text);

      return {
        id: obj.name,
        label: obj.name,
        value: obj.value,
        color: colorMap.get(obj.name),
      };
    });
  }, [result]);

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="w-full h-[300px] bg-white pr-4 py-4 rounded-sm border border-gray-200 p-2 my-4">
      <div className="text-sm font-bold pl-3">Sentiment Analysis</div>
      <ResponsivePie
        data={data}
        margin={{ top: 40, right: 80, bottom: 80, left: 80 }}
        innerRadius={0}
        padAngle={0.7}
        cornerRadius={3}
        activeOuterRadiusOffset={8}
        borderWidth={1}
        borderColor={{
          from: 'color',
          modifiers: [['darker', 0.2]],
        }}
        arcLinkLabelsSkipAngle={10}
        arcLinkLabelsTextColor="#333333"
        arcLinkLabelsThickness={2}
        arcLinkLabelsColor={{ from: 'color' }}
        arcLabelsSkipAngle={10}
        arcLabelsTextColor={{
          from: 'color',
          modifiers: [['darker', 2]],
        }}
        colors={(datum) => datum.data.color ?? '#8884d8'}
        arcLinkLabel={(d) =>
          `${d.label} ${((d.value / total) * 100).toFixed(1)}%`
        }
        legends={[
          {
            anchor: 'bottom',
            direction: 'row',
            justify: false,
            translateX: 0,
            translateY: 56,
            itemsSpacing: 0,
            itemWidth: 100,
            itemHeight: 18,
            itemTextColor: '#000000',
            itemDirection: 'left-to-right',
            itemOpacity: 1,
            symbolSize: 18,
            symbolShape: 'circle',
          },
        ]}
      />
    </div>
  );
}
