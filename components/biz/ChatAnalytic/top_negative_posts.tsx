import React, { useMemo } from 'react';

import {
  <PERSON>Chart,
  Bar,
  Rectangle,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  //   Legend,
} from 'recharts';
interface Idata {
  color: string;
  name: string;
  value: number;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function Analyze_comment_emotions({ result }: { result: any }) {
  const data = useMemo(() => {
    const data = [];
    for (const val of result.content) {
      const obj = JSON.parse(val.text);
      for (const key in obj.values) {
        obj[obj.values[key].name] = obj.values[key].value;
        obj[obj.values[key].name + 'color'] = obj.values[key].color;
      }

      data.push({
        ...obj,
      });
    }

    return data;
  }, [result]);

  return (
    <div className="w-full h-[300px] my-2 bg-white pr-4 py-4 rounded-sm">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          width={500}
          height={300}
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 10,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          {data[0]?.values.map((item: Idata, index: number) => (
            <Bar
              key={index}
              dataKey={item.name}
              stackId="a"
              fill={item.color}
              activeBar={<Rectangle fill="pink" stroke="blue" />}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
