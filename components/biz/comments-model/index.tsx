'use client';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import Image from 'next/image';

import { useState, useEffect } from 'react';
import {
  ChevronDown,
  Search,
  Eye,
  ThumbsUp,
  Languages,
  Cog,
  Facebook,
  Instagram,
  EyeOff,
  MessageSquareReply,
  ThumbsDown,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { trpc } from '@/trpc/client';
import { timeAgo } from './utils';
import { translateTargetLanguages } from '@/lib/translators';
import { useDebounce } from 'use-debounce';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { toast } from 'sonner';
import { Account } from '@/modules/contents/web.interface';
import { useRouter, useSearchParams } from 'next/navigation';
import { DatePickerWithRange } from '@/components/date-rander-picker';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

export default function CommentsModel({
  AccountList,
  setSelectedAccountOut,
  selectedAccountOut,
}: {
  AccountList: Account[] | undefined;
  setSelectedAccountOut: (account: Account | null) => void;
  selectedAccountOut: Account | null;
}) {
  const searchParamsPath = useSearchParams();
  const externalPageId = searchParamsPath.get('externalPageId');
  const t = useTranslations('Comments');
  const router = useRouter();
  const [searchParams, setSearchParams] = useState<{
    pageIndex: number;
    pageSize: number;
    q: string;
    externalPageId: string;
    emotion: string | undefined;
    hiddenStatus: boolean | undefined;
    startDate: string | undefined;
    endDate: string | undefined;
  }>({
    pageIndex: 1,
    pageSize: 20,
    q: '',
    externalPageId: externalPageId ?? '',
    emotion: undefined,
    hiddenStatus: undefined,
    startDate: undefined,
    endDate: undefined,
  });
  const utils = trpc.useUtils();
  // 账号列表

  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  // 查询参数
  const { data: pageData, isLoading } = trpc.comments.pageListComments.useQuery(
    {
      pageNo: searchParams.pageIndex,
      pageSize: searchParams.pageSize,
      q: searchParams.q,
      externalPageId: selectedAccount?.externalPageId ?? '',
      hiddenStatus: searchParams.hiddenStatus as boolean | undefined,
      emotion: searchParams.emotion as
        | 'pending'
        | 'positive'
        | 'negative'
        | 'neutral'
        | undefined,
      startDate: searchParams.startDate,
      endDate: searchParams.endDate,
    },
  );

  // 添加选中状态管理
  const [selectedComments, setSelectedComments] = useState<string[]>([]);

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedComments(
        pageData?.data.map((comment) => comment.comment.id) ?? [],
      );
    } else {
      setSelectedComments([]);
    }
  };

  // 处理单个评论选中/取消
  const handleSelectComment = (commentId: string, checked: boolean) => {
    if (checked) {
      setSelectedComments((prev) => [...prev, commentId]);
    } else {
      setSelectedComments((prev) => prev.filter((id) => id !== commentId));
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: userData, isLoading: userDataIsLoading } =
    trpc.user.currentUserInfo.useQuery();

  const translateComment = trpc.comments.translateComment.useMutation({
    onSuccess: (data, variables) => {
      utils.comments.pageListComments.setData(
        {
          pageNo: searchParams.pageIndex,
          pageSize: searchParams.pageSize,
          q: searchParams.q,
          externalPageId: selectedAccount?.externalPageId ?? '',
        },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.map((item) => {
              if (item.comment.id === variables.commentId) {
                return {
                  ...item,
                  comment: {
                    ...item.comment,
                    content: data,
                  },
                };
              }
              return item;
            }),
          };
        },
      );
    },
  });

  const [inputValue, setInputValue] = useState('');
  const [debouncedValue] = useDebounce(inputValue, 500);

  // 监听防抖值的变化
  useEffect(() => {
    setSearchParams({
      ...searchParams,
      q: debouncedValue,
      pageIndex: 1,
    });
  }, [debouncedValue]);

  // 导出
  const exportCsv = trpc.comments.exportCsv.useMutation({
    onSuccess: (data) => {
      toast.success('Export CSV successfully');
      window.open(data.fileUrl, '_blank');
    },
  });

  //操作
  const hideUnhideComment = trpc.comments.changeCommentHideStatus.useMutation({
    onSuccess: () => {
      toast.success(t('toast.hideUnhideCommentSuccess'));
      utils.comments.pageListComments.invalidate();
    },
  });

  // 默认选择
  useEffect(() => {
    if (selectedAccountOut) {
      setSelectedAccount(selectedAccountOut);
    }
  }, [selectedAccountOut]);

  return (
    <div className="bg-white rounded-lg shadow h-[calc(100vh-80px)] overflow-auto">
      <div className="px-6 py-6 bg-[#F9FAFE] h-[calc(100vh-80px)] overflow-auto">
        <div className="bg-white rounded-sm border border-gray-200 w-[200px]">
          <DropdownMenu>
            <DropdownMenuTrigger className="flex w-full items-center justify-between cursor-pointer p-2">
              <div className="flex items-center gap-2">
                <Image
                  src={
                    selectedAccount?.avatarUrl ??
                    '/images/dashboard/platform-all.svg'
                  }
                  width={16}
                  height={16}
                  alt="platform"
                />
                <span className="text-sm">
                  {selectedAccount?.pageName ?? t('all_platforms')}
                </span>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuItem
                onClick={() => {
                  setSelectedAccount(null);
                  setSelectedAccountOut(null);
                }}
              >
                <Image
                  src={'/images/dashboard/platform-all.svg'}
                  width={16}
                  height={16}
                  alt="platform"
                />
                <span>{t('all_platforms')}</span>
              </DropdownMenuItem>
              {AccountList?.map((item) => (
                <DropdownMenuItem
                  key={item.id}
                  onClick={() => {
                    setSelectedAccount(item);
                    setSelectedAccountOut(item);
                  }}
                >
                  <Image
                    src={item.avatarUrl ?? ''}
                    width={16}
                    height={16}
                    alt="platform"
                  />
                  <span>{item.pageName}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="my-6 flex justify-between">
          <p>{t('comment_management_desc')}</p>
          <div
            className="flex items-center gap-2"
            onClick={() => {
              if (exportCsv.isPending) return;
              exportCsv.mutate({
                q: searchParams.q,
                externalPageId: searchParams.externalPageId,
                emotion: searchParams.emotion as
                  | 'positive'
                  | 'negative'
                  | 'neutral'
                  | 'pending'
                  | undefined,
                hiddenStatus: searchParams.hiddenStatus,
                platform: selectedAccount?.platform,
              });
            }}
          >
            {exportCsv.isPending ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <svg
                width="15"
                height="15"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Group 3">
                  <g id="uil:export">
                    <path
                      id="Vector"
                      d="M7.25841 6.42508L9.16675 4.50841V12.5001C9.16675 12.7211 9.25455 12.9331 9.41083 13.0893C9.56711 13.2456 9.77907 13.3334 10.0001 13.3334C10.2211 13.3334 10.4331 13.2456 10.5893 13.0893C10.7456 12.9331 10.8334 12.7211 10.8334 12.5001V4.50841L12.7417 6.42508C12.8192 6.50318 12.9114 6.56518 13.0129 6.60749C13.1145 6.64979 13.2234 6.67158 13.3334 6.67158C13.4434 6.67158 13.5523 6.64979 13.6539 6.60749C13.7554 6.56518 13.8476 6.50318 13.9251 6.42508C14.0032 6.34761 14.0652 6.25544 14.1075 6.15389C14.1498 6.05234 14.1716 5.94342 14.1716 5.83341C14.1716 5.7234 14.1498 5.61448 14.1075 5.51293C14.0652 5.41138 14.0032 5.31921 13.9251 5.24174L10.5917 1.90841C10.5125 1.83254 10.419 1.77307 10.3167 1.73341C10.1139 1.65006 9.8863 1.65006 9.68341 1.73341C9.58112 1.77307 9.48767 1.83254 9.40841 1.90841L6.07508 5.24174C5.99738 5.31944 5.93575 5.41168 5.8937 5.5132C5.85165 5.61472 5.83 5.72353 5.83 5.83341C5.83 5.94329 5.85165 6.0521 5.8937 6.15362C5.93575 6.25514 5.99738 6.34738 6.07508 6.42508C6.15278 6.50278 6.24502 6.56441 6.34654 6.60646C6.44806 6.64851 6.55687 6.67015 6.66675 6.67015C6.77663 6.67015 6.88544 6.64851 6.98696 6.60646C7.08847 6.56441 7.18072 6.50278 7.25841 6.42508ZM17.5001 11.6667C17.2791 11.6667 17.0671 11.7545 16.9108 11.9108C16.7545 12.0671 16.6667 12.2791 16.6667 12.5001V15.8334C16.6667 16.0544 16.579 16.2664 16.4227 16.4227C16.2664 16.5789 16.0544 16.6667 15.8334 16.6667H4.16675C3.94573 16.6667 3.73377 16.5789 3.57749 16.4227C3.42121 16.2664 3.33341 16.0544 3.33341 15.8334V12.5001C3.33341 12.2791 3.24562 12.0671 3.08934 11.9108C2.93306 11.7545 2.7211 11.6667 2.50008 11.6667C2.27907 11.6667 2.06711 11.7545 1.91083 11.9108C1.75455 12.0671 1.66675 12.2791 1.66675 12.5001V15.8334C1.66675 16.4965 1.93014 17.1323 2.39898 17.6012C2.86782 18.07 3.50371 18.3334 4.16675 18.3334H15.8334C16.4965 18.3334 17.1323 18.07 17.6012 17.6012C18.07 17.1323 18.3334 16.4965 18.3334 15.8334V12.5001C18.3334 12.2791 18.2456 12.0671 18.0893 11.9108C17.9331 11.7545 17.7211 11.6667 17.5001 11.6667Z"
                      fill="#1D2129"
                    />
                  </g>
                </g>
              </svg>
            )}
            <span className={`text-sm hover:text-primary cursor-pointer `}>
              {t('export_comments')}
            </span>
          </div>
        </div>
        <div className="flex flex-wrap gap-2 mb-6">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder={t('search')}
              className="pl-8 h-9 w-44 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm bg-white"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
          </div>

          <Select
            value={searchParams.emotion}
            onValueChange={(value) => {
              setSearchParams({
                ...searchParams,
                emotion: value === 'undefined' ? undefined : value,
              });
            }}
          >
            <SelectTrigger className="w-[140px] h-9 bg-white">
              <SelectValue placeholder={t('all_sentiments')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={'undefined'}>{t('all_sentiments')}</SelectItem>
              <SelectItem value="positive">{t('positive')}</SelectItem>
              <SelectItem value="neutral">{t('neutral')}</SelectItem>
              <SelectItem value="negative">{t('negative')}</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={
              searchParams.hiddenStatus === undefined
                ? 'undefined'
                : searchParams.hiddenStatus === false
                  ? 'false'
                  : 'true'
            }
            onValueChange={(value) => {
              setSearchParams({
                ...searchParams,
                hiddenStatus:
                  value === 'undefined'
                    ? undefined
                    : value === 'false'
                      ? false
                      : true,
              });
            }}
          >
            <SelectTrigger className="w-[140px] h-9 bg-white">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={'undefined'}>{t('all_status')}</SelectItem>
              <SelectItem value={'false'}>{t('unhidden')}</SelectItem>
              <SelectItem value={'true'}>{t('hidden')}</SelectItem>
            </SelectContent>
          </Select>

          <DatePickerWithRange
            onChange={(value: DateRange | undefined) => {
              setSearchParams({
                ...searchParams,
                startDate: value?.from ? format(value?.from, 'yyyy-MM-dd') : '',
                endDate: value?.to ? format(value.to, 'yyyy-MM-dd') : '',
              });
            }}
          />
          {selectedComments.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-[140px] h-9 bg-indigo-50 text-indigo-700 hover:bg-indigo-100"
                >
                  <span>Batch Actions</span>
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    if (selectedComments.length === 0) {
                      toast.error('Please select at least one comment');
                      return;
                    }
                    console.log(selectedComments, 'selectedComments');
                  }}
                >
                  <div className="flex items-center gap-2">
                    <ThumbsUp className="h-4 w-4" />
                    <span>{t('batch_like')}</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    if (selectedComments.length === 0) {
                      toast.error('Please select at least one comment');
                      return;
                    }
                    console.log(selectedComments, 'selectedComments');
                  }}
                >
                  <div className="flex items-center gap-2">
                    <ThumbsDown className="h-4 w-4" />
                    <span>{t('batch_unlike')}</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async () => {
                    if (selectedComments.length === 0) {
                      toast.error('Please select at least one comment');
                      return;
                    }
                    await hideUnhideComment.mutate({
                      commentIds: selectedComments,
                      hiddenStatus: true,
                      platform: selectedAccount?.platform ?? '',
                    });
                    setSelectedComments([]);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <EyeOff className="h-4 w-4" />
                    <span>{t('batch_hide')}</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async () => {
                    if (selectedComments.length === 0) {
                      toast.error('Please select at least one comment');
                      return;
                    }
                    await hideUnhideComment.mutate({
                      commentIds: selectedComments,
                      hiddenStatus: false,
                      platform: selectedAccount?.platform ?? '',
                    });
                    setSelectedComments([]);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>{t('batch_unhide')}</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <Button
            variant="outline"
            className="ml-auto text-primary border-primary hover:bg-primary/10 hover:text-primary"
            onClick={() => {
              router.push(`/dashboard/chat?id=${selectedAccount?.id}`);
            }}
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.8086 5.41874L13.6446 5.79607C13.619 5.85751 13.5757 5.91 13.5203 5.94692C13.4649 5.98384 13.3998 6.00354 13.3333 6.00354C13.2667 6.00354 13.2016 5.98384 13.1462 5.94692C13.0908 5.91 13.0476 5.85751 13.0219 5.79607L12.8579 5.41874C12.5696 4.75122 12.0415 4.21602 11.3779 3.91874L10.8719 3.69274C10.8105 3.6645 10.7585 3.61925 10.7221 3.56236C10.6856 3.50546 10.6662 3.43931 10.6662 3.37174C10.6662 3.30417 10.6856 3.23801 10.7221 3.18112C10.7585 3.12422 10.8105 3.07897 10.8719 3.05074L11.3499 2.83807C12.0302 2.53232 12.5673 1.97732 12.8506 1.2874L13.0193 0.88007C13.044 0.816972 13.0872 0.762802 13.1432 0.72462C13.1993 0.686438 13.2655 0.666016 13.3333 0.666016C13.401 0.666016 13.4673 0.686438 13.5233 0.72462C13.5793 0.762802 13.6225 0.816972 13.6473 0.88007L13.8159 1.28674C14.0989 1.97679 14.6358 2.53202 15.3159 2.83807L15.7946 3.0514C15.8558 3.07972 15.9076 3.12496 15.9439 3.18179C15.9802 3.23861 15.9995 3.30463 15.9995 3.37207C15.9995 3.43951 15.9802 3.50553 15.9439 3.56235C15.9076 3.61917 15.8558 3.66442 15.7946 3.69274L15.2879 3.91807C14.6245 4.21565 14.0966 4.75109 13.8086 5.41874ZM6.66659 2.00007H9.33325V3.3334H6.66659C5.60572 3.3334 4.5883 3.75483 3.83816 4.50498C3.08801 5.25512 2.66659 6.27254 2.66659 7.3334C2.66659 9.74007 4.30792 11.3107 7.99992 12.9867V11.3334H9.33325C10.3941 11.3334 11.4115 10.912 12.1617 10.1618C12.9118 9.41168 13.3333 8.39427 13.3333 7.3334H14.6666C14.6666 8.74789 14.1047 10.1044 13.1045 11.1046C12.1043 12.1048 10.7477 12.6667 9.33325 12.6667V15.0001C5.99992 13.6667 1.33325 11.6667 1.33325 7.3334C1.33325 5.91892 1.89516 4.56236 2.89535 3.56217C3.89554 2.56197 5.2521 2.00007 6.66659 2.00007Z"
                fill="#6246EA"
              />
            </svg>

            {t('go_to_chat')}
          </Button>
        </div>
        <div>
          {/* 表头 */}
          <div className="flex gap-2 border py-3 px-4 text-sm text-gray-600 mb-4 rounded-md bg-white">
            <div className="w-8 flex items-center">
              <Checkbox
                checked={
                  selectedComments.length === pageData?.data.length &&
                  pageData?.data.length > 0
                }
                onCheckedChange={handleSelectAll}
                className="border-gray-300 data-[state=checked]:bg-[#36C71C] data-[state=checked]:border-[#36C71C]"
              />
            </div>
            <div className="w-40 font-bold">{t('commented_by')}</div>
            <div className="w-28 font-bold">{t('comment')}</div>
            <div className="w-28 font-bold">{t('platforms')}</div>
            <div className="flex-1 font-bold">{t('post_caption')}</div>
            <div className="w-20 font-bold">{t('sentiment')}</div>
            <div className="w-16 text-center font-bold">{t('status')}</div>
            <div className="w-20 text-center font-bold">{t('actions')}</div>
          </div>

          {/* 第一行数据  className="h-[calc(100vh-440px)]" */}
          <div>
            {isLoading ? (
              <>
                {[1, 2, 3, 4].map((item) => (
                  <div
                    key={item}
                    className="flex gap-2 border py-3 px-4 text-sm items-center mb-4 bg-white rounded-md"
                  >
                    <div className="w-8 flex items-center">
                      <Skeleton className="h-4 w-4" />
                    </div>
                    <div className="w-28">
                      <Skeleton className="h-4 w-20 mb-2" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                    <div className="w-28">
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                    <div className="w-28">
                      <Skeleton className="h-6 w-24" />
                    </div>
                    <div className="flex-1 pr-4">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3 mt-2" />
                    </div>
                    <div className="w-20">
                      <Skeleton className="h-4 w-4 rounded-full" />
                    </div>
                    <div className="w-16 text-center">
                      <Skeleton className="h-4 w-12 mx-auto" />
                    </div>
                    <div className="w-20 flex justify-end">
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                ))}
              </>
            ) : (
              pageData?.data.map((item) => (
                <div
                  key={item.comment.id}
                  className="flex gap-2 border py-3 px-4 text-sm items-center mb-4 bg-white cursor-pointer rounded-md hover:bg-gray-50"
                >
                  <div className="w-8 flex items-center">
                    <Checkbox
                      checked={selectedComments.includes(item.comment.id)}
                      onCheckedChange={(checked) =>
                        handleSelectComment(item.comment.id, checked as boolean)
                      }
                      className="border-gray-300 data-[state=checked]:bg-[#36C71C] data-[state=checked]:border-[#36C71C]"
                    />
                  </div>
                  <div className="w-28">
                    <div className="font-medium w-28 truncate">
                      {item.comment.authorName}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {timeAgo(new Date(item.comment.commentTime ?? ''))}
                    </div>
                  </div>
                  <div className="w-40">
                    <div>{item.comment.content}</div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <div className="hover:bg-slate-100 rounded p-1 inline-block">
                          {translateComment.isPending &&
                          translateComment.variables?.commentId ===
                            item.comment.id ? (
                            <Cog className="size-4 animate-spin" />
                          ) : (
                            <Languages
                              className={`h-4 w-4 hover:cursor-pointer ${
                                translateComment.isPending &&
                                translateComment.variables?.commentId ===
                                  item.comment.id
                                  ? 'animate-spin'
                                  : ''
                              }`}
                            />
                          )}
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[180px]">
                        {userData?.user.preferedTranslateLanguage && (
                          <>
                            <DropdownMenuLabel className="font-normal text-sm text-gray-500">
                              {t('action.translate_last_language')}
                            </DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={() => {
                                translateComment.mutate({
                                  commentId: item.comment.id,
                                  message: item.comment.content ?? '',
                                  targetLanguage: userData?.user
                                    .preferedTranslateLanguage as string,
                                });
                              }}
                            >
                              {userData?.user.preferedTranslateLanguage}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                          </>
                        )}
                        <DropdownMenuLabel className="font-normal text-sm text-gray-500">
                          {t('action.translate')}
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {translateTargetLanguages.map((language) => (
                          <DropdownMenuItem
                            key={language}
                            onClick={() => {
                              translateComment.mutate({
                                commentId: item.comment.id,
                                message: item.comment.content ?? '',
                                targetLanguage: language,
                              });
                            }}
                          >
                            {language}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="w-28">
                    <div className="flex items-center gap-2">
                      {item.page.platform === 'facebook' ? (
                        <div className="flex items-center">
                          <Facebook className="h-4 w-4" />
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Instagram className="h-4 w-4 " />
                        </div>
                      )}
                      <Link href={item.post.permalink ?? ''} target="_blank">
                        <span className="text-sm">{item.post.platform}</span>
                      </Link>
                    </div>
                  </div>
                  <div className="flex-1 pr-4">
                    <Link
                      href={item.post.permalink ?? ''}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline flex"
                    >
                      <div className="line-clamp-2 text-sm">
                        {item.post.content}
                      </div>
                    </Link>
                  </div>
                  <div className="w-20 flex items-center">
                    {/* | 'pending'
        | 'positive'
        | 'negative'
        | 'neutral' */}
                    <div
                      className={`rounded-full px-2 py-1 flex items-center gap-2 ${
                        item.comment.emotion === 'negative'
                          ? 'bg-red-100'
                          : 'bg-green-100'
                      }`}
                    >
                      <span
                        className={`inline-block w-2 h-2 rounded-full ${
                          item.comment.emotion === 'negative'
                            ? 'bg-red-500'
                            : 'bg-green-500'
                        }`}
                      ></span>
                      <span
                        className={`${
                          item.comment.emotion === 'negative'
                            ? 'text-red-500'
                            : 'text-green-500'
                        }`}
                      >
                        {item.comment.emotion}
                      </span>
                    </div>
                  </div>
                  <div className="w-16 relative">
                    <div className="flex justify-center">
                      <span>{item.comment.isHidden ? 'hidden' : 'active'}</span>
                    </div>
                  </div>
                  <div className="w-20 flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-1 text-xs flex items-center"
                        >
                          <span className="mr-1">{t('actions')}</span>
                          <ChevronDown className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-fit">
                        <DropdownMenuItem className="flex items-center gap-2">
                          <ThumbsUp className="h-4 w-4" />
                          <span>
                            {item.comment.canLike ? t('like') : t('unlike')}
                          </span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={async () => {
                            await hideUnhideComment.mutate({
                              commentIds: [item.comment.id],
                              hiddenStatus: !item.comment.isHidden,
                              platform: item.page.platform,
                            });
                            toast.success(t('comment_hidden_successfully'));
                            setSearchParams({
                              ...searchParams,
                            });
                          }}
                        >
                          <MessageSquareReply className="h-4 w-4" />
                          <span>
                            {item.comment.isHidden ? t('show') : t('hide')}
                          </span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div className="flex-1 text-sm text-muted-foreground">
            {selectedComments.length} of {pageData?.data.length} row(s)
            selected.
          </div>
          <div className="flex items-center space-x-2">
            <Button
              disabled={searchParams.pageIndex === 1}
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchParams({
                  ...searchParams,
                  pageIndex: searchParams.pageIndex - 1,
                });
              }}
            >
              {t('previous')}
            </Button>
            <Button
              disabled={
                searchParams.pageIndex * searchParams.pageSize >
                (pageData?.total as number)
              }
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchParams({
                  ...searchParams,
                  pageIndex: searchParams.pageIndex + 1,
                });
              }}
            >
              {t('next')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
