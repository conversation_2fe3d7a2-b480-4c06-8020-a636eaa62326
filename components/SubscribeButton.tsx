'use client';
import { trpc } from '@/trpc/client';
import { useLocale } from 'next-intl';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { LoginUser } from '@/modules/contents/web.interface';

interface SubscribeButtonProps {
  priceId: string;
  children: React.ReactNode;
  isActive: boolean;
  isLoginStatus: boolean;
  setIsLoginMode: (isLoginMode: boolean) => void;
  isUpgrade: boolean;
  user: LoginUser | null;
  openModal: () => void;
  setPriceId: (priceId: string) => void;
}

export function SubscribeButton({
  priceId,
  children,
  isActive,
  isLoginStatus,
  setIsLoginMode,
  isUpgrade,
  user,
  openModal,
  setPriceId,
}: SubscribeButtonProps) {
  const locale = useLocale();

  const createCheckoutSession = trpc.stripe.createCheckoutSession.useMutation({
    onSuccess: (url) => {
      if (url) {
        window.location.href = url;
      }
    },
  });
  const handleSubscribe = async () => {
    if (!isLoginStatus) {
      setIsLoginMode(true);
      return;
    }

    if (!user?.currentPlan || user?.currentPlan === 'Free') {
      //购买
      createCheckoutSession.mutate({
        priceId,
        locale,
      });
      return;
    }

    if (isActive) {
      toast.error('You are already subscribed');
      return;
    }
    if (!isUpgrade) {
      toast.error('You are not eligible to upgrade');
    } else {
      setPriceId(priceId);
      openModal();
    }
  };

  return (
    <button
      onClick={handleSubscribe}
      disabled={createCheckoutSession.isPending}
      className={`w-full flex items-center justify-center  bg-primary text-white py-2 rounded-md mb-6 hover:bg-primary/80 ${
        createCheckoutSession.isPending || isActive || !isUpgrade
          ? 'bg-primary/80 cursor-not-allowed'
          : ''
      }`}
    >
      {createCheckoutSession.isPending ? (
        <Loader2 className="w-6 h-6 animate-spin" />
      ) : (
        children
      )}
    </button>
  );
}
