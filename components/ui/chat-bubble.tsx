import * as React from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Copy,
  Check,
  User,
  ChevronUp,
  ChevronDown,
  Loader,
} from 'lucide-react';
import { useCopyToClipboard } from 'usehooks-ts';
import { UIMessage } from 'ai';
import { Account } from '@/modules/contents/web.interface';
import Image from 'next/image';
import AnalysisStepsCollapse from '@/components/AnalysisStepsCollapse';

interface ChatBubbleProps extends React.HTMLAttributes<HTMLDivElement> {
  variant: 'sent' | 'received';
  children: React.ReactNode;
  isCopy?: boolean;
  chatStatus?: 'error' | 'submitted' | 'streaming' | 'ready';
  pageIdMap?: Map<string, string>;
  message?: UIMessage;
  AccountList?: Account[];
}

export function ChatBubble({
  className,
  variant,
  children,
  isCopy,
  chatStatus,
  message,
  pageIdMap,
  AccountList,
  ...props
}: ChatBubbleProps) {
  const selectedAccount = AccountList?.find(
    (account) => account.externalPageId == pageIdMap?.get(message?.id || ''),
  );
  const [collapsed, setCollapsed] = React.useState(false);
  console.log(message);

  React.useEffect(() => {
    if (chatStatus === 'ready') {
      setCollapsed(true);
    }
  }, [chatStatus]);

  return (
    <div>
      {!isCopy && (
        <div
          className={cn(
            'flex border-b border-gray-200 pb-2',
            // variant === 'sent' ? 'justify-end' : 'justify-start',
            className,
          )}
          {...props}
        >
          <div
            className={cn(
              'flex gap-2 items-end max-w-[100%] text-wrap word-break-all',
            )}
          >
            {variant === 'sent' ? (
              <div className="flex flex-col gap-2">
                <div className="text-sm  flex items-center gap-2">
                  <User className="size-5" /> <span>You</span>
                </div>
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <div className="text-sm  flex items-center gap-2 ">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M17.7956 9.13666L17.6011 9.58418C17.5707 9.65705 17.5194 9.7193 17.4537 9.76308C17.388 9.80687 17.3108 9.83023 17.2318 9.83023C17.1529 9.83023 17.0757 9.80687 17.01 9.76308C16.9443 9.7193 16.893 9.65705 16.8626 9.58418L16.6681 9.13666C16.3261 8.345 15.6998 7.71025 14.9128 7.35768L14.3127 7.08965C14.2399 7.05616 14.1783 7.0025 14.135 6.93502C14.0918 6.86755 14.0688 6.78909 14.0688 6.70895C14.0688 6.62881 14.0918 6.55035 14.135 6.48288C14.1783 6.4154 14.2399 6.36174 14.3127 6.32825L14.8796 6.07603C15.6864 5.71342 16.3234 5.05519 16.6594 4.23696L16.8594 3.75387C16.8888 3.67903 16.9401 3.61479 17.0065 3.5695C17.0729 3.52422 17.1514 3.5 17.2318 3.5C17.3122 3.5 17.3908 3.52422 17.4572 3.5695C17.5236 3.61479 17.5749 3.67903 17.6042 3.75387L17.8043 4.23617C18.1399 5.05456 18.7766 5.71306 19.5833 6.07603L20.151 6.32904C20.2235 6.36262 20.285 6.41628 20.3281 6.48367C20.3711 6.55106 20.394 6.62937 20.394 6.70934C20.394 6.78932 20.3711 6.86763 20.3281 6.93502C20.285 7.00241 20.2235 7.05606 20.151 7.08965L19.5501 7.35689C18.7632 7.70982 18.1372 8.34484 17.7956 9.13666ZM9.32526 5.08217H11.6972C12.1339 5.08217 12.4879 5.43616 12.4879 5.87283C12.4879 6.3095 12.1339 6.66349 11.6972 6.66349H9.32526C8.06709 6.66349 6.86045 7.16329 5.97079 8.05296C5.08112 8.94262 4.58132 10.1493 4.58132 11.4074C4.58132 14.2617 6.52792 16.1245 10.9066 18.1122V16.1514H12.4879C13.7461 16.1514 14.9527 15.6516 15.8424 14.7619C16.5443 14.06 17.0036 13.1607 17.1661 12.1945C17.2386 11.7639 17.5858 11.4074 18.0225 11.4074C18.4592 11.4074 18.8183 11.7628 18.7638 12.1961C18.5896 13.5828 17.9593 14.8813 16.9605 15.8801C15.7743 17.0663 14.1655 17.7327 12.4879 17.7327V20.5C8.53461 18.9187 3 16.5467 3 11.4074C3 9.72987 3.66641 8.12101 4.85263 6.9348C6.03884 5.74858 7.6477 5.08217 9.32526 5.08217Z"
                      fill="#1D2129"
                    />
                  </svg>

                  <span>AI assistant</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {variant != 'sent' && !isCopy && (
        <div>
          <div className="flex justify-between py-4">
            <div className="flex gap-4 items-center">
              <div className="flex items-center gap-2 text-sm">
                <svg
                  className="size-4"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 7L9.484 8.394C8.808 10.222 8.47 11.136 7.803 11.803C7.136 12.47 6.222 12.808 4.394 13.484L3 14L4.394 14.516C6.222 15.192 7.136 15.531 7.803 16.197C8.47 16.863 8.808 17.778 9.484 19.606L10 21L10.516 19.606C11.192 17.778 11.531 16.864 12.197 16.197C12.863 15.53 13.778 15.192 15.606 14.516L17 14L15.606 13.484C13.778 12.808 12.864 12.47 12.197 11.803C11.53 11.136 11.192 10.222 10.516 8.394L10 7ZM18 3L17.779 3.597C17.489 4.381 17.344 4.773 17.059 5.058C16.773 5.344 16.381 5.489 15.597 5.778L15 6L15.598 6.221C16.381 6.511 16.773 6.656 17.058 6.941C17.344 7.227 17.489 7.619 17.778 8.403L18 9L18.221 8.403C18.511 7.619 18.656 7.227 18.941 6.942C19.227 6.656 19.619 6.511 20.403 6.222L21 6L20.402 5.779C19.619 5.489 19.227 5.344 18.942 5.059C18.656 4.773 18.511 4.381 18.222 3.597L18 3Z"
                    stroke="#1D2129"
                    strokeWidth="1.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span>Task Steps</span>
              </div>

              {!isCopy && chatStatus == 'ready' && (
                <div className="flex items-center gap-2">
                  <Check className="text-white bg-green-500 rounded-full size-4" />
                  <span className="text-sm">
                    Data has been successfully retrieved.
                  </span>
                </div>
              )}
            </div>
            {chatStatus == 'ready' && (
              <div
                className="cursor-pointer"
                onClick={() => setCollapsed(!collapsed)}
              >
                {collapsed && !isCopy ? (
                  <ChevronUp size={15} />
                ) : (
                  <ChevronDown size={15} />
                )}
              </div>
            )}
          </div>
          {message?.role === 'assistant' && selectedAccount?.pageName && (
            <div className="flex items-center gap-4 border border-gray-200 p-2 rounded-sm mb-4">
              <div className="text-sm text-white bg-blue-400 px-2 py-1 rounded-full">
                Internal Data
              </div>
              {selectedAccount?.avatarUrl ? (
                <Image
                  src={selectedAccount.avatarUrl}
                  alt={selectedAccount.pageName}
                  width={24}
                  height={24}
                />
              ) : (
                <div className="size-6 bg-primary rounded-full flex items-center justify-center text-white text-sm">
                  {selectedAccount.pageName.slice(0, 1)}
                </div>
              )}
              <div className="text-sm">{selectedAccount.pageName}</div>
              <div className="text-sm">{selectedAccount.platform}</div>
            </div>
          )}
        </div>
      )}
      {variant != 'sent' &&
        !isCopy &&
        message?.role === 'assistant' &&
        (chatStatus == 'submitted' || chatStatus == 'streaming') && (
          <div className="flex items-center gap-2 mb-4 ">
            <Loader className="text-primary  animate-spin size-4" />
            <span className="text-sm">Loading data...</span>
          </div>
        )}
      {variant != 'sent' && !isCopy && chatStatus == 'ready' && (
        <div className="text-sm p-2 bg-[#F7F8FA] w-fit rounded-sm">
          Analyzing Conclusions
        </div>
      )}
      {message?.parts && (
        <AnalysisStepsCollapse
          parts={message?.parts}
          collapsed={collapsed}
          setCollapsed={setCollapsed}
          chatStatus={status}
        />
      )}

      <div className="flex flex-col gap-2 mt-1">{children}</div>
    </div>
  );
}

export interface ChatBubbleAvatarProps
  extends React.ComponentProps<typeof Avatar> {
  src?: string;
  fallback?: string;
}

export function ChatBubbleAvatar({
  className,
  src,
  fallback,
  ...props
}: ChatBubbleAvatarProps) {
  return (
    <Avatar className={cn(className)} {...props}>
      {src && <AvatarImage src={src} />}
      {fallback && <AvatarFallback>{fallback}</AvatarFallback>}
    </Avatar>
  );
}

interface ChatBubbleMessageProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'sent' | 'received';
  isLoading?: boolean;
  chatStatus: 'error' | 'submitted' | 'streaming' | 'ready';
  isCopy?: boolean;
}

export function ChatBubbleMessage({
  className,
  variant = 'sent',
  chatStatus,
  children,
  isCopy,
  ...props
}: ChatBubbleMessageProps) {
  const [, copy] = useCopyToClipboard();
  const [copied, setCopied] = React.useState(false);
  const messageRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (copied) {
      const timeout = setTimeout(() => setCopied(false), 2000);
      return () => clearTimeout(timeout);
    }
  }, [copied]);

  const handleCopy = () => {
    if (messageRef.current) {
      const text = messageRef.current.textContent || '';
      copy(text);
      setCopied(true);
    }
  };

  return (
    <div
      className={cn('py-2 max-w-full', variant === 'sent', className)}
      {...props}
    >
      <div className="flex flex-col gap-2">
        <div ref={messageRef}>{children}</div>
        {variant === 'received' && chatStatus === 'ready' && !isCopy && (
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={handleCopy}
            >
              {copied ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              <span className="sr-only">Copy message</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
