import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

interface ChartProps {
  data: {
    labels: string[];
    values: number[];
    title: string;
    chart: {
      data: {
        labels: string[];
        values: number[];
        title: string;
      };
    };
  };
}

export function Chart({ data }: ChartProps) {
  const { values, labels, title } = data;

  const chartData = {
    labels: labels,
    datasets: [
      {
        label: '评分',
        data: values,
        backgroundColor: 'rgb(64, 116, 252)', // 统一使用蓝色
        borderColor: 'rgb(64, 116, 252)',
        borderWidth: 0,
        barThickness: 50,
      },
    ],
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const options: any = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // 隐藏图例
      },
      title: {
        display: true,
        text: title,
        color: '#333',
        font: {
          size: 16,
          weight: 'normal',
        },
        padding: { bottom: 30 },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 3,
        ticks: {
          stepSize: 1,
          color: '#666',
          font: {
            size: 12,
          },
        },
        grid: {
          color: '#f0f0f0',
        },
        border: {
          display: false,
        },
      },
      x: {
        ticks: {
          color: '#666',
          font: {
            size: 12,
          },
        },
        grid: {
          display: false, // 隐藏网格线
        },
        border: {
          display: false,
        },
      },
    },
  };

  return (
    <div className="w-full h-[400px] p-6 bg-white">
      <Bar data={chartData} options={options} />
    </div>
  );
}
