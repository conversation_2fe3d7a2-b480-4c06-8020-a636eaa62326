'use client';
import { Link } from '@/i18n/routing';
import { LoginUser } from '@/modules/contents/web.interface';

export default function HeaderAvatar({ user }: { user: LoginUser }) {
  return (
    <Link
      href="/home"
      className="top-4 right-4 hover:opacity-80 transition-opacity duration-200"
    >
      {user?.avatar ? (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={user?.avatar}
          alt="User Avatar"
          width={40}
          height={40}
          className="rounded-full object-cover w-8 h-8"
          style={{ objectFit: 'cover' }}
        />
      ) : (
        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
          <span className="text-white text-lg ">
            {user.email.slice(0, 1).toLocaleUpperCase()}
          </span>
        </div>
      )}
    </Link>
  );
}
