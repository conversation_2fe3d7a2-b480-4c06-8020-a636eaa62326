'use client';
import { useState } from 'react';
import { DataTable } from '@/components/comments-data-table';
import { Button } from '@/components/ui/button';
import { trpc } from '@/trpc/client';
import { Input } from '@/components/ui/input';
import { useDebounce } from 'use-debounce';
import { toast } from 'sonner';
import { ColumnDef } from '@tanstack/react-table';
import {
  ExternalLink,
  Loader2,
  Meh,
  RefreshCcw,
  EyeClosed,
  Eye,
  MoreHorizontal,
  Reply,
  Sun,
  CloudRain,
  PenTool,
  Languages,
  Facebook,
  Instagram,
  Cog,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CommentWithPost } from '@/db/schema';
import { useTranslations } from 'next-intl';
import {
  Dialog,
  DialogFooter,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogHeader,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
  Tooltip,
} from '@/components/ui/tooltip';
import Link from 'next/link';
import { translateTargetLanguages } from '@/lib/translators';
const replyFormSchema = z.object({
  replyText: z.string().min(1, {
    message: 'Reply text cannot be empty',
  }),
});

type ReplyFormValues = z.infer<typeof replyFormSchema>;

const CommentsClient = () => {
  const t = useTranslations('Comments');
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [platform, setPlatform] = useState<string | undefined>(undefined);
  const [contentQuery] = useDebounce(searchQuery, 800);
  const [isReplyDialogOpen, setIsReplyDialogOpen] = useState(false);
  const [replyingToComment, setReplyingToComment] = useState<
    CommentWithPost['comment'] | null
  >(null);

  const utils = trpc.useUtils();
  const mutate = trpc.comments.fetchLatestComments.useMutation({
    onSuccess: () => {
      toast.success(t('toast.fetchCommentsSuccess'));
      utils.comments.pageListComments.invalidate();
    },
  });
  const hideUnhideComment = trpc.comments.changeCommentHideStatus.useMutation({
    onSuccess: () => {
      toast.success(t('toast.hideUnhideCommentSuccess'));
      utils.comments.pageListComments.invalidate();
    },
  });
  const exportCsv = trpc.comments.exportCsv.useMutation({
    onSuccess: (data) => {
      toast.success('Export CSV successfully');
      window.open(data.fileUrl, '_blank');
    },
  });
  const replyToComment = trpc.comments.replyToComment.useMutation({
    onSuccess: () => {
      toast.success(t('toast.replyToCommentSuccess'));
      utils.comments.pageListComments.invalidate();
      setIsReplyDialogOpen(false);
      setReplyingToComment(null);
      form.reset();
    },
  });
  const { data: userData, isLoading: userDataIsLoading } =
    trpc.user.currentUserInfo.useQuery();

  const generateCommentEmotion =
    trpc.comments.generateCommentEmotion.useMutation({
      onSuccess: () => {
        toast.success(t('toast.generateCommentEmotionSuccess'));
        utils.comments.pageListComments.invalidate();
        utils.user.currentUserInfo.invalidate();
      },
    });

  const translateComment = trpc.comments.translateComment.useMutation({
    onSuccess: (data, variables) => {
      utils.user.currentUserInfo.invalidate();
      // Update the comment in the cache with the translated content
      utils.comments.pageListComments.setData(
        {
          pageNo: pagination.pageIndex,
          pageSize: pagination.pageSize,
          q: contentQuery,
          platform: platform,
        },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.map((item) => {
              if (item.comment.id === variables.commentId) {
                return {
                  ...item,
                  comment: {
                    ...item.comment,
                    content: data,
                  },
                };
              }
              return item;
            }),
          };
        },
      );
    },
  });

  const form = useForm<ReplyFormValues>({
    resolver: zodResolver(replyFormSchema),
    defaultValues: {
      replyText: '',
    },
  });

  const { data, isLoading, isFetching } =
    trpc.comments.pageListComments.useQuery({
      pageNo: pagination.pageIndex,
      pageSize: pagination.pageSize,
      q: contentQuery,
      platform: platform,
    });

  const handlePaginationChange = (updatedPagination: {
    pageIndex: number;
    pageSize: number;
  }) => {
    setPagination(updatedPagination);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Reset to first page when searching
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const handlePlatformChange = (value: string) => {
    setPlatform(value === 'all' ? undefined : value);
    // Reset to first page when changing platform
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const onSubmitReply = (values: ReplyFormValues) => {
    if (replyingToComment) {
      replyToComment.mutate({
        commentId: replyingToComment.id,
        replyText: values.replyText,
      });
    }
  };

  // Calculate page count based on total items and page size
  const totalPages = data?.total
    ? Math.ceil(data.total / pagination.pageSize)
    : 0;

  const columns: ColumnDef<CommentWithPost>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <input
          type="checkbox"
          className="h-4 w-4 rounded border-gray-300"
          checked={table.getIsAllPageRowsSelected()}
          ref={(input) => {
            if (input) {
              input.indeterminate =
                table.getIsSomePageRowsSelected() &&
                !table.getIsAllPageRowsSelected();
            }
          }}
          onChange={(e) => table.toggleAllPageRowsSelected(!!e.target.checked)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          className="h-4 w-4 rounded border-gray-300"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(!!e.target.checked)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'comment.authorName',
      header: t('columns.commentBy'),
    },
    {
      id: 'commentContent',
      accessorKey: 'comment.content',
      header: t('columns.comment'),
      cell: ({ row }) => {
        const comment = row.original.comment;
        return (
          <div className="flex items-center gap-2">
            {comment.emotion === 'positive' ? (
              comment.ai_reason ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Sun className="h-4 w-4 hover:cursor-pointer hover:bg-slate-200" />
                    </TooltipTrigger>
                    <TooltipContent className="w-64">
                      {comment.ai_reason}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <Sun className="h-4 w-4" />
              )
            ) : comment.emotion === 'negative' ? (
              comment.ai_reason ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <CloudRain className="h-4 w-4" />
                    </TooltipTrigger>
                    <TooltipContent className="w-64">
                      {comment.ai_reason}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <CloudRain className="h-4 w-4" />
              )
            ) : comment.emotion === 'neutral' ? (
              comment.ai_reason ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Meh className="h-4 w-4" />
                    </TooltipTrigger>
                    <TooltipContent className="w-64">
                      {comment.ai_reason}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <Meh className="h-4 w-4" />
              )
            ) : generateCommentEmotion.isPending &&
              generateCommentEmotion.variables?.commentId === comment.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <PenTool
                className="h-4 w-4 hover:cursor-pointer hover:bg-slate-200"
                onClick={() => {
                  generateCommentEmotion.mutate({
                    commentId: row.original.comment.id,
                    commentText: row.original.comment.content ?? '',
                  });
                }}
              />
            )}

            <span>{comment.content}</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                {translateComment.isPending &&
                translateComment.variables?.commentId === comment.id ? (
                  <Cog className="size-4 animate-spin" />
                ) : (
                  <Languages
                    className={`h-4 w-4 hover:cursor-pointer hover:bg-slate-200 ${
                      translateComment.isPending &&
                      translateComment.variables?.commentId === comment.id
                        ? 'animate-spin'
                        : ''
                    }`}
                  />
                )}
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {userData?.user.preferedTranslateLanguage && (
                  <>
                    <DropdownMenuLabel>
                      {t('action.translate_last_language')}
                    </DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => {
                        translateComment.mutate({
                          commentId: row.original.comment.id,
                          message: row.original.comment.content ?? '',
                          targetLanguage: userData?.user
                            .preferedTranslateLanguage as string,
                        });
                      }}
                    >
                      {userData?.user.preferedTranslateLanguage}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                <DropdownMenuLabel>{t('action.translate')}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {translateTargetLanguages.map((language) => (
                  <DropdownMenuItem
                    key={language}
                    onClick={() => {
                      translateComment.mutate({
                        commentId: row.original.comment.id,
                        message: row.original.comment.content ?? '',
                        targetLanguage: language,
                      });
                    }}
                  >
                    {language}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <Reply
              className="h-4 w-4 hover:cursor-pointer hover:bg-slate-200"
              onClick={() => {
                setReplyingToComment(comment);
                setIsReplyDialogOpen(true);
                form.reset();
              }}
            />
          </div>
        );
      },
    },
    {
      id: 'postMessage',
      accessorKey: 'post.content',
      header: t('columns.post'),
      cell: ({ row }) => {
        return (
          <Link
            href={row.original.post.permalink ?? ''}
            target="_blank"
            rel="noopener noreferrer"
            className="hover:underline flex"
          >
            {row.original.post.content}
            <ExternalLink className="h-4 w-4" />
          </Link>
        );
      },
    },
    {
      id: 'pageName',
      accessorKey: 'page.pageName',
      header: t('columns.page'),
      cell: ({ row }) => {
        return (
          <>
            {row.original.page.platform === 'facebook' ? (
              <div className="flex items-center gap-2">
                <Facebook className="h-4 w-4" />
                <span>{row.original.page.pageName}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Instagram className="h-4 w-4" />
                <span>{row.original.page.pageName}</span>
              </div>
            )}
          </>
        );
      },
    },
    {
      id: 'isHidden',
      accessorKey: 'comment.isHidden',
      header: t('columns.isHidden'),
      cell: ({ row }) => {
        return (
          <>
            {row.original.comment.isHidden === true ? (
              <EyeClosed className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </>
        );
      },
    },
    {
      id: 'commentTime',
      accessorKey: 'comment.commentTime',
      header: t('columns.commentAt'),
      cell: ({ row }) => {
        return (
          <span>
            {row.original.comment.commentTime
              ? row.original.comment.commentTime.toLocaleString()
              : ''}
          </span>
        );
      },
    },
    {
      id: 'actions',
      header: t('columns.actions'),
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">{t('action.label')}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t('action.label')}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  hideUnhideComment.mutate({
                    commentIds: [row.original.comment.id],
                    hiddenStatus: !row.original.comment.isHidden,
                    platform: row.original.page.platform,
                  });
                }}
              >
                {t('action.hideUnhide')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <p className="text-muted-foreground">{t('description')}</p>
        {userDataIsLoading ? (
          <p className="text-muted-foreground">Loading remaining usage...</p>
        ) : (
          <p className="text-muted-foreground font-bold">
            {userData?.user.amount} comments remaining
          </p>
        )}
        <div className="my-6 flex items-center justify-between">
          <div className="flex gap-4">
            <Input
              placeholder={t('query.placeholder')}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="max-w-sm"
            />
            <Select
              value={platform || 'all'}
              onValueChange={handlePlatformChange}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue
                  placeholder={
                    t('query.platformPlaceholder') || 'All platforms'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t('query.allPlatforms') || 'All platforms'}
                </SelectItem>
                <SelectItem value="facebook">Facebook</SelectItem>
                <SelectItem value="instagram">Instagram</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              mutate.mutate();
            }}
            disabled={mutate.isPending}
          >
            <RefreshCcw className="mr-2 h-4 w-4" />
            {mutate.isPending
              ? t('action.fetching')
              : t('action.fetchLatestPosts')}
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              exportCsv.mutate({
                q: contentQuery,
                platform: platform,
              });
            }}
            disabled={exportCsv.isPending}
          >
            {exportCsv.isPending ? 'exporting...' : 'export to csv'}
          </Button>
        </div>
      </div>
      {isLoading ? (
        <div className="flex justify-center items-center h-full">
          <Loader2 className="h-4 w-4 animate-spin" />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={data?.data ?? []}
          pageCount={totalPages}
          pagination={pagination}
          onPaginationChange={handlePaginationChange}
          isLoading={isFetching}
        />
      )}
      <Dialog
        open={isReplyDialogOpen}
        onOpenChange={(open) => {
          setIsReplyDialogOpen(open);
          if (!open) {
            setReplyingToComment(null);
          }
        }}
      >
        <DialogContent>
          {replyingToComment && (
            <>
              <DialogHeader>
                <DialogTitle>
                  {t('action.dialog.replyHeader', {
                    authorName: replyingToComment.authorName,
                  })}
                </DialogTitle>
                <DialogDescription>
                  <span>{replyingToComment.content}</span>
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmitReply)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="replyText"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder={t('action.replyPlaceholder')}
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit" disabled={replyToComment.isPending}>
                      {replyToComment.isPending
                        ? t('action.submitting')
                        : t('action.dialog.confirm')}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CommentsClient;
