'use client';

import * as React from 'react';
import {
  ChevronsUpDown,
  LayoutDashboard,
  MessageCircle,
  BotMessageSquare,
  Loader2,
  Newspaper,
} from 'lucide-react';
import Image from 'next/image';

import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarMenuSub,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { env } from '@/env';
import { useLocale } from 'next-intl';
import Link from 'next/link';
import { SidebarMenuSubButton } from './blocks/sidebar';
import { trpc } from '@/trpc/client';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/hooks/store/auth';
export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const locale = useLocale();
  const { user } = useAuthStore();
  const prefixPath = locale === 'en' ? '' : `/${locale}`;
  const { data: chats, isLoading: chatsIsLoading } =
    trpc.chat.getChats.useQuery();
  const projectName = env.NEXT_PUBLIC_SITE_NAME;
  const navMain = [
    {
      title: 'Dashboard',
      url: `${prefixPath}/dashboard`,
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: 'Posts',
      url: `${prefixPath}/dashboard/posts`,
      icon: Newspaper,
      isActive: false,
    },
    {
      title: 'Comments',
      url: `${prefixPath}/dashboard/comments`,
      icon: MessageCircle,
      isActive: false,
    },
  ];

  const chatMenu = {
    title: 'Chats',
    url: `${prefixPath}/dashboard/chat`,
    icon: BotMessageSquare,
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-primary-foreground">
            <Image src="/icon.png" alt="Commentify" width={32} height={32} />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{projectName}</span>
          </div>
          <ChevronsUpDown className="ml-auto" />
        </SidebarMenuButton>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Dashboard</SidebarGroupLabel>
          <SidebarMenu>
            {navMain.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton tooltip={item.title} asChild>
                  <Link href={item.url}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>Chats</SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton tooltip={chatMenu.title} asChild>
                <Link href={chatMenu.url}>
                  {chatMenu.icon && <chatMenu.icon />}
                  <span>{chatMenu.title}</span>
                </Link>
              </SidebarMenuButton>
              {chatsIsLoading ? (
                <SidebarMenuSub>
                  <div className="flex items-center justify-center">
                    <Loader2 className="animate-spin" />
                  </div>
                </SidebarMenuSub>
              ) : (
                <TooltipProvider delayDuration={0}>
                  {chats?.map((chat) => {
                    return (
                      <SidebarMenuSub key={chat.id}>
                        <SidebarMenuSubItem>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <SidebarMenuSubButton asChild>
                                <Link
                                  href={`${chatMenu.url}/${chat.id}`}
                                  className={cn(
                                    pathname.endsWith(chat.id) &&
                                      'bg-sidebar-accent text-sidebar-accent-foreground',
                                  )}
                                >
                                  <span className="line-clamp-1">
                                    {chat.title}
                                  </span>
                                </Link>
                              </SidebarMenuSubButton>
                            </TooltipTrigger>
                            <TooltipContent side="right" align="start">
                              <p>{chat.title}</p>
                            </TooltipContent>
                          </Tooltip>
                        </SidebarMenuSubItem>
                      </SidebarMenuSub>
                    );
                  })}
                </TooltipProvider>
              )}
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser
          user={{
            email: user?.email ?? '',
            name: '-',
            avatar: user?.avatar ?? '',
          }}
        />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
