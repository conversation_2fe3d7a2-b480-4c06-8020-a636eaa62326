import { BlogList } from '@/modules/contents/web.interface';
import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function LandingBlog({
  title,
  desc,
  blogList,
}: {
  title: string;
  desc: string;
  blogList?: BlogList[];
}) {
  const router = useRouter();
  const [showAllBlog, setShowAllBlog] = useState(false);
  const showBlogList = showAllBlog ? blogList : blogList?.slice(0, 4);
  return (
    <div className="max-w-5xl mx-auto mb-16">
      <h2 className="text-2xl md:text-3xl font-bold text-center mb-2">
        {title}
      </h2>
      <p className="text-center text-gray-500 mb-6">{desc}</p>
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        {showBlogList?.map((item, index) => (
          <div
            className="bg-white rounded-xl shadow overflow-hidden flex flex-col cursor-pointer group"
            key={index}
            onClick={() => {
              router.push(`/blog/${item.title_id}`);
            }}
          >
            <Image
              src={item.cover_image}
              alt={item.title}
              width={300}
              height={140}
              loading="lazy"
              className="w-full h-[140px] object-cover"
            />
            <div className="p-3 flex-1 flex flex-col">
              <h3 className="font-semibold text-sm mb-8 line-clamp-3 group-hover:text-primary transition">
                {item.title}
              </h3>
              <div className="text-xs text-gray-400 mt-auto">{item.date}</div>
            </div>
          </div>
        ))}
      </div>
      {showBlogList?.length &&
        blogList?.length &&
        showBlogList.length < blogList.length && (
          <div className="flex justify-center">
            <button
              className=" mt-6 border border-gray-300 rounded py-2 text-gray-700 hover:bg-gray-100 transition flex items-center px-20"
              onClick={() => setShowAllBlog(!showAllBlog)}
            >
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Group 1272628363">
                  <rect
                    id="Rectangle 34628920"
                    y="5"
                    width="12"
                    height="2"
                    rx="1"
                    fill="#0C0C0C"
                  />
                  <rect
                    id="Rectangle 34628921"
                    x="7"
                    width="12"
                    height="2"
                    rx="1"
                    transform="rotate(90 7 0)"
                    fill="#0C0C0C"
                  />
                </g>
              </svg>

              <span className="ml-2 text-sm">Load More</span>
            </button>
          </div>
        )}
    </div>
  );
}
