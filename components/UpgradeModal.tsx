// components/UpgradeModal.tsx
'use client';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { trpc } from '@/trpc/client';
import { Loader2 } from 'lucide-react';
export default function UpgradeModal({
  open,
  onOpenChange,
  priceId,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  priceId: string;
}) {
  const t = useTranslations('Pricing');
  const upgradeSubscription = trpc.stripe.upgradeSubscription.useMutation({
    onSuccess: () => {
      toast.success('Upgrade successful');
      const timer = setTimeout(() => {
        onOpenChange(false);
        window.location.reload();
        clearTimeout(timer);
      }, 1000);
    },
  });
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-xs p-6">
        <DialogHeader>
          <DialogTitle className="text-center text-lg font-bold">
            {t('confirm_subscription_change')}
          </DialogTitle>
        </DialogHeader>
        <DialogDescription className="text-center mb-4">
          {t('confirm_subscription_change_desc')}
        </DialogDescription>
        <Button
          className="w-full bg-primary text-white font-semibold text-base py-2 mt-2"
          onClick={() => {
            if (upgradeSubscription.isPending) {
              return;
            }
            upgradeSubscription.mutate({
              priceId: priceId,
              locale: 'en',
            });
          }}
        >
          {upgradeSubscription.isPending ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            t('upgrade_and_pay')
          )}
        </Button>
      </DialogContent>
    </Dialog>
  );
}
