/* eslint-disable @next/next/no-img-element */
'use client';
import dynamic from 'next/dynamic';

import { generateUUID } from '@/modules/chat/utils';
import { trpc } from '@/trpc/client';
import { useChat } from '@ai-sdk/react';
import { Attachment, UIMessage } from 'ai';

import { ArrowLeftToLine, ArrowRightToLine, Plus } from 'lucide-react';
import { ChatBubbleMessage, ChatBubble } from '@/components/ui/chat-bubble';
import { ChatMessageList } from '@/components/ui/chat-message-list';
import { useEffect, useState, useCallback, useRef } from 'react';
import Image from 'next/image';
import TypewriterMarkdown from '@/components/markdown';
import { toast } from 'sonner';

import { useTranslations } from 'next-intl';
const DatabaseAssociation = dynamic(() => import('./DatabaseAssociation'), {
  ssr: false,
});

import ChatInput from './chatInput';
const AnalyzeCommentEmotion = dynamic(
  () => import('@/components/biz/ChatAnalytic/analyze_comment_emotions'),
  {
    ssr: false,
  },
);
const AnalyzeCommentEmotionOther = dynamic(
  () => import('@/components/biz/ChatAnalytic/analyze_comment_emotions_ohter'),
  {
    ssr: false,
  },
);

// import TopneGativePosts from '@/components/biz/ChatAnalytic/top_negative_posts';
import AnalyzeNegativeIssues from '@/components/biz/ChatAnalytic/analyze_negative_issues';
import { useAuthStore } from '@/hooks/store/auth';
import { redirect, useRouter } from 'next/navigation';
import { LoginUser } from '@/modules/contents/web.interface';
const NoCreditModal = dynamic(() => import('@/components/no-credit-modal'), {
  ssr: false,
});

export default function Chat({
  id,
  initialMessages,
  pageIdMap,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  pageIdMap?: Map<string, string>;
}) {
  const t = useTranslations('Chat');
  const router = useRouter();
  const { prompt, setPrompt, setUser } = useAuthStore();
  const utils = trpc.useUtils();
  const externalPageId = useRef<string>('');
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, []);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    stop,
    status,
    append,
  } = useChat({
    id,
    body: { id, externalPageId: externalPageId.current },
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      refreshUserInfo();
      utils.chat.getChats.invalidate();
      scrollToBottom();
    },
    onError: (error) => {
      if (error.message.includes('No credits')) {
        setNoCreditOpen(true);
        toast.error(error.message);
        // 打开弹窗
      } else if (error.message.includes('Unauthorized')) {
        redirect('/home?logout=true');
      }
    },
  });

  // 监听消息变化，自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const [attachments, setAttachments] = useState<Attachment[]>([]);

  const handlePrompt = useCallback(() => {
    if (prompt) {
      append({
        role: 'user',
        content: prompt,
      });
      setPrompt('');
    }
  }, [prompt, append, setPrompt]);

  useEffect(() => {
    handlePrompt();
  }, [handlePrompt]);
  //
  const [shouldFetch, setShouldFetch] = useState(true);
  const { data: currentUserInfo, isSuccess } =
    trpc.user.currentUserInfo.useQuery(undefined, {
      enabled: shouldFetch,
    });
  useEffect(() => {
    if (isSuccess) {
      const { amount, remainAmount } =
        currentUserInfo?.user as unknown as LoginUser;
      const isFirst = localStorage.getItem('isFirst');

      // 检查剩余额度
      if (remainAmount / amount < 0.1 && !isFirst) {
        setNoCreditOpen(true);
      }

      setUser(currentUserInfo?.user as unknown as LoginUser);
      setShouldFetch(false); // 查询成功后禁用查询

      // 首次访问时设置标记
      if (!isFirst) {
        localStorage.setItem('isFirst', '11');
      }
    }
  }, [isSuccess, currentUserInfo]);
  // 获取账号信息
  const { data: AccountList } = trpc.socialPage.getPages.useQuery(
    {
      externalPageId: undefined,
    },
    {
      refetchOnWindowFocus: false,
    },
  );
  // 弹出框
  const [isSettingsOpen, setIsSettingsOpen] = useState(true);
  // 升级弹窗
  const [noCreditOpen, setNoCreditOpen] = useState(false);

  const refreshUserInfo = useCallback(() => {
    setShouldFetch(true);
  }, []);

  return (
    <div className="h-full bg-background rounded-lg flex flex-col flex-shrink-0">
      <div className="flex h-full">
        <div className="h-full flex-1 relative">
          <div className="gap-2 flex items-center justify-between h-fit p-4 ">
            <div className="flex items-center">
              <Image
                src="/icon-ai.webp"
                alt="AI assistant"
                width={32}
                height={32}
              />
              <span>AI assistant</span>
            </div>
            <div>
              {!isSettingsOpen ? (
                <ArrowLeftToLine
                  className="size-5 hover:text-primary cursor-pointer"
                  onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                />
              ) : null}
            </div>
          </div>
          <div
            ref={chatContainerRef}
            id="scroll-row-chat"
            className="flex-1 rounded flex-shrink-0 mb-[200px] overflow-y-auto h-[calc(100%-250px)] [&::-webkit-scrollbar]:hidden"
          >
            <ChatMessageList>
              {messages.map((message, index) => {
                const isLast = index === messages.length - 1;
                const showLoading =
                  isLast &&
                  message.role === 'assistant' &&
                  (status === 'submitted' || status === 'streaming');
                return (
                  <div key={message.id}>
                    {/* head */}
                    <ChatBubble
                      variant={message.role === 'user' ? 'sent' : 'received'}
                      chatStatus={showLoading ? status : 'ready'}
                      isCopy={false}
                      message={message}
                      pageIdMap={pageIdMap}
                      AccountList={AccountList}
                    >
                      <ChatBubbleMessage variant="sent" chatStatus={status}>
                        {message.parts.map((part, index) => {
                          if (part.type === 'text' && message.role === 'user') {
                            return (
                              <TypewriterMarkdown key={index}>
                                {part.text}
                              </TypewriterMarkdown>
                            );
                          }
                          return null;
                        })}
                      </ChatBubbleMessage>
                    </ChatBubble>
                    {/* body 非最后一个 */}
                    {!isLast && (
                      <ChatBubble
                        isCopy={true}
                        key={message.id + index}
                        variant={message.role === 'user' ? 'sent' : 'received'}
                        pageIdMap={pageIdMap}
                        AccountList={AccountList}
                      >
                        <ChatBubbleMessage
                          isCopy={true}
                          variant={
                            message.role === 'user' ? 'sent' : 'received'
                          }
                          chatStatus={status}
                        >
                          {message.parts.map((part, index) => {
                            const { type } = part;
                            if (type === 'reasoning') {
                              // Display reasoning content if needed
                              const { reasoning } = part;
                              // For now, we're not displaying the reasoning in the UI
                              // Uncomment the following lines to display reasoning if needed
                              return (
                                <div
                                  key={index}
                                  className="bg-gray-50 p-2 rounded-md my-2 text-sm"
                                >
                                  <div className="font-medium mb-1">
                                    AI Reasoning:
                                  </div>
                                  <TypewriterMarkdown>
                                    {reasoning}
                                  </TypewriterMarkdown>
                                </div>
                              );
                            } else if (
                              type === 'text' &&
                              message.role === 'assistant'
                            ) {
                              return (
                                <TypewriterMarkdown key={index}>
                                  {part.text}
                                </TypewriterMarkdown>
                              );
                            } else if (type === 'tool-invocation') {
                              const { toolInvocation } = part;
                              const { toolName, toolCallId, state } =
                                toolInvocation;

                              if (state === 'result') {
                                const { result } = toolInvocation; // 工具返回结果
                                return (
                                  <div key={toolCallId}>
                                    {toolName == 'histogram_period' &&
                                    result.content.length > 0 ? (
                                      <AnalyzeCommentEmotion result={result} />
                                    ) : toolName == 'bubble_chart' &&
                                      result.content.length > 0 ? (
                                      <AnalyzeNegativeIssues result={result} />
                                    ) : toolName == 'pie_chart' &&
                                      result.content.length > 0 ? (
                                      <AnalyzeCommentEmotionOther
                                        result={result}
                                      />
                                    ) : (
                                      <></>
                                    )}
                                  </div>
                                );
                              }
                            }
                          })}
                        </ChatBubbleMessage>
                      </ChatBubble>
                    )}

                    {/* body 最后一个 */}
                    {isLast && (
                      <ChatBubble
                        isCopy={true}
                        key={message.id + index + '1'}
                        variant={message.role === 'user' ? 'sent' : 'received'}
                        pageIdMap={pageIdMap}
                        AccountList={AccountList}
                      >
                        <ChatBubbleMessage
                          isCopy={true}
                          variant={
                            message.role === 'user' ? 'sent' : 'received'
                          }
                          chatStatus={status}
                        >
                          {message.parts.map((part, index) => {
                            const { type } = part;
                            if (type === 'reasoning') {
                            } else if (
                              type === 'text' &&
                              message.role === 'assistant'
                            ) {
                              return (
                                <TypewriterMarkdown key={index}>
                                  {part.text}
                                </TypewriterMarkdown>
                              );
                            } else if (type === 'tool-invocation') {
                              const { toolInvocation } = part;
                              const { toolName, toolCallId, state } =
                                toolInvocation;

                              if (state === 'result') {
                                const { result } = toolInvocation; // 工具返回结果
                                return (
                                  <div key={toolCallId}>
                                    {toolName == 'histogram_period' &&
                                    result.content.length > 0 ? (
                                      <AnalyzeCommentEmotion result={result} />
                                    ) : toolName == 'bubble_chart' &&
                                      result.content.length > 0 ? (
                                      <AnalyzeNegativeIssues result={result} />
                                    ) : toolName == 'pie_chart' &&
                                      result.content.length > 0 ? (
                                      <AnalyzeCommentEmotionOther
                                        result={result}
                                      />
                                    ) : (
                                      <></>
                                    )}
                                  </div>
                                );
                              }
                            }
                          })}
                        </ChatBubbleMessage>
                      </ChatBubble>
                    )}
                  </div>
                );
              })}

              {status === 'submitted' && (
                <ChatBubble variant="received">
                  <ChatBubbleMessage variant="received" chatStatus={status}>
                    <div className="flex items-center gap-2">
                      <div className="animate-bounce">•</div>
                      <div className="animate-bounce [animation-delay:0.2s]">
                        •
                      </div>
                      <div className="animate-bounce [animation-delay:0.4s]">
                        •
                      </div>
                    </div>
                  </ChatBubbleMessage>
                </ChatBubble>
              )}
            </ChatMessageList>
          </div>
          <div className="h-fit absolute bottom-0 left-0 right-0">
            <form
              onSubmit={(event) => {
                handleSubmit(event, {
                  experimental_attachments: attachments,
                });
                setAttachments([]);
              }}
              className="relative rounded-lg"
            >
              <ChatInput
                value={input}
                isChat={true}
                onChange={handleInputChange}
                status={status}
                stop={stop}
              />
            </form>
          </div>
        </div>
        <div
          className={`
            shadow-[-4px_0_6px_-1px_rgba(0,0,0,0.1)] 
            transition-all 
            duration-100 
            ease-in-out 
            overflow-hidden
            ${isSettingsOpen ? 'w-[300px]' : 'w-0'}
          `}
        >
          <div
            className="w-[300px] flex-shrink-0 p-2 flex justify-end"
            onClick={() => setIsSettingsOpen(!isSettingsOpen)}
          >
            <div className="flex items-center gap-1 w-fit bg-[#E6E6F8] rounded-md px-[8px]">
              <span className="text-sm text-muted-foreground hover:text-primary cursor-pointer">
                Hide
              </span>
              <ArrowRightToLine className="size-3 hover:text-primary cursor-pointer" />
            </div>
          </div>
          <div className="px-4 py-2">
            {AccountList?.length && AccountList?.length > 0 ? (
              <DatabaseAssociation
                AccountList={AccountList}
                append={append}
                callbackSelect={(id: string) => {
                  externalPageId.current = id;
                }}
              />
            ) : (
              <div>
                <div className="text-lg font-bold px-4">
                  {t('connect_socail_channel')}
                </div>
                <div className="p-4 text-white">
                  <div className="w-full rounded-md aspect-square bg-[url('/images/chat/bg-line.webp')] bg-inherit bg-center relative p-4">
                    <div className="text-base font-bold w-[150px] mt-4">
                      {t('connect_socail_channel')}
                    </div>
                    <div className="w-[150px] text-sm mt-4">
                      {t(
                        'link_your_account_to_enable_automatic_comment_moderation',
                      )}
                    </div>
                    <div
                      className="flex items-center gap-2 w-full justify-center mt-4 bg-white rounded-md p-2 text-black text-sm hover:bg-gray-200 cursor-pointer hover:text-primary"
                      onClick={() => router.push('/dashboard/social-channel')}
                    >
                      <Plus className="size-4" />
                      <span>{t('connect_now')}</span>
                    </div>
                    <Image
                      src="/images/chat/bg-right-top.webp"
                      alt="Line"
                      width={100}
                      height={100}
                      className="absolute -top-8 right-0"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* 新增积分 */}
      <NoCreditModal open={noCreditOpen} onOpenChange={setNoCreditOpen} />
    </div>
  );
}
