'use client';

import * as React from 'react';
import { format, subMonths } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface DatePickerWithRangeProps {
  onChange: (date: DateRange | undefined) => void;
}

export function DatePickerWithRange({ onChange }: DatePickerWithRangeProps) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: subMonths(new Date(), 1),
    to: new Date(),
  });
  const [open, setOpen] = React.useState(false);

  return (
    <div className={cn('grid gap-2')}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'w-[300px] justify-start text-left font-normal',
              !date && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, 'LLL dd, y')} -{' '}
                  {format(date.to, 'LLL dd, y')}
                </>
              ) : (
                format(date.from, 'LLL dd, y')
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(value) => {
              setDate(value);
              if (value?.from && value?.to) {
                setOpen(false);
                onChange(value);
              }
            }}
            numberOfMonths={2}
          />
          <div className="flex gap-2 p-3 border-t mt-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                const today = new Date();
                const range = { from: today, to: today };
                setDate(range);
                setOpen(false);
                onChange(range);
              }}
            >
              today
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                const to = new Date();
                const from = new Date();
                from.setDate(to.getDate() - 6); // 包含今天共7天
                const range = { from, to };
                setDate(range);
                setOpen(false);
                onChange(range);
              }}
            >
              last week
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                const to = new Date();
                const from = new Date();
                from.setMonth(to.getMonth() - 1);
                const range = { from, to };
                setDate(range);
                setOpen(false);
                onChange(range);
              }}
            >
              last month
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setDate(undefined);
                setOpen(false);
                onChange(undefined);
              }}
            >
              all
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
