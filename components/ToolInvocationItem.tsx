import { useEffect, useRef } from 'react';
import { Check, Loader, ChevronDown, ChevronUp } from 'lucide-react';

interface ToolInvocationWithResult {
  toolCallId: string;
  toolName: string;
  state: string;
  result?: unknown;
}

const nameMap = {
  histogram_period: 'Aggregation',
  bubble_chart: 'Analyze',
  pie_chart: 'Understand data',
  list_social_posts: 'List social posts',
};

export default function ToolInvocationItem({
  toolInvocation,
  showLine = true,
  isCollapsed,
  onToggle,
}: {
  toolInvocation: ToolInvocationWithResult;
  showLine?: boolean;
  isCollapsed: boolean;
  onToggle: () => void;
}) {
  const prevStateRef = useRef<string>(toolInvocation.state);

  useEffect(() => {
    if (prevStateRef.current === 'call' && toolInvocation.state === 'result') {
      onToggle();
    }
    if (prevStateRef.current === 'result' && toolInvocation.state === 'call') {
      onToggle();
    }
    prevStateRef.current = toolInvocation.state;
  }, [toolInvocation.state, onToggle]);

  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative flex">
      {/* 虚线装饰条 */}
      {showLine && (
        <div
          className="
            absolute left-8 -top-2 bottom-0
            w-4 flex justify-center
          "
        >
          <div className="h-full border-l-[1px] border-dashed border-[#D1D5DB]"></div>
        </div>
      )}
      <div
        className={`
          bg-[#F7F8FA] text-primary z-[1] p-2 mb-[20px] overflow-hidden rounded-md
          transition-all duration-300 ease-in-out
          ${isCollapsed ? 'max-h-[38px]' : 'max-h-[500px]'}
          ml-4 flex-1
        `}
      >
        <div className="flex items-center gap-2 justify-between">
          <div className="flex items-center gap-2">
            <Check className="text-white bg-primary rounded-full size-3 transition-transform duration-1000" />
            <span className="text-base transition-opacity duration-300">
              {nameMap[toolInvocation.toolName as keyof typeof nameMap]}
            </span>
          </div>
          <div className="flex items-center gap-1">
            {toolInvocation.state === 'call' && (
              <Loader className="text-primary animate-spin size-4 transition-opacity duration-1000" />
            )}
            {toolInvocation.state === 'result' && (
              <button
                type="button"
                className="ml-2 text-xs text-primary hover:underline flex items-center transition-all duration-1000"
                onClick={onToggle}
              >
                {isCollapsed ? (
                  <ChevronDown className="w-4 h-4 ml-1 transition-transform duration-2000" />
                ) : (
                  <ChevronUp className="w-4 h-4 ml-1 transition-transform duration-2000" />
                )}
              </button>
            )}
          </div>
        </div>
        {/* 动画内容区域 */}
        <div
          ref={contentRef}
          className={`
            transition-all duration-300 ease-in-out
            ${isCollapsed ? 'opacity-0 translate-y-[-10px]' : 'opacity-100 translate-y-0'}
          `}
        >
          <div className="mt-2 break-all text-[13px] text-primary/90">
            {toolInvocation.state === 'call' && <span>Tool is running...</span>}
            {toolInvocation.state === 'result' &&
              (typeof toolInvocation.result === 'object' &&
              toolInvocation.result !== null ? (
                <pre className="whitespace-pre-wrap max-h-[200px] overflow-y-auto bg-black rounded-md p-2 text-white">
                  {JSON.stringify(toolInvocation.result, null, 2)}
                </pre>
              ) : (
                String(toolInvocation.result)
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
