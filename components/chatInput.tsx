'use client';

import { Send } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
export type ChatInputProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>;
interface Iprops {
  status?: string;
  stop?: () => void;
  isLoginStatus?: boolean;
  openModal?: () => void;
  isChat?: boolean;
}
import { useAuthStore } from '@/hooks/store/auth';

const ChatInput = React.forwardRef<
  HTMLTextAreaElement,
  ChatInputProps & Iprops
>(({ status, stop, isLoginStatus, openModal, isChat, ...props }, ref) => {
  const t = useTranslations('Chat');
  const router = useRouter();
  const pathname = usePathname();
  const isChatPage = pathname.includes('/dashboard/chat');
  const { setPrompt } = useAuthStore();

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      if (!isLoginStatus) {
        openModal?.();
      } else {
        if (!isChatPage) {
          router.push('/dashboard/chat?type=1');
          return;
        }
      }
      e.preventDefault();
      const form = e.currentTarget.form;
      form?.dispatchEvent(
        new Event('submit', { cancelable: true, bubbles: true }),
      );
    }
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow-lg border border-[#F0F1F3] focus-within:border-primary transition-colors">
        <div className="relative">
          <textarea
            ref={ref}
            onChange={(e) => {
              if (isChatPage) return;
              setPrompt(e.target.value);
            }}
            placeholder={t('ask_something_with_ai')}
            className={`w-full border-none rounded-md p-4 focus:outline-none focus:ring-none resize-none ${!isChat ? 'h-12' : 'h-20'}`}
            {...props}
            onKeyDown={handleKeyDown}
          />
          {!isChat && (
            <div className="absolute bottom-0 right-0 p-2">
              <button
                className="bg-primary text-white rounded-md px-4 py-2 flex items-center gap-2 hover:bg-indigo-700 transition-all"
                onClick={() => {
                  router.push('/dashboard/chat?type=1');
                }}
              >
                {t('start_a_new_chat')} <Send className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
        {isChat && (
          <div className="flex flex-row-reverse items-center py-2 px-4">
            <div>
              <button
                className={`bg-primary text-white rounded-md px-4 py-2 flex items-center gap-2 hover:bg-indigo-700 transition-all ${
                  status === 'streaming' ||
                  (status === 'submitted' && 'bg-primary/50')
                }`}
              >
                {status === 'streaming' || status === 'submitted' ? (
                  <span
                    className="flex items-center gap-2 text-base font-medium"
                    onClick={stop}
                  >
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 14 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7 0C10.866 0 14 3.13401 14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7C0 3.13401 3.13401 0 7 0ZM7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1ZM8.5 4.5C9.05228 4.5 9.5 4.94772 9.5 5.5V8.5C9.5 9.05228 9.05228 9.5 8.5 9.5H5.5C4.94772 9.5 4.5 9.05228 4.5 8.5V5.5C4.5 4.94772 4.94772 4.5 5.5 4.5H8.5Z"
                        fill="white"
                      />
                    </svg>

                    <span>Analyzing...</span>
                  </span>
                ) : (
                  <span
                    className="flex items-center gap-2 text-base font-medium"
                    onClick={() => {
                      if (!isLoginStatus) {
                        openModal?.();
                      } else {
                        if (!isChatPage) {
                          router.push('/dashboard/chat?type=1');
                        }
                      }
                    }}
                  >
                    {t('start_a_new_chat')} <Send className="h-4 w-4" />
                  </span>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

ChatInput.displayName = 'ChatInput';

export default ChatInput;
