import { useEffect, useState } from 'react';
import ToolInvocationItem from '@/components/ToolInvocationItem';

interface AnalysisStepsCollapseProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parts: any[];
  chatStatus: string;
  setCollapsed: (value: boolean) => void;
  collapsed: boolean;
}

export default function AnalysisStepsCollapse({
  parts,
  chatStatus,
  setCollapsed,
  collapsed,
}: AnalysisStepsCollapseProps) {
  const toolParts = parts.filter((part) => part.type === 'tool-invocation');
  const allFinished =
    toolParts.length > 0 &&
    toolParts.every((part) => part.toolInvocation.state === 'result');

  const [expandedItemId, setExpandedItemId] = useState<string | null>(null);

  useEffect(() => {
    const callingTool = toolParts.find(
      (part) => part.toolInvocation.state === 'call',
    );
    if (callingTool) {
      setExpandedItemId(callingTool.toolInvocation.toolCallId);
    }
  }, [toolParts]);

  useEffect(() => {
    if (chatStatus === 'ready' && allFinished) {
      setCollapsed(true);
      setExpandedItemId(null);
    }
  }, [chatStatus, allFinished, setCollapsed]);

  if (toolParts.length === 0) return null;

  // 动画容器
  return (
    <div
      className={`
        transition-all duration-300 ease-in-out overflow-hidden mt-4
        ${collapsed ? 'max-h-0 opacity-0' : 'max-h-[800px] opacity-100'}
      `}
    >
      {toolParts.map((part, idx) => (
        <ToolInvocationItem
          key={part.toolInvocation.toolCallId}
          toolInvocation={part.toolInvocation}
          showLine={idx !== toolParts.length - 1}
          isCollapsed={expandedItemId !== part.toolInvocation.toolCallId}
          onToggle={() => {
            setExpandedItemId(
              expandedItemId === part.toolInvocation.toolCallId
                ? null
                : part.toolInvocation.toolCallId,
            );
          }}
        />
      ))}
    </div>
  );
}
