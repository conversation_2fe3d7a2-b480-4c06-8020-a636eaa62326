'use client';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import CommentsModel from './biz/comments-model';
import RulePage from './biz/rule-page/page';
import { trpc } from '@/trpc/client';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { Account } from '@/modules/contents/web.interface';
import { useTranslations } from 'next-intl';
import ChatInput from '@/components/chatInput';

export default function SocialChannelPage() {
  const t = useTranslations('Comments');
  const searchParamsPath = useSearchParams();
  const externalPageId = searchParamsPath.get('externalPageId');
  const type = searchParamsPath.get('type');
  const [activeTab, setActiveTab] = useState(type ?? 'comments');
  const { data: AccountList } = trpc.socialPage.getPages.useQuery(
    {
      externalPageId: undefined,
    },
    {
      refetchOnWindowFocus: false,
    },
  );
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);

  useEffect(() => {
    if (externalPageId) {
      const account = AccountList?.find(
        (item) => item.externalPageId === externalPageId,
      );
      if (account) {
        setSelectedAccount(account);
      }
    }
  }, [AccountList, externalPageId]);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4 flex gap-2 items-center">
        <Link href="/dashboard/bind-account">{t('user_comments')}</Link>
        {selectedAccount && (
          <div className="flex items-center gap-2">
            <span>{'>'}</span>
            <span className="text-xl vertical-middle font-medium inline-block pt-1">
              {selectedAccount?.pageName}
            </span>
          </div>
        )}
      </h1>
      <div className="bg-white rounded-lg shadow h-[calc(100vh-80px)]">
        <div className="p-6 pb-0 pt-2 border-b border-gray-200">
          <Tabs
            defaultValue={type ?? 'comments'}
            className="w-full"
            value={activeTab}
            onValueChange={(value) => {
              if (value == 'moderation' && AccountList?.length === 0) {
                toast.error(t('please_bind_your_account_first'));
                return;
              }
              setActiveTab(value);
            }}
          >
            <TabsList className="bg-transparent  gap-6">
              <TabsTrigger
                value="comments"
                className="font-bold text-base py-[6px] data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none data-[state=active]:text-primary rounded-none bg-transparent"
              >
                {t('comments')}
              </TabsTrigger>
              <TabsTrigger
                value="moderation"
                className="font-bold text-base py-[6px] data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none data-[state=active]:text-primary rounded-none bg-transparent"
              >
                {t('moderation')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {
          <React.Fragment>
            {activeTab === 'comments' ? (
              <CommentsModel
                AccountList={AccountList}
                setSelectedAccountOut={setSelectedAccount}
                selectedAccountOut={selectedAccount}
              />
            ) : (
              <RulePage
                setSelectedAccountOut={setSelectedAccount}
                AccountList={AccountList}
                selectedAccountOut={selectedAccount}
                activeTab={activeTab}
              />
            )}
          </React.Fragment>
        }
        <div className="sticky right-0 bottom-[20px] max-w-[900px] mx-auto">
          <ChatInput isLoginStatus={true} />
        </div>
      </div>
    </div>
  );
}
