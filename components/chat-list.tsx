'use client';
import { trpc } from '@/trpc/client';
import {
  Too<PERSON>ip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { BotMessageSquare, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SidebarMenuSubButton } from './blocks/sidebar';
import { usePathname, useParams } from 'next/navigation';
import { useLocale } from 'next-intl';
import { redirect } from 'next/navigation';

export default function ChatList() {
  const params = useParams();
  const chatId = params.id; // 获取路径中的 id 参数

  const pathname = usePathname();

  const locale = useLocale();
  const { data: chats, isLoading: chatsIsLoading } =
    trpc.chat.getChats.useQuery();

  const prefixPath = locale === 'en' ? '' : `/${locale}`;
  const chatMenu = {
    title: 'Chats',
    url: `${prefixPath}/dashboard/chat`,
    icon: BotMessageSquare,
  };
  return (
    <div className="flex flex-col gap-2">
      {chatsIsLoading ? (
        <div className="flex items-center justify-center">
          <Loader2 className="animate-spin" />
        </div>
      ) : (
        <TooltipProvider delayDuration={0}>
          {chats?.length === 0 && (
            <div className="flex items-center justify-center mt-4">
              <p>No chats found</p>
            </div>
          )}
          {chats?.map((chat) => {
            return (
              <div key={chat.id}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SidebarMenuSubButton asChild>
                      <div
                        onClick={() => {
                          redirect(`${chatMenu.url}/${chat.id}`);
                        }}
                        className={cn(
                          pathname.endsWith(chat.id) &&
                            'bg-sidebar-accent text-sidebar-accent-foreground',
                          'cursor-pointer',
                        )}
                      >
                        <span
                          className={`truncate pl-[12px] inline-block w-[200px] hover:text-primary ${chatId == chat.id && 'text-primary'}`}
                        >
                          {chat.title}
                        </span>
                      </div>
                    </SidebarMenuSubButton>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    align="start"
                    className="bg-white text-black shadow"
                  >
                    <p>{chat.title}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            );
          })}
        </TooltipProvider>
      )}
    </div>
  );
}
