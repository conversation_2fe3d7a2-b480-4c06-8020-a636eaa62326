Commentify.ai Data Deletion Instructions

App Name: Commentify.ai Developer: Commentify Team Contact: <EMAIL>

## **Scope of User Data Collection**

In compliance with our Privacy Policy, this application may collect the following Facebook-related data:

- Account Information: Public profile data (name, email, etc.) when users log in via Facebook
- Interaction Data: Records of comment management activities performed through our app on Facebook
- Technical Data: IP addresses, device information, and other log data from Facebook page visits

**Data Deletion Process**

Self-Service Deletion:

1. Log in to your Commentify.ai account → Navigate to "Settings > Data Management"
1. Click the "Clear Facebook Data" button
1. The system will automatically remove:

- Stored Facebook account linkage information
- Local database records of comment operations
- Related behavioral data from analytics logs

Admin-Assisted Deletion:

Users may submit deletion <NAME_EMAIL> with:

- The email address associated with their Facebook account
- Specific description of data types to be deleted We will:
- Verify the request within ​72 hours
- Process the deletion upon confirmation
- Send email confirmation upon completion

3\.Compliance with Facebook Policies

We affirm that: ✓ We do not store unnecessary Facebook data (e.g., friend lists) ✓ All deletions trigger notifications to Facebook API where applicable ✓ We adhere to Section 4 of Facebook's ​Developer Policy regarding data usage

Supplemental Statement: These instructions complement the Commentify.ai Privacy Policy. Third-party auditors have verified the above processes (where applicable).
