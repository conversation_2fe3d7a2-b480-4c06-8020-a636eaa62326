'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { InputWithIcon } from '@/components/ui/input-with-icon';
import { Label } from '@/components/ui/label';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { Lock, Mail, Loader2 } from 'lucide-react';
import { z } from 'zod';
import { loginWithGoogle } from '@/modules/auth/supabase';
import { useAuthStore } from '@/hooks/store/auth';
import { toast } from 'sonner';
import { LoginUser } from '@/modules/contents/web.interface';
import { trpc } from '@/trpc/client';
import { useRouter } from 'next/navigation';
interface SignUpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function SignUpDialog({
  open,
  onOpenChange,
}: SignUpDialogProps) {
  const t = useTranslations('Auth');
  const [isLogin, setIsLogin] = useState(true);
  const [isAgree, setIsAgree] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const { setIsLoginMode, setIsLoginStatus, setUser, setHasPlatform } =
    useAuthStore();
  const userSignup = trpc.user.userSignup.useMutation({
    onSuccess: (result) => {
      if (result.status === 'ok') {
        setIsLoading(false);
        setIsLoginMode(false);
        setIsLoginStatus(true);
        setUser(result.bizUser as unknown as LoginUser);
        setHasPlatform(false);
        toast.success(t('signupSuccess'));
        router.push('/dashboard/social-channel');
      } else {
        setIsLoading(false);
        toast.error(t('signupFailed'));
      }
    },
    onError: () => {
      setIsLoading(false);
      toast.error(t('signupFailed'));
    },
  });
  const userLogin = trpc.user.userLogin.useMutation({
    onSuccess: (result) => {
      if (result.status === 'ok') {
        setIsLoginMode(false);
        setIsLoading(false);
        setIsLoginStatus(true);
        setUser(result.bizUser as unknown as LoginUser);
        setHasPlatform((result?.platforms?.length ?? 0) > 0);
        toast.success(t('loginSuccess'));
        if ((result?.platforms?.length ?? 0) > 0) {
          router.push('/dashboard');
        } else {
          router.push('/dashboard/social-channel');
        }
      } else {
        setIsLoading(false);
        toast.error(t('loginFailed'));
      }
    },
    onError: () => {
      setIsLoading(false);
      toast.error(t('loginFailed'));
    },
  });
  const signUpSchema = z
    .object({
      email: z.string().email(t('invalidEmail')),
      password: z.string().min(6, t('passwordTooShort')),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('passwordsDontMatch'),
      path: ['confirmPassword'],
    });

  const loginSchema = z.object({
    email: z.string().email(t('invalidEmail')),
    password: z.string().min(1, t('passwordRequired')),
  });

  const validateForm = () => {
    try {
      if (isLogin) {
        loginSchema.parse({ email, password });
      } else {
        signUpSchema.parse({ email, password, confirmPassword });
      }
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = () => {
    if (!isAgree || !validateForm()) return;
    setIsLoading(true);
    if (isLogin) {
      userLogin.mutate({ email, password });
      // 这边做跳转或者关闭
    } else {
      userSignup.mutate({ email, password });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold">
            {isLogin ? t('login') : t('signUp')}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="email">{t('email')}</Label>
            <InputWithIcon
              id="email"
              type="email"
              placeholder={t('emailPlaceholder')}
              prefix={<Mail className="w-4 h-4" />}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">{t('password')}</Label>
            <InputWithIcon
              id="password"
              type="password"
              placeholder={t('passwordPlaceholder')}
              prefix={<Lock className="w-4 h-4" />}
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
              }}
              onKeyUp={(e) => {
                if (e.key === 'Enter') {
                  handleSubmit();
                }
              }}
            />
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password}</p>
            )}
          </div>
          {!isLogin && (
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">{t('confirmPassword')}</Label>
              <InputWithIcon
                id="confirmPassword"
                type="password"
                placeholder={t('confirmPasswordPlaceholder')}
                prefix={<Lock className="w-4 h-4" />}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                onKeyUp={(e) => {
                  if (e.key === 'Enter') {
                    handleSubmit();
                  }
                }}
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-500">{errors.confirmPassword}</p>
              )}
            </div>
          )}
          <div className="text-sm text-muted-foreground text-center flex items-center justify-center">
            <input
              type="checkbox"
              checked={isAgree}
              onChange={(e) => setIsAgree(e.target.checked)}
              className="mr-1 h-4 w-4"
            />
            {t('iAgree')}{' '}
            <Link
              href="/terms-use"
              className="text-primary hover:underline text-[#715CFF] mx-1"
              onClick={() => {
                setIsLoginMode(false);
              }}
            >
              {t('terms')}
            </Link>{' '}
            {t('and')}{' '}
            <Link
              href="/privacy-policy"
              className="text-primary hover:underline text-[#715CFF] mx-1"
              onClick={() => {
                setIsLoginMode(false);
              }}
            >
              {t('privacyPolicy')}
            </Link>
          </div>
          <Button
            type="submit"
            className="w-full bg-[#715CFF] hover:bg-[#715CFF]/90"
            onClick={handleSubmit}
            disabled={!isAgree || isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            {isLogin ? t('login') : t('continue')}
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                {t('orCreateWith')}
              </span>
            </div>
          </div>
          <Button
            variant="outline"
            className="w-full"
            onClick={loginWithGoogle}
            disabled={isLoading}
          >
            <Image
              src="/google_icon.webp"
              alt="Google"
              width={20}
              height={20}
              className="mr-2"
              loading="lazy"
            />
            {t('loginWithGoogle')}
          </Button>
          <div className="text-center text-sm text-muted-foreground">
            {t('alreadyHaveAccount')}{' '}
            <button
              onClick={() => {
                setIsLogin(!isLogin);
              }}
              className="text-primary hover:underline"
            >
              {isLogin ? t('signUp') : t('login')}
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
