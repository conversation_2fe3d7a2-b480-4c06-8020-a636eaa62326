import { Check } from 'lucide-react';

interface PricingCardProps {
  name: string;
  price: string;
  description: string;
  features: string[];
  isPopular?: boolean;
  onSelect: () => void;
}

export function PricingCard({
  name,
  price,
  description,
  features,
  isPopular = false,
}: PricingCardProps) {
  return (
    <div
      className={`rounded-2xl border p-8 shadow-sm ${isPopular ? 'border-blue-600 bg-blue-50' : 'border-gray-200 bg-white'}`}
    >
      {isPopular && (
        <span className="inline-block rounded-full bg-blue-600 px-3 py-1 text-xs font-semibold text-white">
          Popular
        </span>
      )}
      <h3 className="mt-4 text-xl font-semibold text-gray-900">{name}</h3>
      <p className="mt-4">
        <span className="text-4xl font-bold text-gray-900">{price}</span>
        <span className="text-gray-500">/month</span>
      </p>
      <p className="mt-2 text-sm text-gray-500">{description}</p>
      <ul className="mt-8 space-y-3">
        {features.map((feature) => (
          <li key={feature} className="flex items-center gap-3">
            <Check className="h-5 w-5 text-blue-600" />
            <span className="text-sm text-gray-700">{feature}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
