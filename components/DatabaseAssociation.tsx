import { Account } from '@/modules/contents/web.interface';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Message, CreateMessage } from 'ai';

import { Ellipsis, File } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
export default function DatabaseAssociation({
  AccountList,
  append,
  callbackSelect,
}: {
  AccountList: Account[] | undefined;
  append: (value: Message | CreateMessage) => void;
  callbackSelect: (id: string) => void;
}) {
  const t = useTranslations('Chat');
  const searchParams = useSearchParams();
  const accountId = searchParams.get('id');
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);

  useEffect(() => {
    if (AccountList) {
      const account = AccountList.find((item) => item.id === accountId);
      if (account) {
        setSelectedAccount(account);
      }
    }
  }, [AccountList, accountId]);

  const Summary = useMemo(() => {
    if (!AccountList) return 0;
    if (selectedAccount?.id) return selectedAccount?.commentCount;
    let summary = 0;
    AccountList.forEach((item) => {
      summary += item.commentCount;
    });
    return summary;
  }, [AccountList, selectedAccount]);

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <File className="size-5" />
          <span className="text-base font-bold">
            {t('database_association')}
          </span>
        </div>
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger className="flex w-full items-center justify-between cursor-pointer p-2 hover:bg-accent hover:text-accent-foreground">
              <Ellipsis className="size-4 hover:text-primary cursor-pointer" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              {AccountList?.map((item) => (
                <DropdownMenuItem
                  key={item.id}
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={() => {
                    setSelectedAccount(item);
                    callbackSelect(item.externalPageId ?? '');
                  }}
                >
                  {item.avatarUrl && (
                    <Image
                      src={item.avatarUrl ?? ''}
                      width={16}
                      height={16}
                      alt="platform"
                    />
                  )}
                  <span>{item.pageName}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="mt-2 bg-[url('/images/chat/bg.webp')]  bg-cover bg-center pt-6">
        <p className="text-sm p-4 pb-0 pt-2">{t('data_table_name')}</p>
        {!selectedAccount?.id ? (
          <div className="flex items-center gap-2 p-2 text-sm pb-4">
            <Image
              src={'/images/dashboard/platform-all.svg'}
              width={20}
              height={20}
              alt="platform"
            />
            <span>{t('all_platforms')}</span>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-sm pl-4 mt-2 pb-4">
            <Image
              src={selectedAccount?.avatarUrl ?? ''}
              width={20}
              height={20}
              alt="platform"
              className="rounded-full"
            />
            <span>{selectedAccount?.pageName}</span>
            <span>{selectedAccount?.platform}</span>
          </div>
        )}
      </div>
      <div
        className="bg-black text-base  text-white py-2 mt-4 text-center rounded-sm hover:bg-gray-800 cursor-pointer"
        onClick={() => {
          append({
            role: 'user',
            content: `generate data report`,
          });
        }}
      >
        {t('generate_data_report')}
      </div>
      <Separator className="my-6" />
      <div className="flex items-center gap-2">
        <File className="size-5" />
        <span className="text-base font-bold">{t('data_summary')}</span>
      </div>
      <div className="text-sm  text-gray-500 mt-2 ">
        {t('data_summary_desc').replace('xxx', Summary.toString() ?? '0')}
      </div>
      <Separator className="my-6" />
      <div className="flex items-center gap-2 mb-8">
        <File className="size-5" />
        <span className="text-base font-bold">{t('exploration')}</span>
      </div>
      {[
        'What is the sentiment distribution of user comments?',
        "What are the primary reasons behind users' negative sentiment?",
        'Which post or advertisement has received the most negative feedback?',
      ].map((item, index) => (
        <div
          key={item}
          className="text-sm  text-gray-500 mt-2  hover:text-primary cursor-pointer"
          onClick={() => {
            append({
              role: 'user',
              content: item,
            });
          }}
        >
          <span>{item}</span>
          {index !== 2 && <Separator className="my-6" />}
        </div>
      ))}
    </div>
  );
}
