'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { trpc } from '@/trpc/client';
import { useAuthStore } from '@/hooks/store/auth';
import { useMemo } from 'react';
import { Separator } from '@/components/ui/separator';
import { useTranslations, useLocale } from 'next-intl';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface NoCreditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function NoCreditModal({
  open,
  onOpenChange,
}: NoCreditModalProps) {
  const locale = useLocale();
  const router = useRouter();
  const { user } = useAuthStore();
  const [data] = trpc.stripe.getProducts.useSuspenseQuery(undefined, {
    retry: 3,
  });
  const NextPlan = useMemo(() => {
    const curIndex = data.findIndex((item) => item.name === user?.currentPlan);
    return data[curIndex + 1];
  }, [data, user]);
  const t = useTranslations('Pricing');
  const upgradeSubscription = trpc.stripe.upgradeSubscription.useMutation({
    onSuccess: () => {
      toast.success('Upgrade successful');
      const timer = setTimeout(() => {
        onOpenChange(false);
        window.location.reload();
        clearTimeout(timer);
      }, 1000);
    },
  });
  const createCheckoutSession = trpc.stripe.createCheckoutSession.useMutation({
    onSuccess: (url) => {
      if (url) {
        toast.success('Upgrade successful');
        window.location.href = url;
      }
    },
  });
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-center text-base font-bold">
            Upgrade now to continue the current task
          </DialogTitle>
        </DialogHeader>

        <div className="mt-4 bg-[#FDEAC1] p-4 rounded-lg">
          <p className="text-sm">
            Upgrade your report immediately to unlock more
          </p>
          <div className="bg-white p-2 px-4 rounded-md mt-4">
            <div className="text-sm flex gap-2 items-center py-2 ">
              <div className="text-sm font-bold">Upgrade to</div>
              <div className="text-sm font-bold bg-[#2DA589] text-white px-4 py-[2px] rounded-full">
                {user?.currentPlan}
              </div>
            </div>
            <Separator className="my-3" />
            <ul className="space-y-3 mb-6">
              <li className="flex items-start">
                <span>
                  <span className="text-primary">{NextPlan?.credits}</span>{' '}
                  {t('creditMonth')}
                </span>
              </li>
              <li className="flex items-start">
                <span>{t('unlimitedPages')}</span>
              </li>
              <li className="flex items-start">
                <span>{t('unlimitedSentimentAnalysis')}</span>
              </li>
              <li className="flex items-start">
                <span>{t('automaticallyHideNegativeCommentsAndSpam')}</span>
              </li>
              <li className="flex items-start">
                <span>{t('aiPoweredDataAnalysisJobs')}</span>
              </li>
              <li className="flex items-start">
                <span>{t('commentManagement')}</span>
              </li>
              <li className="flex items-start">
                <span>{t('customizableModerationSettings')}</span>
              </li>
            </ul>
            <Separator className="my-3" />
            <div className="text-sm text-gray-500">Total amount due today</div>
            <div className="flex gap-2 items-center text-sm text-gray-500">
              <span className="text-black font-bold text-2xl">
                ${NextPlan?.f_value}
              </span>
              <span>/month</span>
              <span>billed monthly</span>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <Button
            className="w-full text-sm"
            onClick={() => {
              if (user?.currentPlan === 'Free') {
                createCheckoutSession.mutate({
                  priceId: NextPlan?.priceId,
                  locale,
                });
              } else {
                upgradeSubscription.mutate({
                  priceId: NextPlan?.priceId,
                });
              }
            }}
          >
            {upgradeSubscription.isPending || createCheckoutSession.isPending
              ? 'Upgrading...'
              : 'Upgrade my plan'}
          </Button>
        </div>
        <div
          className="flex justify-center text-sm text-gray-500 cursor-pointer hover:text-primary"
          onClick={() => {
            onOpenChange(false);
            router.push('/dashboard/setting-pricing');
          }}
        >
          Compare all features
        </div>
      </DialogContent>
    </Dialog>
  );
}
